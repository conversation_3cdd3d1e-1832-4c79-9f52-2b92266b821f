{"version": "independent", "npmClient": "yarn", "useWorkspaces": true, "command": {"publish": {"message": "ci(publish): lerna publish %s", "registry": "https://nexus01.authine.cn/repository/npm-cloudpivot/"}, "version": {"exact": true, "includeMergedTags": true, "allowBranch": ["master", "develop", "6.11.x", "6.12.x", "6.13.x", "6.14.x", "6.15.x", "lerna-publish-branch"], "push": false, "changelog": false, "ignoreChanges": ["build/", "docs/", "*.md", "**/__tests__/**", "**/test/unit/**/*.spec.(js|jsx|ts|tsx)", "**/__tests__/*.(js|jsx|ts|tsx)", "**/*.md", "**/dist", ".gitlab/", "test/", "lerna.json"], "message": "ci(publish): lerna version %s"}}, "packages": ["packages/*"]}