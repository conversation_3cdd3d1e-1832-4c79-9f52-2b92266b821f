/**
 * 获取列表参数
 * @param {string} schemaCode 
 * @param {object} options 
 * @returns {object}
*/
export const getListParams = (schemaCode, options = {}) => {
    const { page = 0, size = 50, queryCondition = [], showTotal = true } = options;
    return {
        filters: [],
        mobile: false,
        page,
        queryCode: schemaCode,
        schemaCode: schemaCode,
        size,
        queryVersion: 1,
        queryCondition: [[queryCondition]],
        showTotal,
    };
};

/**
 * 加载表单参数
 * @param {string} schemaCode 
 * @param {object} options 
 * @returns {object}
*/
export const loadFormParams = (schemaCode, options = {}) => {
    const { objectId, isWorkFlow = false, isMobile = false } = options;
    return {
        sheetCode: schemaCode,
        objectId,
        schemaCode: schemaCode,
        isWorkFlow,
        return: Date.now(),
        _viewCode: schemaCode,
        relevanceInfo: JSON.stringify({ "_viewCode": schemaCode }),
        isMobile,
    };
};


/**
 * 获取服务测试参数
 * @param {string} code 
 * @param {string} serviceCode 
 * @param {object} testInputParametersMap 
 * @returns {object}
*/
export const getServiceTestParams = (code, serviceCode, testInputParametersMap = {}) => {
    return {
        code,
        serviceCode,
        testInputParametersMap,
    };
};

/**
 * 获取formurl参数
 * @param {string} bizObjectId 
 * @param {string} schemaCode 
 * @param {string} formCode 
 * @returns {object}
*/
export const getFormUrlParams = (bizObjectId, schemaCode, formCode) => {
    return {
        bizObjectId,
        schemaCode,
        formCode,
    };
};