/**
 * 导入必要的库
 * @summary 导入用于处理Excel文件和保存文件的外部库
 */
import * as XLSX from 'xlsx'; // 用于处理Excel文件的库
import * as saveAs from 'file-saver'; // 用于保存文件的库
import * as XLSXStyle from 'sheetjs-style'
import lodash from 'lodash'; // 用于实用的工具函数

export const exportExcel = (options) => {
    console.log("🚀 ~ exportExcel ~ options:", options)
    let worksheet = '';
    if (lodash.isArray(options.data)) {
        // 将数据转换为工作表
        worksheet = XLSX.utils.json_to_sheet(options.data);
    } else
        worksheet = XLSX.utils.table_to_sheet(options.data);

    // 创建工作簿并添加工作表
    const workbook = XLSX.utils.book_new();

    if (options.columnsWidth)
        worksheet['!cols'] = new Array(options.columns).fill({ wpx: options.columnsWidth });


    /**
     * 处理Excel表格数据，根据单元格的值来设置单元格的背景色。
     * @param {Object} worksheet - Excel工作表对象。
     * @param {function} callback - 回调函数，用于处理每个单元格的值。
     * callback接收三个参数：行索引row、列索引col和单元格值value。
     * 根据单元格的值设置不同的背景色。
     */
    if (!lodash.isEmpty(options.style))
        processExcelData(worksheet, (row, col, value) => {
            console.log("🚀 ~ processExcelData ~ col:", col)
            if (options.style.type == 'number')
                value.z = '###,##0.00';
            // 根据单元格的值设置背景色
            if (options.style.s) {
                if (value.v == '租出小计' || value.v == '租入小计')
                    setRowStyle(worksheet, row - 1, {
                        fgColor: { rgb: 'faeadb' },
                    });
                if (value.v == '房屋租出' || value.v == '房屋租入')
                    setRowStyle(worksheet, row - 1, {
                        fgColor: { rgb: 'e3dfeb' },
                    });
                if (value.v == '设备租出' || value.v == '设备租入')
                    setRowStyle(worksheet, row - 1, {
                        fgColor: { rgb: 'e3dfeb' },
                    });
                if (value.v == '年份' && options.style.table == 'zbjh-two') {
                    setColumnStyle(worksheet, col - 1, '', {
                        vertical: 'center', // 设置垂直居中对齐
                        horizontal: 'center' // 添加水平居中
                    });
                }
            }
        }, options);

    XLSX.utils.book_append_sheet(workbook, worksheet, options.filename);

    // 生成Excel的配置
    XLSXStyle.writeFile(workbook, options.fileName + ".xlsx");
}


// 将字符串转换为ArrayBuffer
function s2ab(s) {
    const buf = new ArrayBuffer(s.length);
    const view = new Uint8Array(buf);
    for (let i = 0; i !== s.length; ++i) view[i] = s.charCodeAt(i) & 0xFF;
    return buf;
}

/**
 * 处理Excel数据，对匹配特定模式的单元格执行操作
 * @param jsonData 包含Excel单元格数据的JSON对象。键名应遵循Excel的列字母+行号格式（如A1, B2等）。
 */
function processExcelData(jsonData, callback, options) {
    let index = 0
    // 遍历jsonData中的每个键，查找匹配列字母和行号格式的键
    for (const key in jsonData) {
        if (jsonData.hasOwnProperty(key)) {
            const match = key.match(/^([A-Z]+)(\d+)$/); // 使用正则表达式匹配键名，以验证其格式
            if (match) {
                const col = parseInt(match[2], 10); // 提取列字母
                const row = parseInt(match[2], 10); // 提取行号，并转换为整数
                console.log("🚀 ~ processExcelData ~ col:", parseInt(match[2], 10))
                // 打印单元格信息
                console.log(`行号: ${row}, 列: ${col}, 值:`, jsonData[key]);
                callback && callback(row, col, jsonData[key], options);
            }
        }
    }
}

/**
 * 设置指定行的背景颜色
 * @param {Object} ws 工作表对象，来自XLSX库，代表一个Excel表格的工作表
 * @param {number} rowIndex 要设置背景色的行索引
 * @param {Object} fill 
 * @param {alignment} alignment 
 */
function setRowStyle(ws, rowIndex, fill, alignment, options) {
    // 解码工作表的范围，以确定哪些单元格已被占用
    const range = XLSX.utils.decode_range(ws['!ref']);
    // 遍历所有列，从范围的开始列到结束列
    for (let col = range.s.c; col <= range.e.c; col++) {
        // 为当前单元格生成一个引用对象
        const cellRef = { r: rowIndex, c: col };
        // 将单元格引用对象转换为Excel中使用的单元格地址字符串
        const address = XLSX.utils.encode_cell(cellRef);
        // 确保目标单元格在工作表中存在，如果不存在则初始化
        if (!ws[address]) ws[address] = { t: 'z' };
        // 设置单元格的样式，此处为背景色
        const params = {};
        if (fill)
            params.fill = fill;
        if (alignment)
            params.alignment = alignment
        ws[address].s = params;
    }
}

function setColumnStyle(ws, columnIndex, fill, alignment) {
    // 确保工作表有效
    if (!ws) return;

    // 遍历工作表的有效行范围
    const range = XLSX.utils.decode_range(ws['!ref']);
    for (let rowIndex = range.s.r + 1; rowIndex <= range.e.r; rowIndex++) {
        // 为当前单元格生成一个引用对象
        const cellRef = { r: rowIndex, c: columnIndex };
        // 将单元格引用对象转换为Excel中使用的单元格地址字符串
        const address = XLSX.utils.encode_cell(cellRef);
        // 确保目标单元格在工作表中存在，如果不存在则初始化
        if (!ws[address]) ws[address] = { t: 'z' };
        const params = {};
        if (fill)
            params.fill = fill;
        if (alignment)
            params.alignment = alignment
        ws[address].s = params;
    }
}