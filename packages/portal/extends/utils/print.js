export default function printHtml(elementId, title) {
    let style = getPrintStyle();
    let printPart = window.document.getElementById(elementId);
    let container = getContainer(printPart.innerHTML);
    let printHeader = window.document.getElementById("printHeader");
    console.log(title)
    if (title) {
        printHeader.style.display = "block";
    }
    console.log(printHeader)

    window.document.body.appendChild(style);
    window.document.body.appendChild(container);
    getLoadPromise(container).then(() => {
        window.print();
        printHeader.style.display = "none";
        window.document.body.removeChild(style);
        window.document.body.removeChild(container);
    });

}

// 设置打印样式
function getPrintStyle() {
    let styleContent = `
        #print-container { display: none; }
        @media print {
            body > :not(#print-container) {
                display: none;
            }
            html,
            body {
                display: block !important;
                -webkit-print-color-adjust: exact; /* Chrome, Safari */
                color-adjust: exact; /* Firefox */
            }

            #print-container {
                display: block;
                // transform: scale(0.98);
                padding: 4mm 5mm;
                box-sizing: border-box;
            }

            #printHeader {
                font-size: 16px;
                font-weight: 600;
                color: #333;
                margin-bottom: 6px;
                margin-left: 12px;
                margin-right: 12px;
                text-align: center;
                display: flex !important;
                justify-content: center;
                position: relative;
            }

            #projectPrintTable {
                border: 1px solid #333333;
            }

            #printHeader > div {
                display: flex;
                justify-content: center;
            }

            #projectPrintTableList .czxj_crxj {
                background-color: #faeadb;
            }

            #projectPrintTableList .fwcz_fwzr {
                background-color: #e3dfeb;
            }

            #summaryPrintTableList [key="lb"] {
                background-color: #fcfc47;;
            }

            #summaryPrintTableList [key="ssdw"] {
                background-color: #fcfc47;;
            }

            #summaryPrintTableList [key="jcdw"] {
                background-color: #fcfc47;;
            }

            @page {
                size: A4;
            }
        }
    `;
    let style = document.createElement("style");
    style.innerHTML = styleContent;
    return style;
}

// 清空打印内容
function cleanPrint() {
    let div = document.getElementById('print-container')
    if (!!div) {
        document.querySelector('body').removeChild(div)
    }
}

// 新建DOM，将需要打印的内容填充到DOM
function getContainer(html) {
    cleanPrint()
    let container = document.createElement("div");
    container.setAttribute("id", "print-container");
    container.innerHTML = html;
    return container;
}

// 图片完全加载后再调用打印方法
function getLoadPromise(dom) {
    let imgs = dom.querySelectorAll("img");
    imgs = [].slice.call(imgs);

    if (imgs.length === 0) {
        return Promise.resolve();
    }

    let finishedCount = 0;
    return new Promise(resolve => {
        function check() {
            finishedCount++;
            if (finishedCount === imgs.length) {
                resolve();
            }
        }
        imgs.forEach(img => {
            img.addEventListener("load", check);
            img.addEventListener("error", check);
        })
    });
}