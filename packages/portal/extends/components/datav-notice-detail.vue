<template>
  <div class="datav-notice-detail">
    <div class="notice-container">
      <!-- 标题区域 -->
      <h1 class="notice-title">{{ noticeData.bt }}</h1>

      <!-- 元信息表格 -->
      <div class="notice-meta">
        <table class="meta-table">
          <tr>
            <td class="label">收发文编号</td>
            <td class="value">{{ noticeData.wjdj }}</td>
          </tr>
          <tr>
            <td class="label">收发文机构</td>
            <td class="value">{{ noticeData.sfwjg }}</td>
          </tr>
          <tr>
            <td class="label">文件类别</td>
            <td class="value">{{ noticeData.wjlb }}</td>
          </tr>
          <tr>
            <td class="label">标题</td>
            <td class="value">{{ noticeData.bt }}</td>
          </tr>
          <tr>
            <td class="label">日期</td>
            <td class="value">{{ noticeData.rq }}</td>
          </tr>
        </table>
      </div>

      <!-- 内容区域 -->
      <div class="notice-content">
        <div class="content-header">内容</div>
        <div class="content-body">
          <div v-html="noticeData.nr"></div>
        </div>
      </div>

      <!-- 附件区域 -->
      <div
        class="notice-attachments"
        v-if="noticeData.fj && noticeData.fj.length"
      >
        <div class="attachments-header">附件</div>
        <div class="attachments-list">
          <div
            v-for="(attachment, index) in noticeData.fj"
            :key="index"
            class="attachment-item"
          >
            <i class="attachment-icon"></i>
            <span class="attachment-name">{{ attachment.name }}</span>
            <span class="attachment-size">({{ attachment.fileSize }})</span>
            <span class="attachment-uploader">{{
              attachment.creater.name
            }}</span>
            <span class="attachment-date">{{ attachment.createdTime }}</span>
            <i
              class="attachment-download"
              @click="handleDownload(attachment)"
            ></i>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import lyApi from '../ly-api';
@Component({
  name: 'DatavNoticeDetail',
})
export default class DatavNoticeDetail extends Vue {
  @Prop({ default: () => ({}) }) private noticeData!: {};

  created() {}

  async handleDownload(attachment: any) {
    const params = {
      refId: attachment.refId,
    };
    await lyApi.downloadFile(params);
    // console.log("🚀 ~ DatavNoticeDetail ~ handleDownload ~ attachment:", attachment)
  }
}
</script>

<style lang="less" scoped>
.datav-notice-detail {
  width: 100%;
  height: 100%;
  color: #fff;
  font-family: 'Microsoft YaHei', sans-serif;
  padding: 20px;
  box-sizing: border-box;
  background: rgba(0, 18, 40, 0.8);
  overflow: auto;

  .notice-container {
    max-width: 1800px;
    margin: 0 auto;
    padding: 30px;
    border-radius: 8px;
    background: rgba(1, 29, 69, 0.6);
    box-shadow: 0 0 20px rgba(0, 149, 255, 0.15);
    border: 1px solid rgba(0, 149, 255, 0.3);
  }

  .notice-title {
    text-align: center;
    font-size: 36px;
    font-weight: bold;
    margin-bottom: 40px;
    color: #10a5ff;
    text-shadow: 0 0 10px rgba(16, 165, 255, 0.5);
    letter-spacing: 2px;
  }

  .notice-meta {
    margin-bottom: 40px;

    .meta-table {
      width: 100%;
      border-collapse: collapse;
      border: 1px solid rgba(0, 149, 255, 0.3);

      tr {
        border-bottom: 1px solid rgba(0, 149, 255, 0.3);

        &:nth-child(2n) {
          background-color: rgba(0, 149, 255, 0.05);
        }
      }

      td {
        padding: 15px 20px;

        &.label {
          width: 150px;
          background: rgba(0, 149, 255, 0.1);
          font-weight: bold;
          color: #66ccff;
          border-right: 1px solid rgba(0, 149, 255, 0.3);
        }

        &.value {
          color: #e0e0e0;
        }
      }
    }
  }

  .notice-content,
  .notice-attachments {
    margin-bottom: 40px;

    .content-header,
    .attachments-header {
      font-size: 22px;
      font-weight: bold;
      color: #10a5ff;
      padding-bottom: 15px;
      border-bottom: 1px solid rgba(0, 149, 255, 0.3);
      margin-bottom: 20px;
    }

    .content-body {
      font-size: 18px;
      line-height: 1.8;
      color: #e0e0e0;
      letter-spacing: 1px;
      padding: 0 10px;
      text-align: justify;
    }
  }

  .attachments-list {
    .attachment-item {
      display: flex;
      align-items: center;
      padding: 12px 10px;
      border-bottom: 1px dashed rgba(0, 149, 255, 0.2);
      transition: all 0.3s ease;

      .attachment-icon {
        width: 24px;
        height: 24px;
        background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2310a5ff"><path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8l-6-6zm-1 1v5h5v11H6V4h7z"/></svg>')
          no-repeat center;
        margin-right: 10px;
      }

      .attachment-name {
        flex: 2;
        color: #10a5ff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .attachment-size {
        flex: 0.4;
        color: #8e8e8e;
      }

      .attachment-uploader {
        flex: 0.4;
        color: #8e8e8e;
      }

      .attachment-date {
        flex: 0.8;
        color: #8e8e8e;
      }

      .attachment-download {
        width: 22px;
        height: 22px;
        background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2310a5ff"><path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/></svg>')
          no-repeat center;
        cursor: pointer;
      }
    }
  }
}

@media screen and (max-width: 1440px) {
  .datav-notice-detail {
    .notice-title {
      font-size: 30px;
    }

    .notice-content .content-body {
      font-size: 16px;
    }
  }
}
</style>
