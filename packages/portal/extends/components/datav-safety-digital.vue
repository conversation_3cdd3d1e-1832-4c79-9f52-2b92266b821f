<template>
  <div class="safety-digital-container">
    <!-- 上部分面板 -->
    <div class="digital-left-panel">
      <div class="digital-left-side">
        <div class="digital-time">{{ currentDateTime }}</div>
      </div>
      <div class="digital-right-side">
        <div class="digital-safety">
          <span class="digital-safety-label">已安全运行</span>
          <div class="digital-number-display">
            <span
              v-for="(digit, index) in safetyDays.toString().padStart(3, '0')"
              :key="index"
              class="digital-number"
              >{{ digit }}</span
            >
          </div>
          <span class="digital-safety-unit">天</span>
          <div class="digital-hours">
            <span class="digital-hours-count">{{ safetyHours }}</span>
            <span class="digital-hours-unit">小时</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 下部分面板：显示统计数据 -->
    <div class="digital-right-panel">
      <div class="digital-data-panel">
        <!-- 过滤侧边栏 -->
        <div class="filter-sidebar">
          <div
            v-for="(label, type) in filterOptions"
            :key="type"
            class="filter-item"
            :class="{ active: activeFilter === type }"
            @click="setActiveFilter(type)"
          >
            {{ label }}
          </div>
        </div>

        <!-- 数据统计区域 -->
        <div class="digital-stats">
          <!-- 表头 -->
          <div class="digital-stats-row">
            <div
              class="digital-stats-header"
              v-for="(header, index) in statsHeaders"
              :key="index"
            >
              {{ header }}
            </div>
          </div>

          <!-- 数值显示 -->
          <div class="digital-stats-values">
            <div
              class="stats-value-container"
              v-for="(item, index) in statsData"
              :key="index"
            >
              <div class="stats-value-digits">
                <span
                  class="stats-digit"
                  v-for="(digit, dIndex) in item.value
                    .toString()
                    .padStart(3, '0')"
                  :key="dIndex"
                >
                  {{ digit }}
                </span>
              </div>
              <!-- <div class="stats-trend" :class="item.trend">
                <i class="stats-trend-icon"></i>
                <span class="stats-percent">{{ item.percent }}%</span>
              </div> -->
            </div>
          </div>

          <!-- 标签显示 -->
          <div class="digital-stats-labels">
            <div
              class="stats-label"
              v-for="(item, index) in statsData"
              :key="index"
            >
              {{ item.label }}
            </div>
          </div>
        </div>
      </div>

      <!-- 添加底部数据仪表盘 -->
      <!-- <div class="digital-dashboard">
        <div class="dashboard-item" v-for="(item, index) in dashboardData" :key="index">
          <div class="dashboard-value">{{ item.value }}</div>
          <div class="dashboard-label">{{ item.label }}</div>
        </div>
      </div> -->
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';

@Component({
  name: 'DatavSafetyDigital',
})
export default class DatavSafetyDigital extends Vue {
  // 数据属性
  currentDateTime = '';
  safetyDays = 365;
  safetyHours = 8;
  activeFilter = 'year';
  timerInterval: number | null = null;

  // 过滤选项
  filterOptions = {
    year: '年',
    month: '月',
    day: '日',
    all: '全部',
  };

  // 表头数据
  statsHeaders = ['施工队伍', '入场承包商', '施工项目'];

  // 统计数据
  statsData = [
    { value: 300, label: '井下作业', trend: 'up', percent: 5.2 },
    { value: 305, label: '基建工程', trend: 'down', percent: 1.8 },
    { value: 30, label: '油气处理', trend: 'up', percent: 12.3 },
  ];

  // 仪表盘数据
  dashboardData = [
    { value: '98.6%', label: '设备完好率' },
    { value: '92.3%', label: '计划完成率' },
    { value: '100%', label: '安全检查覆盖率' },
  ];

  // 设置活动过滤器
  setActiveFilter(filter: string) {
    this.activeFilter = filter;
    // 这里可以添加根据过滤器更新数据的逻辑
    this.updateStatsData(filter);
  }

  // 更新统计数据
  updateStatsData(filter: string) {
    // 模拟根据过滤器更新数据
    if (filter === 'year') {
      this.statsData = [
        { value: 300, label: '井下作业', trend: 'up', percent: 5.2 },
        { value: 305, label: '基建工程', trend: 'down', percent: 1.8 },
        { value: 30, label: '油气处理', trend: 'up', percent: 12.3 },
      ];
    } else if (filter === 'month') {
      this.statsData = [
        { value: 128, label: '井下作业', trend: 'up', percent: 3.5 },
        { value: 145, label: '基建工程', trend: 'up', percent: 7.2 },
        { value: 12, label: '油气处理', trend: 'down', percent: 2.1 },
      ];
    } else if (filter === 'day') {
      this.statsData = [
        { value: 15, label: '井下作业', trend: 'up', percent: 6.7 },
        { value: 18, label: '基建工程', trend: 'up', percent: 8.9 },
        { value: 3, label: '油气处理', trend: 'neutral', percent: 0 },
      ];
    } else {
      this.statsData = [
        { value: 450, label: '井下作业', trend: 'up', percent: 10.5 },
        { value: 423, label: '基建工程', trend: 'up', percent: 8.3 },
        { value: 45, label: '油气处理', trend: 'up', percent: 15.7 },
      ];
    }
  }

  // 格式化日期时间
  formatDateTime() {
    const now = new Date();
    const year = now.getFullYear();
    const month = (now.getMonth() + 1).toString().padStart(2, '0');
    const day = now.getDate().toString().padStart(2, '0');
    const hours = now.getHours().toString().padStart(2, '0');
    const minutes = now.getMinutes().toString().padStart(2, '0');
    const seconds = now.getSeconds().toString().padStart(2, '0');

    this.currentDateTime = `${year}年${month}月${day}日 ${hours}:${minutes}:${seconds}`;

    // 更新安全运行小时数
    this.safetyHours = parseInt(hours);
  }

  // 生命周期钩子
  mounted() {
    this.formatDateTime();
    this.timerInterval = setInterval(() => {
      this.formatDateTime();
    }, 1000);
  }

  beforeDestroy() {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
    }
  }
}
</script>

<style lang="less" scoped>
// 主容器样式
.safety-digital-container {
  padding: 60px 30px 20px 30px;
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  position: relative;
  gap: 15px;
  animation: containerFadeIn 0.8s ease-out forwards;
}

@keyframes containerFadeIn {
  0% {
    opacity: 0;
    transform: translateY(10px);
  }
  100% {
    opacity: 1;
    transform: translateY(0);
  }
}

// 上部分面板 - 现在是水平分布的
.digital-left-panel {
  width: 100%;
  display: flex;
  flex-direction: column;
  position: relative;
  z-index: 2;
  gap: 10px;
}

.digital-left-side {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 3;
}

.digital-right-side {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  z-index: 3;
}

// 数字时间显示
.digital-time {
  font-size: 28px;
  color: #00ffff;
  font-weight: bold;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.6);
  z-index: 3;
  font-family: 'Digital-7', 'Monaco', monospace;
  letter-spacing: 2px;
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.digital-time::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    transparent,
    rgba(0, 255, 255, 0.1),
    transparent
  );
  animation: timeShimmer 3s infinite;
}

@keyframes timeShimmer {
  0% {
    left: -100%;
  }
  100% {
    left: 100%;
  }
}

// 安全运行天数
.digital-safety {
  display: flex;
  // flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 8px;
  position: relative;
  z-index: 3;
}

.digital-safety-label {
  font-size: 15px;
  color: rgba(255, 255, 255, 0.9);
  text-transform: uppercase;
  letter-spacing: 1px;
}

.digital-safety-unit {
  font-size: 15px;
  color: rgba(255, 255, 255, 0.9);
  margin-top: 3px;
}

// 数字显示
.digital-number-display {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 4px;
  perspective: 500px;
}

.digital-number {
  display: inline-block;
  width: 50px;
  height: 50px;
  line-height: 50px;
  background: linear-gradient(
    to bottom,
    rgba(0, 85, 122, 0.9),
    rgba(0, 45, 82, 0.8)
  );
  border: 1px solid rgba(0, 195, 255, 0.7);
  border-radius: 4px;
  font-family: 'Digital-7', 'Monaco', monospace;
  font-size: 32px;
  font-weight: bold;
  color: #00ffff;
  text-shadow: 0 0 10px rgba(0, 255, 255, 0.9);
  position: relative;
  overflow: hidden;
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5) inset, 0 0 15px rgba(0, 195, 255, 0.4);
  animation: digitPulse 2s infinite alternate;
  transform-style: preserve-3d;
  transform: rotateX(10deg);
  text-align: center;
}

@keyframes digitPulse {
  0% {
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.6);
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.5) inset,
      0 0 10px rgba(0, 195, 255, 0.3);
  }
  100% {
    text-shadow: 0 0 18px rgba(0, 255, 255, 1);
    box-shadow: 0 0 5px rgba(0, 0, 0, 0.5) inset,
      0 0 25px rgba(0, 195, 255, 0.7);
  }
}

.digital-number::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 50%;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.15),
    transparent
  );
}

// 下部分面板
.digital-right-panel {
  flex: 1;
  display: flex;
  flex-direction: column;
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid rgba(0, 195, 255, 0.3);
  background-color: rgba(0, 20, 40, 0.5);
  box-shadow: 0 0 15px rgba(0, 0, 0, 0.3);
  animation: fadeInRight 0.8s ease-out forwards;
  z-index: 2;
}

@keyframes fadeInRight {
  0% {
    opacity: 0;
    transform: translateX(20px);
  }
  100% {
    opacity: 1;
    transform: translateX(0);
  }
}

.digital-data-panel {
  flex: 1;
  display: flex;
  flex-direction: row;
  overflow: hidden;
}

// 过滤侧边栏
.filter-sidebar {
  width: 40px;
  display: flex;
  flex-direction: column;
  background: linear-gradient(
    to bottom,
    rgba(0, 80, 120, 0.4),
    rgba(0, 100, 150, 0.5)
  );
  border-right: 1px solid rgba(0, 195, 255, 0.3);
  box-shadow: 2px 0 5px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 2;
}

.filter-item {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 16px;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
  border-bottom: 1px solid rgba(0, 195, 255, 0.2);
  position: relative;
  overflow: hidden;
}

.filter-item:hover {
  background-color: rgba(0, 195, 255, 0.1);
  color: rgba(255, 255, 255, 0.9);
}

.filter-item.active {
  background-color: rgba(0, 195, 255, 0.2);
  color: #00ffff;
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.5);
}

.filter-item.active::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 3px;
  background-color: #00ffff;
  box-shadow: 0 0 5px rgba(0, 255, 255, 0.8);
}

.filter-item::after {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    to right,
    transparent,
    rgba(255, 255, 255, 0.1),
    transparent
  );
  transition: all 0.6s ease;
}

.filter-item:hover::after {
  left: 100%;
}

// 数据统计区域
.digital-stats {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 10px;
  font-family: 'Digital-7', 'Monaco', monospace;
}

.digital-stats-row {
  display: flex;
  justify-content: space-around;
  background: linear-gradient(
    to right,
    rgba(0, 50, 80, 0.7),
    rgba(0, 70, 110, 0.6)
  );
  padding: 8px 0;
  border-radius: 4px 4px 0 0;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  position: relative;
  z-index: 1;
}

.digital-stats-header {
  flex: 1;
  text-align: center;
  font-size: 14px;
  color: #fff;
  font-weight: bold;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.5);
  border-right: 1px solid rgba(0, 195, 255, 0.2);
  position: relative;
  overflow: hidden;
  z-index: 3;
}

.digital-stats-header::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 30px;
  height: 1px;
  background-color: rgba(0, 195, 255, 0.3);
}

.digital-stats-header:last-child {
  border-right: none;
}

// 数值显示区域
.digital-stats-values {
  display: flex;
  flex: 1;
  justify-content: space-around;
  align-items: center;
  padding: 15px 0;
  background: rgba(0, 10, 20, 0.3);
  position: relative;
  overflow: hidden;
}

.digital-stats-values::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: radial-gradient(
      circle at 20% 50%,
      rgba(0, 100, 200, 0.05),
      transparent 70%
    ),
    radial-gradient(circle at 80% 50%, rgba(0, 100, 200, 0.05), transparent 70%);
  pointer-events: none;
}

.stats-value-container {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
}

.stats-value-container::after {
  content: '';
  position: absolute;
  right: 0;
  top: 20%;
  height: 60%;
  width: 1px;
  background: rgba(0, 195, 255, 0.2);
}

.stats-value-container:last-child::after {
  display: none;
}

// 数字显示
.stats-value-digits {
  display: flex;
  gap: 3px;
  perspective: 300px;
}

.stats-digit {
  display: inline-block;
  width: 35px;
  height: 45px;
  line-height: 45px;
  text-align: center;
  background: linear-gradient(
    to bottom,
    rgba(0, 85, 122, 0.9),
    rgba(0, 45, 82, 0.8)
  );
  border: 1px solid rgba(0, 195, 255, 0.7);
  border-radius: 3px;
  font-size: 26px;
  font-weight: bold;
  color: #00ffff;
  text-shadow: 0 0 8px rgba(0, 255, 255, 0.8);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5) inset, 0 0 12px rgba(0, 195, 255, 0.3);
  position: relative;
  overflow: hidden;
  transform-style: preserve-3d;
  transform: rotateX(8deg);
  transition: all 0.3s ease;
  animation: statDigitPulse 3s infinite alternate;
  z-index: 3;
}

@keyframes statDigitPulse {
  0% {
    color: #00ffff;
    text-shadow: 0 0 5px rgba(0, 255, 255, 0.6);
  }
  100% {
    color: #7fffff;
    text-shadow: 0 0 15px rgba(0, 255, 255, 1);
  }
}

.stats-digit:hover {
  transform: rotateX(8deg) scale(1.05);
  box-shadow: 0 0 5px rgba(0, 0, 0, 0.5) inset, 0 0 15px rgba(0, 195, 255, 0.5);
}

.stats-digit::after {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 50%;
  background: linear-gradient(
    to bottom,
    rgba(255, 255, 255, 0.15),
    transparent
  );
}

// 标签显示区域
.digital-stats-labels {
  display: flex;
  justify-content: space-around;
  background: linear-gradient(
    to right,
    rgba(0, 30, 60, 0.5),
    rgba(0, 40, 70, 0.4)
  );
  border-radius: 0 0 4px 4px;
}

.stats-label {
  flex: 1;
  text-align: center;
  font-size: 13px;
  color: rgba(255, 255, 255, 0.8);
  border-right: 1px solid rgba(0, 195, 255, 0.2);
  padding: 5px 0;
  position: relative;
  overflow: hidden;
  transition: all 0.3s ease;
  z-index: 3;
}

.stats-label:hover {
  color: #fff;
  text-shadow: 0 0 5px rgba(0, 195, 255, 0.5);
}

.stats-label:last-child {
  border-right: none;
}

@font-face {
  font-family: 'Digital-7';
  src: url('../assets/fonts/digital-7-4.ttf') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}

// 小时数显示
.digital-hours {
  display: flex;
  align-items: center;
  margin-left: 15px;
  background: rgba(0, 65, 102, 0.3);
  padding: 3px 8px;
  border-radius: 3px;
  border: 1px solid rgba(0, 195, 255, 0.3);
}

.digital-hours-count {
  font-family: 'Digital-7', 'Monaco', monospace;
  font-size: 20px;
  color: #00ffff;
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.6);
  margin-right: 4px;
}

.digital-hours-unit {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
}

// 趋势指标
.stats-trend {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-top: 5px;
  font-size: 12px;
}

.stats-trend.up {
  color: #00ff7f;
}

.stats-trend.down {
  color: #ff5252;
}

.stats-trend.neutral {
  color: #ffcc00;
}

.stats-trend-icon {
  display: inline-block;
  width: 0;
  height: 0;
  margin-right: 5px;
}

.stats-trend.up .stats-trend-icon {
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-bottom: 8px solid #00ff7f;
}

.stats-trend.down .stats-trend-icon {
  border-left: 5px solid transparent;
  border-right: 5px solid transparent;
  border-top: 8px solid #ff5252;
}

.stats-trend.neutral .stats-trend-icon {
  width: 8px;
  height: 2px;
  background-color: #ffcc00;
}

.stats-percent {
  font-family: 'Digital-7', 'Monaco', monospace;
  text-shadow: 0 0 3px currentColor;
}

// 底部仪表盘
.digital-dashboard {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: 60px;
  background: linear-gradient(
    to right,
    rgba(0, 40, 70, 0.6),
    rgba(0, 60, 90, 0.5)
  );
  border-top: 1px solid rgba(0, 195, 255, 0.3);
  padding: 0 20px;
}

.dashboard-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.dashboard-value {
  font-family: 'Digital-7', 'Monaco', monospace;
  font-size: 22px;
  color: #00ffff;
  text-shadow: 0 0 5px rgba(0, 255, 255, 0.7);
  font-weight: bold;
}

.dashboard-label {
  font-size: 12px;
  color: rgba(255, 255, 255, 0.8);
  margin-top: 3px;
}
</style>
