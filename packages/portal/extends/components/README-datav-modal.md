# DataV Modal 组件

一个符合科技感主题的弹窗组件，基于 Ant Design Vue 的 Modal 组件封装，专为数据可视化场景设计。

## 特性

- 🎨 科技感设计风格，符合 DataV 主题
- 🌟 发光边框动画效果
- 🎯 支持自定义标题和底部内容
- 📱 响应式设计
- ⚡ 基于 TypeScript 开发
- 🔧 丰富的配置选项

## 基础用法

```vue
<template>
  <div>
    <a-button @click="visible = true">打开弹窗</a-button>
    
    <datav-modal
      :visible="visible"
      title="基础弹窗"
      @cancel="visible = false"
      @ok="handleOk"
    >
      <p>这是弹窗内容</p>
    </datav-modal>
  </div>
</template>

<script>
import DatavModal from './datav-modal.vue';

export default {
  components: {
    DatavModal,
  },
  data() {
    return {
      visible: false,
    };
  },
  methods: {
    handleOk() {
      console.log('确定按钮被点击');
      this.visible = false;
    },
  },
};
</script>
```

## API

### Props

| 参数 | 说明 | 类型 | 默认值 |
| --- | --- | --- | --- |
| visible | 对话框是否可见 | boolean | false |
| title | 标题 | string | '弹窗标题' |
| width | 宽度 | string \| number | '800px' |
| centered | 垂直居中展示 Modal | boolean | true |
| destroyOnClose | 关闭时销毁 Modal 里的子元素 | boolean | true |
| maskClosable | 点击蒙层是否允许关闭 | boolean | false |
| keyboard | 是否支持键盘 esc 关闭 | boolean | true |
| showFooter | 是否显示底部区域 | boolean | true |
| showCancel | 是否显示取消按钮 | boolean | true |
| showOk | 是否显示确定按钮 | boolean | true |
| cancelText | 取消按钮文字 | string | '取消' |
| okText | 确定按钮文字 | string | '确定' |
| confirmLoading | 确定按钮 loading | boolean | false |

### Events

| 事件名称 | 说明 | 回调参数 |
| --- | --- | --- |
| cancel | 点击遮罩层或右上角叉或取消按钮的回调 | function(e) |
| ok | 点击确定回调 | function(e) |

### Slots

| 名称 | 说明 |
| --- | --- |
| default | 弹窗内容 |
| title | 自定义标题 |
| footer | 自定义底部内容 |

## 高级用法

### 自定义标题

```vue
<datav-modal :visible="visible" @cancel="visible = false">
  <template slot="title">
    <span>🚀 自定义标题</span>
  </template>
  <p>弹窗内容</p>
</datav-modal>
```

### 自定义底部

```vue
<datav-modal :visible="visible" @cancel="visible = false">
  <p>弹窗内容</p>
  <template slot="footer">
    <a-button @click="visible = false">关闭</a-button>
    <a-button type="primary" @click="handleSubmit">提交</a-button>
  </template>
</datav-modal>
```

### 无底部弹窗

```vue
<datav-modal
  :visible="visible"
  :show-footer="false"
  @cancel="visible = false"
>
  <p>这是一个没有底部的弹窗</p>
</datav-modal>
```

### 大尺寸弹窗

```vue
<datav-modal
  :visible="visible"
  width="1200px"
  title="大尺寸弹窗"
  @cancel="visible = false"
>
  <div>大量内容...</div>
</datav-modal>
```

## 样式定制

组件使用了科技感的设计风格，主要特色包括：

- 深蓝色背景 (`rgba(0, 20, 40, 0.95)`)
- 青色发光边框 (`#00ffff`)
- 渐变按钮效果
- 动态边框动画
- 模糊背景效果

如需自定义样式，可以通过覆盖 CSS 类来实现：

```less
:global(.datav-modal-wrap) {
  .ant-modal-content {
    // 自定义样式
  }
}
```

## 注意事项

1. 组件依赖 `@h3/antd-vue` 的 Modal 和 Button 组件
2. 使用了 Vue Property Decorator 语法
3. 样式使用了 Less 预处理器
4. 建议在深色背景下使用以获得最佳视觉效果
