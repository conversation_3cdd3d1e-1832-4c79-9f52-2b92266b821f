<template>
  <div class="datav-modal-example">
    <h2>DataV Modal 组件示例</h2>
    
    <div class="example-buttons">
      <a-button type="primary" @click="showBasicModal">基础弹窗</a-button>
      <a-button type="primary" @click="showCustomModal">自定义弹窗</a-button>
      <a-button type="primary" @click="showLargeModal">大尺寸弹窗</a-button>
      <a-button type="primary" @click="showNoFooterModal">无底部弹窗</a-button>
    </div>

    <!-- 基础弹窗 -->
    <datav-modal
      :visible="basicVisible"
      title="基础弹窗"
      @cancel="basicVisible = false"
      @ok="handleBasicOk"
    >
      <p>这是一个基础的 DataV 风格弹窗。</p>
      <p>具有科技感的设计风格，适用于数据可视化场景。</p>
    </datav-modal>

    <!-- 自定义标题和底部弹窗 -->
    <datav-modal
      :visible="customVisible"
      width="900px"
      @cancel="customVisible = false"
    >
      <template slot="title">
        <span>🚀 自定义标题弹窗</span>
      </template>
      
      <div class="custom-content">
        <h3>数据统计</h3>
        <div class="stats-grid">
          <div class="stat-item">
            <div class="stat-value">1,234</div>
            <div class="stat-label">总用户数</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">567</div>
            <div class="stat-label">活跃用户</div>
          </div>
          <div class="stat-item">
            <div class="stat-value">89%</div>
            <div class="stat-label">系统可用性</div>
          </div>
        </div>
      </div>

      <template slot="footer">
        <a-button @click="customVisible = false">关闭</a-button>
        <a-button type="primary" @click="handleCustomOk">导出数据</a-button>
      </template>
    </datav-modal>

    <!-- 大尺寸弹窗 -->
    <datav-modal
      :visible="largeVisible"
      title="大尺寸弹窗"
      width="1200px"
      @cancel="largeVisible = false"
      @ok="largeVisible = false"
    >
      <div class="large-content">
        <p>这是一个大尺寸的弹窗，适合展示更多内容。</p>
        <div class="chart-placeholder">
          <div class="chart-title">数据图表区域</div>
          <div class="chart-content">
            这里可以放置图表组件
          </div>
        </div>
      </div>
    </datav-modal>

    <!-- 无底部弹窗 -->
    <datav-modal
      :visible="noFooterVisible"
      title="无底部弹窗"
      :show-footer="false"
      @cancel="noFooterVisible = false"
    >
      <div class="no-footer-content">
        <p>这是一个没有底部按钮的弹窗。</p>
        <p>可以通过点击右上角的关闭按钮或按 ESC 键关闭。</p>
        <div class="action-buttons">
          <a-button @click="noFooterVisible = false">手动关闭</a-button>
        </div>
      </div>
    </datav-modal>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import { Button } from '@h3/antd-vue';
import DatavModal from './datav-modal.vue';

@Component({
  name: 'DatavModalExample',
  components: {
    AButton: Button,
    DatavModal,
  },
})
export default class DatavModalExample extends Vue {
  basicVisible = false;
  customVisible = false;
  largeVisible = false;
  noFooterVisible = false;

  showBasicModal() {
    this.basicVisible = true;
  }

  showCustomModal() {
    this.customVisible = true;
  }

  showLargeModal() {
    this.largeVisible = true;
  }

  showNoFooterModal() {
    this.noFooterVisible = true;
  }

  handleBasicOk() {
    this.$message.success('确定按钮被点击');
    this.basicVisible = false;
  }

  handleCustomOk() {
    this.$message.success('数据导出成功');
    this.customVisible = false;
  }
}
</script>

<style scoped lang="less">
.datav-modal-example {
  padding: 40px;
  background: #00065b;
  min-height: 100vh;
  color: #fff;

  h2 {
    color: #fff;
    text-align: center;
    margin-bottom: 40px;
    text-shadow: 0 0 10px rgba(0, 195, 255, 0.8);
  }

  .example-buttons {
    display: flex;
    justify-content: center;
    gap: 20px;
    margin-bottom: 40px;

    .ant-btn {
      height: 40px;
      padding: 0 24px;
      background: linear-gradient(135deg, #00c3ff 0%, #0080ff 100%);
      border-color: #00c3ff;
      color: #fff;
      border-radius: 6px;
      
      &:hover {
        background: linear-gradient(135deg, #00ffff 0%, #00c3ff 100%);
        border-color: #00ffff;
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.6);
      }
    }
  }

  .custom-content {
    h3 {
      color: #fff;
      margin-bottom: 20px;
      text-shadow: 0 0 8px rgba(0, 195, 255, 0.6);
    }

    .stats-grid {
      display: grid;
      grid-template-columns: repeat(3, 1fr);
      gap: 20px;
      margin-bottom: 20px;

      .stat-item {
        text-align: center;
        padding: 20px;
        background: rgba(0, 195, 255, 0.1);
        border: 1px solid rgba(0, 195, 255, 0.3);
        border-radius: 8px;

        .stat-value {
          font-size: 32px;
          font-weight: bold;
          color: #00ffff;
          text-shadow: 0 0 10px rgba(0, 255, 255, 0.8);
          margin-bottom: 8px;
        }

        .stat-label {
          color: rgba(255, 255, 255, 0.8);
          font-size: 14px;
        }
      }
    }
  }

  .large-content {
    .chart-placeholder {
      margin-top: 20px;
      padding: 40px;
      background: rgba(0, 195, 255, 0.05);
      border: 2px dashed rgba(0, 195, 255, 0.3);
      border-radius: 8px;
      text-align: center;

      .chart-title {
        font-size: 18px;
        color: #00ffff;
        margin-bottom: 20px;
        text-shadow: 0 0 8px rgba(0, 255, 255, 0.6);
      }

      .chart-content {
        color: rgba(255, 255, 255, 0.6);
        font-size: 16px;
      }
    }
  }

  .no-footer-content {
    .action-buttons {
      margin-top: 20px;
      text-align: center;

      .ant-btn {
        background: linear-gradient(135deg, #00c3ff 0%, #0080ff 100%);
        border-color: #00c3ff;
        color: #fff;
        
        &:hover {
          background: linear-gradient(135deg, #00ffff 0%, #00c3ff 100%);
          border-color: #00ffff;
          box-shadow: 0 0 15px rgba(0, 255, 255, 0.6);
        }
      }
    }
  }
}
</style>
