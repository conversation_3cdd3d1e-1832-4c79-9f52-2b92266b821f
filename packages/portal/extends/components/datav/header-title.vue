<template>
  <div class="header-title">{{ title }}</div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component({
  name: 'HeaderTitle',
})
export default class HeaderTitle extends Vue {
  @Prop({ type: String, default: '龙丰掌上安全' }) title!: string;
}
</script>

<style lang="less" scoped>
.header-title {
  width: 100%;
  height: 8%;
  font-weight: bold;
  letter-spacing: 4px;
  font-size: 32px;
  display: flex;
  justify-content: center;
  align-items: center;
  color: transparent;
  background-size: 300% 300%;
  -webkit-background-clip: text;
  background-clip: text;
  animation: textGradient 4s infinite;
  background-image: linear-gradient(90deg, #00c3ff, #ffffff, #0099ff, #00c3ff);
}

@keyframes textGradient {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}
</style>
