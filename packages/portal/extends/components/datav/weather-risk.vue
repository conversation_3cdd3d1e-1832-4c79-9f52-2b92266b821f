<template>
  <div class="weather-risk">
    <div class="weather-risk-box">
      <div class="weather-risk-box-top" @click="handleShowWeekForecast">
        <ADatavWeatherList
          :weatherData="weatherData"
          
        />
      </div>
      <div class="weather-risk-box-bottom" v-if="weatherAlerts.length > 0">
        <ADatavRiskList :riskList="weatherAlerts" @click="handleAlertClick" />
      </div>
      <div class="weather-risk-box-bottom" v-else>
        <div class="weather-risk-box-bottom-item">
          <div class="weather-risk-box-bottom-item-title">
            暂无预警
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import DatavRiskList from './risk-list.vue';
import DatavWeatherList from './weather-list.vue';

@Component({
  name: 'DatavWeatherRisk',
  components: {
    ADatavRiskList: DatavRiskList,
    ADatavWeatherList: DatavWeatherList,
  },
})
export default class DatavWeatherRisk extends Vue {
  @Prop({
    type: Object,
    default: () => {},
  })
  weatherData!: any;

  @Prop({
    type: Array,
    default: () => [],
  })
  weatherAlerts!: any[];

  handleAlertClick(alert: any) {
    this.$emit('alertClick', alert);
  }

  handleShowWeekForecast(data: any) {
    this.$emit('weekForecastClick', data);
  }
}
</script>

<style lang="less" scoped>
.weather-risk {
  width: 100%;
  height: 100%;
  padding: 6.5% 3% 2.5% 3%;
  box-sizing: border-box;

  .weather-risk-box {
    width: 100%;
    height: 100%;
    overflow: auto;
    background-color: rgba(1, 202, 217, 0.1);
    border-radius: 0;
    padding: 2% 2% 0 2%;
    box-sizing: border-box;
    display: flex;
    flex-direction: column;
    clip-path: polygon(
      10px 0,
      calc(100% - 10px) 0,
      100% 10px,
      100% calc(100% - 10px),
      calc(100% - 10px) 100%,
      10px 100%,
      0 calc(100% - 10px),
      0 10px
    );
    gap: 5%;

    &-top {
      height: calc(62% - 2.5%);
    }

    &-bottom {
      height: calc(38% - 2.5%);
    }
  }
  .weather-risk-box-bottom-item {
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }
  .weather-risk-box-bottom-item-title {
    font-size: 16px;
    color: #fff;
  }
}
</style>
