<template>
  <div class="card-flipping-device" :style="cardStyles">
    <div class="card-title" :style="cardTitleStyle">{{ title }}</div>
    <div class="card-number" :style="cardNumberStyle">{{ number }}</div>
    <!-- <div class="card-comparison" :style="cardComparisonStyle">
      <span>环比</span>
      <i class="comparison-arrow" :class="{ up: isUp, down: !isUp }"></i>
      <span>{{ percentage }}%</span>
    </div> -->
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
@Component({
  name: 'CardFlippingDevice',
})
export default class CardFlippingDevice extends Vue {
  @Prop({ default: '今日舆情总数' }) title!: string;
  @Prop({ default: '54' }) number!: string;
  @Prop({ default: 2 }) percentage!: number;
  @Prop({ default: true }) isUp!: boolean;
  @Prop({ default: '' }) cardStyles!: string;
  @Prop({ default: '' }) cardTitleStyle!: string;
  @Prop({ default: '' }) cardNumberStyle!: string;
  @Prop({ default: '' }) cardComparisonStyle!: string;
}
</script>
<style lang="less" scoped>
@font-face {
  font-family: 'Digital-7';
  src: url('../../assets/fonts/DS-DIGIT.TTF') format('truetype');
  font-weight: normal;
  font-style: normal;
  font-display: swap;
}
.card-flipping-device {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  border-radius: 4px;

  .card-title {
    width: 100%;
    font-size: 12px;
    font-weight: 500;
    margin-bottom: 10%;
    text-align: center;
    color: #ffffff;
  }

  .card-number {
    font-size: 24px;
    font-weight: bold;
    margin-bottom: 10%;
    line-height: 1;
    color: #e56edf;
    font-family: 'Digital-7';
  }

  .card-comparison {
    display: flex;
    align-items: center;
    font-size: 12px; 
    color: #789ce0;
    width: 100%;
    justify-content: center;

    .comparison-arrow {
      display: inline-block;
      width: 0;
      height: 0;
      margin: 0 2%;

      &.up {
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-bottom: 6px solid #ff4d4f;
      }

      &.down {
        border-left: 4px solid transparent;
        border-right: 4px solid transparent;
        border-top: 6px solid #52c41a;
      }
    }
  }
}
</style>
