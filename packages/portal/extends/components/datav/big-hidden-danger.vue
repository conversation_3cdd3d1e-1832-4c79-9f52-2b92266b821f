<template>
  <div class="notice-list-container">
    <vue-seamless-scroll
      ref="seamlessBigHiddenDangerScrollRef"
      :class-option="options"
      class="notice-list-seamless-scroll"
      :data="list"
      v-if="list.length > 0"
    >
      <div class="notice-list">
        <div class="notice-list-item" v-for="item in list" :key="item.id" @click="handleClick(item)">
          <div class="notice-list-item-content">
            <!-- <div class="notice-list-item-type" :class="getTypeClass(item.type)">
              {{ item.type }}
            </div> -->
            <div class="notice-list-item-title">
              <span class="notice-list-item-title-text">
                {{ item.dw }}
              </span>
            </div>
            <div class="notice-list-item-time">
              {{ item.time }}
            </div>
          </div>
        </div>
      </div>
    </vue-seamless-scroll>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import vueSeamlessScroll from 'vue-seamless-scroll';
@Component({
  name: 'NoticeList',
  components: {
    vueSeamlessScroll,
  },
})
export default class NoticeList extends Vue {
  @Prop({ type: Array, default: () => [] }) list!: any[];

  options = {
    step: 0.5,
    singleHeight: 44,
    waitTime: 3000,
    isWaitTime: true,
    direction: 1,
    wheelStep: 8,
    limitMoveNum: 4,
  };

  getTypeClass(type: string): string {
    // 根据不同类型返回不同的样式类名
    const noticeTypes = ['安全生产月', '健康', 'HSE监督', '通知', '安全'];
    const announcementTypes = ['防灾减灾周', '培训', '公告', '会议'];
    const urgentTypes = ['风险', '质量', '紧急', '警告', '事故'];

    if (noticeTypes.includes(type)) {
      return 'type-notice';
    } else if (announcementTypes.includes(type)) {
      return 'type-announcement';
    } else if (urgentTypes.includes(type)) {
      return 'type-urgent';
    } else {
      return 'type-default';
    }
  }

  handleClick(item: any) {
    this.$emit('click', item);
  }
}
</script>
<style lang="less" scoped>
.notice-list-container {
  width: 100%;
  height: 100%;
  padding: 6.5% 2.5% 2% 2.5%;
  box-sizing: border-box;

  .notice-list-seamless-scroll {
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: rgba(1, 202, 217, 0.1);
    border-radius: 0;
    // 使用clip-path创建斜角效果
    clip-path: polygon(
      10px 0,
      calc(100% - 10px) 0,
      /* 右上角斜角 */ 100% 10px,
      /* 右上角斜角 */ 100% calc(100% - 10px),
      /* 右下角斜角 */ calc(100% - 10px) 100%,
      /* 右下角斜角 */ 10px 100%,
      /* 左下角斜角 */ 0 calc(100% - 10px),
      /* 左下角斜角 */ 0 10px /* 左上角斜角 */
    );
  }

  .notice-list {
    width: 100%;
    height: 100%;

    &-item {
      padding: 0 15px;
      height: 44px;
      border-bottom: 1px solid rgba(1, 202, 217, 0.2);
      line-height: 44px;
      width: 100%;
      box-sizing: border-box;
      cursor: pointer;

      // &:last-child {
      //   border-bottom: none;
      // }

      &-content {
        display: flex;
        align-items: center;
      }

      &-type {
        padding: 0 6px;
        border-radius: 4px;
        font-size: 12px;
        margin-right: 10px;
        min-width: 40px;
        text-align: center;
        height: 26px;
        line-height: 26px;

        &.type-notice {
          background-color: rgba(24, 144, 255, 0.2);
          color: #1890ff;
        }

        &.type-announcement {
          background-color: rgba(82, 196, 26, 0.2);
          color: #52c41a;
        }

        &.type-urgent {
          background-color: rgba(245, 34, 45, 0.2);
          color: #f5222d;
        }

        &.type-default {
          background-color: rgba(250, 173, 20, 0.2);
          color: #faad14;
        }
      }

      &-title {
        flex: 1;
        overflow: hidden;
        display: flex;
        align-items: center;
        justify-content: center;

        &-text {
          display: inline-block;
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
          color: #fff;
          font-size: 14px;
          width: 100%;
          max-width: 100%;
        }
      }

      &-time {
        font-size: 12px;
        color: rgba(255, 255, 255, 0.65);
        margin-left: 10px;
      }
    }
  }
}
</style>
