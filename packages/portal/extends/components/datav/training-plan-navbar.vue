<template>
  <div class="training-plan-navbar">
    <div class="training-plan-navbar-list">
      <div
        v-for="(item, index) in navItems"
        :key="index"
        class="nav-item"
        :class="{ active: activeIndex === index }"
        @click="handleNavClick(index)"
      >
        <div class="nav-icon" :style="{
          '--item-color': item.color,
          '--bg-image': `url('${item.icon}')`
        }"></div>
        <span class="nav-text">{{ item.name }}</span>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
@Component({
  name: 'TrainingPlanNavbar',
})
export default class TrainingPlanNavbar extends Vue {
  private navItems = [
    {
      name: '培训计划',
      icon: 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-5 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z"/></svg>',
      color: '#2970FF'
    },
    {
      name: '课程管理',
      icon: 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M12 3L1 9l4 2.18v6L12 21l7-3.82v-6l2-1.09V17h2V9L12 3zm6.82 6L12 12.72 5.18 9 12 5.28 18.82 9zM17 15.99l-5 2.73-5-2.73v-3.72L12 15l5-2.73v3.72z"/></svg>',
      color: '#01CAD9'
    },
    {
      name: '模拟考试',
      icon: 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z"/></svg>',
      color: '#FAAD14'
    },
    {
      name: '学习资料',
      icon: 'data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor"><path d="M10 4H4c-1.11 0-2 .89-2 2v12c0 1.11.89 2 2 2h16c1.11 0 2-.89 2-2V8c0-1.11-.89-2-2-2h-8l-2-2z"/></svg>',
      color: '#00C293'
    }
  ];

  private activeIndex = 0;

  private handleNavClick(index: number): void {
    this.activeIndex = index;
    this.$emit('nav-change', index, this.navItems[index]);
  }
}
</script>

<style lang="less" scoped>
.training-plan-navbar {
  width: 100%;
  height: 100%;
padding: 6.5% 3% 2.5% 3%;  
  box-sizing: border-box;

  .training-plan-navbar-list {
    width: 100%;
    height: 100%;
       background-color: rgba(1, 202, 217, 0.1);
    border-radius: 0;
    // 使用clip-path创建斜角效果
    clip-path: polygon(
      10px 0,
      calc(100% - 10px) 0,
      /* 右上角斜角 */ 100% 10px,
      /* 右上角斜角 */ 100% calc(100% - 10px),
      /* 右下角斜角 */ calc(100% - 10px) 100%,
      /* 右下角斜角 */ 10px 100%,
      /* 左下角斜角 */ 0 calc(100% - 10px),
      /* 左下角斜角 */ 0 10px /* 左上角斜角 */
    );
    border-radius: 8px;
    position: relative;
    padding: 12px 16px;
    box-sizing: border-box;
    display: flex;
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    gap: 12px;
    backdrop-filter: blur(5px);

    // 顶部装饰线
    &::before {
      content: '';
      position: absolute;
      top: 0;
      left: 20%;
      right: 20%;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(1, 202, 217, 0.4), transparent);
      border-radius: 1px;
    }

    // 底部装饰线
    &::after {
      content: '';
      position: absolute;
      bottom: 0;
      left: 30%;
      right: 30%;
      height: 1px;
      background: linear-gradient(90deg, transparent, rgba(1, 202, 217, 0.2), transparent);
      border-radius: 1px;
    }

    .nav-item {
      flex: 1;
      height: 100%;
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      position: relative;
      transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
      padding: 10px 6px;
      box-sizing: border-box;

      .nav-icon {
        width: 50px;
        height: 50px;
        margin-bottom: 12px;
        position: relative;
        transition: all 0.3s ease;

        // 圆形背景
        background: linear-gradient(135deg,
          rgba(255, 255, 255, 0.1) 0%,
          rgba(255, 255, 255, 0.05) 100%
        );
        border: 2px solid rgba(255, 255, 255, 0.2);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;

        // 内部图标
        &::after {
          content: '';
          width: 24px;
          height: 24px;
          background-image: var(--bg-image);
          background-size: contain;
          background-repeat: no-repeat;
          background-position: center;
          filter: brightness(0) invert(1);
          transition: all 0.3s ease;
        }

        // 外圈发光效果
        &::before {
          content: '';
          position: absolute;
          top: -4px;
          left: -4px;
          right: -4px;
          bottom: -4px;
          background: conic-gradient(
            from 0deg,
            var(--item-color, #01CAD9) 0%,
            transparent 50%,
            var(--item-color, #01CAD9) 100%
          );
          border-radius: 50%;
          opacity: 0;
          transition: all 0.3s ease;
          z-index: -1;
          animation: rotate 3s linear infinite;
        }
      }

      .nav-text {
        font-size: 12px;
        font-weight: 500;
        color: rgba(255, 255, 255, 0.8);
        text-align: center;
        letter-spacing: 0.5px;
        transition: all 0.3s ease;
        line-height: 1.2;
        white-space: nowrap;
        text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
      }

      &:hover {
        transform: translateY(-3px);

        .nav-icon {
          transform: scale(1.05);
          border-color: var(--item-color, rgba(1, 202, 217, 0.6));
          background: linear-gradient(135deg,
            var(--item-color, rgba(1, 202, 217, 0.2)) 0%,
            rgba(255, 255, 255, 0.1) 100%
          );
          box-shadow: 0 4px 15px var(--item-color, rgba(1, 202, 217, 0.3));

          &::before {
            opacity: 0.6;
          }

          &::after {
            filter: brightness(0) invert(1) drop-shadow(0 0 4px currentColor);
            color: var(--item-color, #01CAD9);
          }
        }

        .nav-text {
          color: #ffffff;
          text-shadow: 0 0 8px var(--item-color, rgba(1, 202, 217, 0.6));
        }
      }

      &.active {
        transform: translateY(-5px);

        .nav-icon {
          transform: scale(1.1);
          border-color: var(--item-color, #01CAD9);
          background: linear-gradient(135deg,
            var(--item-color, rgba(1, 202, 217, 0.4)) 0%,
            rgba(255, 255, 255, 0.2) 100%
          );
          box-shadow:
            0 6px 20px var(--item-color, rgba(1, 202, 217, 0.4)),
            inset 0 1px 0 rgba(255, 255, 255, 0.3);

          &::before {
            opacity: 1;
            animation: rotate 2s linear infinite;
          }

          &::after {
            filter: brightness(0) invert(1) drop-shadow(0 0 6px currentColor);
            color: var(--item-color, #01CAD9);
            animation: iconPulse 2s infinite;
          }
        }

        .nav-text {
          color: #ffffff;
          font-weight: 600;
          text-shadow: 0 0 15px var(--item-color, rgba(1, 202, 217, 1));
          transform: translateY(-1px);
        }
      }

      // 点击效果
      &:active {
        transform: translateY(-1px) scale(0.98);
      }
    }
  }

  // 动画定义
  @keyframes rotate {
    from {
      transform: rotate(0deg);
    }
    to {
      transform: rotate(360deg);
    }
  }

  @keyframes iconPulse {
    0%, 100% {
      transform: scale(1);
    }
    50% {
      transform: scale(1.1);
    }
  }

  // 响应式设计
  @media (max-width: 1200px) {
    .training-plan-navbar-list {
      gap: 10px;
      padding: 10px 12px;

      .nav-item {
        padding: 8px 4px;

        .nav-icon {
          width: 45px;
          height: 45px;
          margin-bottom: 6px;

          &::after {
            width: 20px;
            height: 20px;
          }
        }

        .nav-text {
          font-size: 11px;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .training-plan-navbar-list {
      gap: 8px;
      padding: 8px 10px;

      .nav-item {
        padding: 6px 3px;

        .nav-icon {
          width: 40px;
          height: 40px;
          margin-bottom: 5px;

          &::after {
            width: 18px;
            height: 18px;
          }
        }

        .nav-text {
          font-size: 10px;
        }
      }
    }
  }
}
</style>
