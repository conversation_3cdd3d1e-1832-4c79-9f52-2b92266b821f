<template>
  <div class="hse-inspect-plan">
    <div class="hse-inspect-plan-item">
      <div class="hse-inspect-plan-item-content">
        <ACardFlippingDevice
          :number="533"
          title="本周检查总数"
          :percentage="45"
          :is-up="true"
          cardNumberStyle="color: #00D084; font-size: 30px;"
          cardTitleStyle="font-size: 14px;"
          cardComparisonStyle="font-size: 16px;"
        />
      </div>
      <div class="hse-inspect-plan-item-line"></div>
      <div class="hse-inspect-plan-item-content">
        <ACardFlippingDevice
          :number="321"
          title="本周问题总数"
          :percentage="45"
          :is-up="true"
          cardNumberStyle="color: #00D084; font-size: 30px;"  
          cardTitleStyle="font-size: 14px;"
          cardComparisonStyle="font-size: 16px;"
        />
      </div>
    </div>
    <div class="hse-inspect-plan-item">
      <div class="hse-inspect-plan-item-content">
        <ACardFlippingDevice
          :number="123"
          title="本月检查总数"
          :percentage="45"
          :is-up="true"
          cardNumberStyle="color: #D1AD60; font-size: 30px;"
          cardTitleStyle="font-size: 14px;"
          cardComparisonStyle="font-size: 16px;"
        />
      </div>
      <div class="hse-inspect-plan-item-line"></div>
      <div class="hse-inspect-plan-item-content">
        <ACardFlippingDevice
          :number="123"
          title="本月问题总数"
          :percentage="45"
          :is-up="true"
          cardNumberStyle="color: #D1AD60; font-size: 30px;"
          cardTitleStyle="font-size: 14px;"
          cardComparisonStyle="font-size: 16px;"
        />
      </div>
    </div>
    <!-- <div class="hse-inspect-plan-item">
      <div class="hse-inspect-plan-item-content">
      <ACardFlippingDevice
        :number="533"
        title="本季检查总数"
        :percentage="45"
        :is-up="true"
        cardNumberStyle="color: #18BFA4; font-size: 30px;"
        cardTitleStyle="font-size: 14px;"
        cardComparisonStyle="font-size: 16px;"
      />
      </div>
      <div class="hse-inspect-plan-item-line"></div>
      <div class="hse-inspect-plan-item-content">
        <ACardFlippingDevice
        :number="533"
        title="本季问题总数"
        :percentage="45"
        :is-up="true"
        cardNumberStyle="color: #18BFA4; font-size: 30px;"
        cardTitleStyle="font-size: 14px;"
        cardComparisonStyle="font-size: 16px;"
      />
      </div>
    </div>
    <div class="hse-inspect-plan-item">
      <div class="hse-inspect-plan-item-content">
      <ACardFlippingDevice
        :number="533"
        title="本年检查总数"  
        :percentage="45"
        :is-up="true"
        cardNumberStyle="color: #E56EDF; font-size: 30px;"
        cardTitleStyle="font-size: 14px;" 
        cardComparisonStyle="font-size: 16px;"
      />  
      </div>
      <div class="hse-inspect-plan-item-line"></div>
      <div class="hse-inspect-plan-item-content">
        <ACardFlippingDevice
        :number="533"
        title="本年问题总数"
        :percentage="45"
        :is-up="true"
        cardNumberStyle="color: #E56EDF; font-size: 30px;"
        cardTitleStyle="font-size: 14px;"
        cardComparisonStyle="font-size: 16px;"
      />
      </div>
    </div> -->
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import CardFlippingDevice from './card-flipping-device.vue';
@Component({
  name: 'HseInspectPlan',
  components: {
    ACardFlippingDevice: CardFlippingDevice,
  },
})
export default class HseInspectPlan extends Vue {
  private chart: echarts.ECharts | null = null;
}
</script>
<style lang="less" scoped>
.hse-inspect-plan {
  width: 100%;
  height: 100%;
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  grid-template-rows: repeat(2, 1fr);
  gap: 1%;
  padding: 10% 5% 2.5% 5%;
  box-sizing: border-box;
  &-item {
    width: 100%;
    height: 100%;
    background-color: rgba(1, 202, 217, 0.1);
    border-radius: 0;

    // 使用clip-path创建斜角效果
    clip-path: polygon(
      10px 0,
      calc(100% - 10px) 0,
      /* 右上角斜角 */ 100% 10px,
      /* 右上角斜角 */ 100% calc(100% - 10px),
      /* 右下角斜角 */ calc(100% - 10px) 100%,
      /* 右下角斜角 */ 10px 100%,
      /* 左下角斜角 */ 0 calc(100% - 10px),
      /* 左下角斜角 */ 0 10px /* 左上角斜角 */
    );
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    position: relative;
    &-content {
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      width: 100%;
      padding-top: 18px;
    }
    &-line {
      width: 100%;
      height: 1px;
      background-color: rgba(1, 202, 217, 0.5);
      position: absolute;
      top: 50%;
      left: 0;
      transform: translateY(-50%);
      transform: scale(0.8);
    }
  }
}
</style>
