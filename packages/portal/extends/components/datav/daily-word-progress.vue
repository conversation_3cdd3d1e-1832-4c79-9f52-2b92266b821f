<template>
  <div class="daily-word-progress">
    <div class="chart-container" id="dailyWordProgress"></div>
  </div>
</template>
<script lang="ts">
import { Vue, Component, Emit } from 'vue-property-decorator';
import * as echarts from 'echarts';

@Component({
  name: 'DailyWordProgress',
})
export default class DailyWordProgress extends Vue {
  private chart: echarts.ECharts | null = null;

  private initChart() {
    const chartDom = document.getElementById(
      'dailyWordProgress',
    ) as HTMLElement;
    if (!chartDom) return;
    this.chart = echarts.init(chartDom);

    // 状图显示 显示小队的每日进度
    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: function (params) {
          let result = '';
          params.forEach(function (item) {
            result += item.seriesName + ': ' + item.value + '<br/>';
          });
          return result;
        },
        textStyle: {
          color: '#fff',
          fontSize: 12,
        },
        backgroundColor: 'rgba(0,0,0,0.7)',
        borderColor: 'rgba(255,255,255,0.3)',
        borderWidth: 1,
      },
      legend: {
        top: 0,
        right: 10,
        itemWidth: 15,
        itemHeight: 10,
        textStyle: {
          color: '#fff',
          fontSize: 12,
        },
        icon: 'rect',
        data: ['2024年', '2025年'],
      },
      grid: {
        left: '2%',
        right: '4%',
        bottom: '5%',
        top: '16%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: [
          '1月',
          '2月',
          '3月',
          '4月',
          '5月',
          '6月',
          '7月',
          '8月',
          '9月',
          '10月',
          '11月',
          '12月',
        ],
        axisLine: {
          lineStyle: {
            color: 'rgba(255,255,255,.5)',
          },
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 12,
        },
      },
      yAxis: {
        type: 'value',
        axisLine: {
          show: true, // 确保显示坐标轴线
          lineStyle: {
            color: 'rgba(255,255,255,.5)',
          },
        },
        splitLine: {
          show: true,
          lineStyle: {
            color: 'rgba(255,255,255,.1)',
          },
        },
        // 显示刻度值
        axisTick: {
          show: true,
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 12,
          fontWeight: 'bold',
        },
        show: true, // 确保整个坐标轴显示
      },
      series: [
        {
          name: '2024年',
          type: 'bar',
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          data: [85, 92, 78, 95, 88, 90, 86, 94, 82, 89, 93, 87],
          barWidth: 8,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(0, 195, 255, 1)' },
              { offset: 1, color: 'rgba(0, 195, 255, 0.3)' },
            ]),
          }, 
          label: {
            show: false,
            position: 'top',
            color: '#ffffff',
          },
        },
        {
          name: '2025年',
          type: 'bar',
          smooth: true,
          symbol: 'circle',
          symbolSize: 8,
          data: [80, 88, 75, 92, 84, 87, 83, 91, 79, 86, 90, 85],
          barWidth: 8,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: 'rgba(255, 204, 0, 1)' },
              { offset: 1, color: 'rgba(255, 204, 0, 0.3)' },
            ]),
          },
          label: {
            show: false,
            position: 'top',
            color: '#ffffff',
          },
        },
      ],
    };

    this.chart.setOption(option);
    window.addEventListener('resize', this.resizeChart);
    this.$emit('resize', this.chart);
  }

  private resizeChart() {
    if (this.chart) {
      this.chart.resize();
    }
  }

  mounted() {
    this.initChart();
  }

  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  }
}
</script>
<style lang="less" scoped>
.daily-word-progress {
  width: 100%;
  height: 100%;
  padding: 6% 3% 2.5% 3%;
  box-sizing: border-box;
  .chart-container {
    width: 100%;
    height: 100%;
    background-color: rgba(1, 202, 217, 0.1);
    border-radius: 4px;
    clip-path: polygon(
      10px 0,
      calc(100% - 10px) 0,
      /* 右上角斜角 */ 100% 10px,
      /* 右上角斜角 */ 100% calc(100% - 10px),
      /* 右下角斜角 */ calc(100% - 10px) 100%,
      /* 右下角斜角 */ 10px 100%,
      /* 左下角斜角 */ 0 calc(100% - 10px),
      /* 左下角斜角 */ 0 10px /* 左上角斜角 */
    );
  }
}
</style>
}
</style>
