<template>
  <div class="weather-risk-list-container">
    <vue-seamless-scroll
      class="weather-risk-seamless-scroll"
      :data="riskList"
      :class-option="options"
    >
      <div class="weather-risk-list">
        <div
          class="weather-risk-item"
          v-for="(alert, index) in riskList"
          :key="index"
          @click="handleClick(alert)"
        >
          <div class="alert-icon" :class="getColor(alert.levelCode)"></div>
          <div class="alert-content">
            <div class="alert-content-left">
              <div class="alert-title">{{ alert.grade }}</div>
              <div class="alert-desc">{{ alert.title }}</div>
            </div>
            <div class="alert-content-right">
              <div class="alert-time">{{ alert.time }}</div>
            </div>
          </div>
        </div>
      </div>
    </vue-seamless-scroll>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import vueSeamlessScroll from 'vue-seamless-scroll';
@Component({
  name: 'DatavRiskList',
  components: {
    vueSeamlessScroll,
  },
})
export default class DatavRiskList extends Vue {
  @Prop({ type: Array, default: () => [] }) riskList!: any[];

  options = {
    step: 0.5,
    singleHeight: 34,
    waitTime: 3000,
    isWaitTime: true,
    direction: 1,
    wheelStep: 8,
    limitMoveNum: 2,
  };

  getColor(level: string) {
    console.log("🚀 ~ DatavRiskList ~ getColor ~ level:", level)
    return {
      '01': 'blue',
      '02': 'yellow',
      '03': 'orange',
      '04': 'red',
      '05': 'white',
    }[level];
  }

  handleClick(alert: any) {
    this.$emit('click', alert);
  }
}
</script>

<style lang="less" scoped>
.weather-risk-list-container {
  width: 100%;
  height: 100%;
  overflow: hidden;

  .weather-risk-seamless-scroll {
    overflow: hidden;
  }
}

.weather-risk-list {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;

  .weather-risk-item {
    display: flex;
    align-items: center;
    padding: 0 12px;
    height: 30px;
    background-color: rgba(255, 255, 255, 0.1);
    border-radius: 8px;
    margin-bottom: 4px;
    backdrop-filter: blur(5px);
    border-left: 3px solid transparent;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
    width: 100%;
    box-sizing: border-box;
    display: flex;
    align-items: center;
    cursor: pointer;
    &:hover {
      transform: translateY(-3px);
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
    }

    .alert-icon {
      width: 18px;
      height: 18px;
      border-radius: 50%;
      margin-right: 12px;
      flex-shrink: 0;
      box-shadow: 0 0 8px rgba(0, 0, 0, 0.2);

      &.yellow {
        background-color: #ffa500;
        border: 2px solid rgba(255, 255, 255, 0.3);
      }

      &.orange {
        background-color: #ff4500;
        border: 2px solid rgba(255, 255, 255, 0.3);
      }

      &.blue {
        background-color: #1e90ff;
        border: 2px solid rgba(255, 255, 255, 0.3);
      }

      &.white {
        background-color: #ffffff;
        border: 2px solid rgba(255, 255, 255, 0.3);
      }

      &.red {
        background-color: #ff0000;
        border: 2px solid rgba(255, 255, 255, 0.3);
      }
    }

    .alert-content {
      flex: 1;
      display: flex;
      align-items: center;

      .alert-content-left {
        flex: 1;
        display: flex;
        align-items: center;
      }

      .alert-content-right {
        display: flex;
        align-items: center;
        justify-content: flex-end;
      }


      .alert-title {
        font-weight: bold;
        margin-bottom: 0;
        margin-right: 10px;
        font-size: 14px;
        color: #ffffff;
        white-space: nowrap;
      }

      .alert-desc {
        font-size: 12px;
        opacity: 0.9;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.4;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      .alert-time {
        font-size: 12px;
        opacity: 0.9;
        color: rgba(255, 255, 255, 0.9);
        line-height: 1.4;
      }
    }

    &:has(.alert-icon.yellow) {
      border-left-color: #ffa500;
      background: linear-gradient(90deg, rgba(255, 165, 0, 0.1), transparent);
    }

    &:has(.alert-icon.orange) {
      border-left-color: #ff4500;
      background: linear-gradient(90deg, rgba(255, 69, 0, 0.1), transparent);
    }

    &:has(.alert-icon.blue) {
      border-left-color: #1e90ff;
      background: linear-gradient(90deg, rgba(30, 144, 255, 0.1), transparent);
    }

    &:has(.alert-icon.white) {
      border-left-color: #ffffff;
      background: linear-gradient(90deg, rgba(255, 255, 255, 0.1), transparent);
    }

    &:has(.alert-icon.red) {
      border-left-color: #ff0000;
      background: linear-gradient(90deg, rgba(255, 0, 0, 0.1), transparent);
    }
  }
}
</style>
