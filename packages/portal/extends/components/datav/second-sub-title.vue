<template>
  <div class="datav-sub-title" :style="style">
    <div class="datav-sub-title-text">{{ title }}</div>
  </div>
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { Prop } from 'vue-property-decorator';

@Component({
  name: 'DatavSubTitle',
})
export default class DatavSubTitle extends Vue {
  @Prop({ type: String, required: true }) title!: string;
  @Prop({ type: String, required: false }) style!: string;
}
</script>
<style lang="less" scoped>
.datav-sub-title {
  display: flex;
  left: 0;
  box-sizing: border-box;
  height: 10%;
  position: absolute;
  width: 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  &-text {
    font-size: 12px;
    color: #fff;
  }
}
</style>
