<template>
  <div class="warning-list-container" ref="warningListContainerRef">
    <ADatavSeamlessScroll
      :copyCallback="handleCopyCallback"
      class="warning-list-seamless-scroll"
      :data="warningList"
      :class-option="options"
      ref="warningListRef"
    >
      <div class="warning-list">
        <div
          class="warning-list-item"
          v-for="(item, index) in warningList"
          :key="item.id"
          @click="handleClick(item)"
        >
          <div class="warning-list-item-content">
            <div class="warning-list-item-icon">
              <i class="icon-warning">{{ index + 1 }}</i>
            </div>
            <div class="warning-list-item-text">
              <div class="warning-list-item-title">{{ item.title }}</div>
              <div class="warning-list-item-content">
                {{ item.content }}
                <div class="warning-list-item-time" v-if="item.time">
                  {{ item.time || new Date().toLocaleString() }}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </ADatavSeamlessScroll>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Watch } from 'vue-property-decorator';
import vueSeamlessScroll from '../../components/vue-seamless-scroll/src/components/myClass.vue';
@Component({
  name: 'WarningList',
  components: {
    ADatavSeamlessScroll: vueSeamlessScroll,
  },
})
export default class WarningList extends Vue {
  @Prop({ type: Array, default: () => [] }) warningList!: any[];

  options = {
    step: 0.5,
    singleHeight: 98,
    waitTime: 3000,
    isWaitTime: true,
    direction: 1,
    wheelStep: 8,
    limitMoveNum: 3,
  };

  /**
   * 点击事件
   * @param item
   */
  handleClick(item: any) {
    this.$emit('click', item);
  }

  /**
   * 复制回调
   * @param html
   * @returns
   */
  handleCopyCallback(html: any) {
    // 将html转换为dom对象
    const parseDom = new DOMParser().parseFromString(html, 'text/html');
    parseDom
      .querySelectorAll('.warning-list-item')
      .forEach((item: any, index: number) => {
        if (index % 2 === 0) {
          item.style.backgroundColor = 'rgba(1, 202, 217, 0.1)';
        } else {
          item.style.backgroundColor = 'rgba(0, 255, 255, 0.4)';
        }
      });
    return parseDom.body.innerHTML;
  }
}
</script>

<style lang="less" scoped>
.warning-list-container {
  width: 100%;
  height: 100%;
  padding: 10% 5% 2.5% 5%;
  box-sizing: border-box;

  .warning-list-seamless-scroll {
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: rgba(1, 202, 217, 0.1);
    border-radius: 0;
    // 使用clip-path创建斜角效果
    clip-path: polygon(
      10px 0,
      calc(100% - 10px) 0,
      /* 右上角斜角 */ 100% 10px,
      /* 右上角斜角 */ 100% calc(100% - 10px),
      /* 右下角斜角 */ calc(100% - 10px) 100%,
      /* 右下角斜角 */ 10px 100%,
      /* 左下角斜角 */ 0 calc(100% - 10px),
      /* 左下角斜角 */ 0 10px /* 左上角斜角 */
    );
  }

  .warning-list {
    &-item {
      height: 98px;
      color: #fff;
      transition: background-color 0.3s ease;
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 10px;
      box-sizing: border-box;
      position: relative;

      // &::before {
      //   content: '';
      //   position: absolute;
      //   bottom: 0;
      //   left: 50%;
      //   transform: translateX(-50%);
      //   width: 90%;
      //   height: 1px;
      //   background-color: rgba(0, 255, 255, 0.4);
      // }

      &:nth-child(odd) {
        background-color: rgba(0, 255, 255, 0.4);
      }

      &:nth-child(even) {
        background-color: rgba(1, 202, 217, 0.1);
      }

      &-content {
        display: flex;
        align-items: flex-start;
      }

      &-icon {
        flex-shrink: 0;
        width: 20px;
        height: 20px;
        margin-right: 8px;
        margin-top: 2px;
        background: linear-gradient(
          135deg,
          rgba(255, 230, 0, 0.9),
          rgba(255, 180, 0, 0.8)
        );
        border-radius: 3px;
        display: flex;
        align-items: center;
        justify-content: center;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3),
          inset 0 1px 1px rgba(255, 255, 255, 0.5);
        transform: translateZ(0);

        .icon-warning {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 14px;
          height: 14px;
          position: relative;
          color: #fff;
          font-weight: bold;
          font-size: 12px;
          text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3);
        }
      }

      &-text {
        flex: 1;
      }

      &-title {
        font-size: 14px;
        font-weight: bold;
        margin-bottom: 5px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }

      &-content {
        font-size: 12px;
        opacity: 0.9;
        position: relative;
        line-height: 24px;
      }

      &-time {
        font-size: 12px;
        color: rgba(0, 255, 255, 0.8);
        display: flex;
        align-items: center;
        justify-content: flex-end;
        position: absolute;
        right: 0;
        bottom: 0;
      }
    }
  }
}
</style>
