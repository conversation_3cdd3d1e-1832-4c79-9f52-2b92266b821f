<template>
  <div class="training-plan">
    <vue-seamless-scroll
      class="training-plan-scroll"
      :data="trainingPlanList"
      :class-option="options"
    >
      <div class="training-plan-list">
        <div class="training-plan-column">
          <div
            class="training-plan-item"
            v-for="item in trainingPlanList"
            :key="item.id"
            @click="handleItemClick(item)"
          >
            <div class="training-plan-item-content">
              <div class="training-plan-item-row">
                <span class="training-plan-item-title">{{ item.title }}</span>
                <span class="training-plan-item-time">{{ item.time }}</span>
              </div>
              <div class="training-plan-item-row training-plan-item-details">
                <div class="training-plan-item-child">
                  <div
                    v-for="(child, index) in item.child"
                    :key="child.id"
                    class="child-item"
                  >
                    <div class="child-item-content">
                      <span class="child-label">单位:</span>
                      <span class="child-value">{{ child.zbdw }}</span>
                    </div>
                    <div class="child-item-content" v-if="child.pxxmmc">
                      <span class="child-label">内容:</span>
                      <span class="child-value">{{ child.pxxmmc }}</span>
                    </div>
                    <div class="child-item-content" v-if="child.yf">
                      <span class="child-label">月份:</span>
                      <span class="child-value">{{ child.yf }}</span>
                    </div>
                    <div class="child-item-content" v-if="child.jhpxrs">
                      <span class="child-label">人数:</span>
                      <span class="child-value">{{ child.jhpxrs }}</span>
                    </div>
                    <div class="child-item-content" v-if="child.yjfy">
                      <span class="child-label">费用:</span>
                      <span class="child-value">{{ child.yjfy }}</span>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </vue-seamless-scroll>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import vueSeamlessScroll from 'vue-seamless-scroll';

@Component({
  name: 'TrainingPlan',
  components: {
    vueSeamlessScroll,
  },
})
export default class TrainingPlan extends Vue {
  @Prop({ type: Array, default: () => [] }) trainingPlanList!: any[];
  options = {
    step: 0.5,
    singleHeight: 100,
    waitTime: 3000,
    isWaitTime: true,
    direction: 1,
    wheelStep: 8,
    limitMoveNum: 3,
  };

  /**
   * 点击培训计划
   * @param item 行数据
   */
  handleItemClick(item: any) {
    this.$emit('click', item);
  }
}
</script>

<style lang="less" scoped>
.training-plan {
  width: 100%;
  height: 100%;
  padding: 6.5% 3% 2.5% 3%;
  box-sizing: border-box;

  .training-plan-scroll {
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: rgba(1, 202, 217, 0.1);
    border-radius: 0;
    // 使用clip-path创建斜角效果
    clip-path: polygon(
      10px 0,
      calc(100% - 10px) 0,
      /* 右上角斜角 */ 100% 10px,
      /* 右上角斜角 */ 100% calc(100% - 10px),
      /* 右下角斜角 */ calc(100% - 10px) 100%,
      /* 右下角斜角 */ 10px 100%,
      /* 左下角斜角 */ 0 calc(100% - 10px),
      /* 左下角斜角 */ 0 10px /* 左上角斜角 */
    );
    padding: 2.5% 0;
    box-sizing: border-box;
  }

  .training-plan-list {
    width: 100%;
    height: 100%;
    padding: 0 2.5%;
  }

  .training-plan-column {
    display: flex;
    flex-direction: column;
    height: 100%;
    width: 100%;
  }

  .training-plan-item {
    display: flex;
    align-items: center;
    width: 100%;
    margin-bottom: 10px;
    height: auto;
    min-height: 44px;
    padding: 0 15px;
    height: 90px;
    background: linear-gradient(
      90deg,
      rgba(1, 202, 217, 0.1) 0%,
      rgba(1, 202, 217, 0.2) 100%
    );
    border-radius: 8px;
    border: 1px solid rgba(1, 202, 217, 0.3);
    box-shadow: 0 0 10px rgba(1, 202, 217, 0.15);
    transition: all 0.3s ease;
    cursor: pointer;

    &:hover {
      transform: translateX(5px);
      box-shadow: 0 0 15px rgba(1, 202, 217, 0.3);
      background: linear-gradient(
        90deg,
        rgba(1, 202, 217, 0.2) 0%,
        rgba(1, 202, 217, 0.3) 100%
      );
    }

    &-icon {
      margin-right: 12px;

      .danger-icon {
        display: block;
        width: 20px;
        height: 20px;
        background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%2301CAD9"><path d="M12 2L1 21h22L12 2zm0 4l7.5 13h-15L12 6z"/><path d="M11 10h2v5h-2zm0 6h2v2h-2z"/></svg>')
          no-repeat center;
        background-size: contain;
      }
    }

    &-content {
      flex: 1;
      overflow: hidden;
    }

    &-row {
      display: flex;
      justify-content: space-between;
      align-items: center;
      width: 100%;

      &.training-plan-item-details {
        margin-top: 5px;
        flex-direction: column;
        align-items: flex-start;
      }
    }

    &-title {
      font-size: 14px;
      color: #fff;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      max-width: 70%;
    }

    &-time {
      font-size: 12px;
      color: rgba(255, 255, 255, 0.7);
      white-space: nowrap;
    }

    &-child {
      width: 100%;
      font-size: 12px;
      color: rgba(255, 255, 255, 0.85);

      .child-item {
        padding: 6px 8px;
        background: rgba(1, 202, 217, 0.15);
        border-radius: 4px;
        display: flex;
        gap: 10px;

        &:last-child {
          margin-bottom: 0;
        }

        &-content {
          display: flex;
          padding: 3px 0;
          border-bottom: 1px solid rgba(1, 202, 217, 0.1);

          &:last-child {
            margin-bottom: 0;
            border-bottom: none;
          }
        }

        .child-label {
          color: rgba(255, 255, 255, 0.6);
          font-size: 11px;
          margin-right: 8px;
        }

        .child-value {
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          color: rgba(255, 255, 255, 0.9);
          display: flex;
          justify-content: flex-start;
        }
      }
    }
  }
}
</style>
