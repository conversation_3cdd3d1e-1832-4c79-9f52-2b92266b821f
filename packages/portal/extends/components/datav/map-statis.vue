<template>
  <div class="map-statis">
    <el-amap ref="map" class="amap-demo" :mapStyle="mapStyle" :zoom="zoom">
      <el-amap-marker
        v-for="(marker, index) in markers"
        :key="index"
        :position="marker.position"
        :title="marker.name"
      >
        <div class="marker-content" :class="{ pulse: enablePulse }">
          <div class="marker-content-data" :style="{ color: marker.color }">
            <span class="marker-content-count">{{ marker.count }}</span>
            <span class="marker-content-unit">{{ marker.unit }}</span>
          </div>
          <div class="marker-icon-wrapper">
            <div
              class="marker-halo"
              :style="{ backgroundColor: marker.color }"
            ></div>
            <a-icon
              type="environment"
              theme="filled"
              class="marker-icon"
              :style="{ color: marker.color }"
            />
          </div>
        </div>
      </el-amap-marker>
    </el-amap>
    <div class="map-statis-legend">
      <div class="map-statis-legend-items">
        <div
          v-for="(item, index) in markers"
          :key="index"
          class="map-statis-legend-item"
          @click="centerMapOn(item.position)"
        >
          <div
            class="map-statis-legend-item-color"
            :style="{ backgroundColor: item.color }"
          ></div>
          <div class="map-statis-legend-item-name">{{ item.name }}</div>
        </div>
      </div>
    </div>
    <!-- 右上角加一个放大图片 -->
    <div v-if="isFullScreen" class="map-zoom-control">
      <a-icon type="fullscreen" class="zoom-icon" @click="toggleFullScreen" />
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component, Prop } from 'vue-property-decorator';
import { Icon } from '@h3/antd-vue';
import '../../../src/config/amap';
@Component({
  name: 'MapStatis',
  components: {
    AIcon: Icon,
  },
})
export default class MapStatis extends Vue {
  @Prop({ type: Boolean, default: true }) isFullScreen!: boolean;

  @Prop({ type: Number, default: 8 }) zoom!: number;

  center = [116.397428, 39.90923];

  // 蓝色的默认样式amap://styles/blue
  mapStyle = 'amap://styles/macaron';

  enablePulse = true; // 启用脉冲动画

  visible = false;

  markers = [
    {
      position: [124.889944, 46.642567],
      name: '大庆市让胡路区',
      count: 2,
      unit: '支',
      icon: 'environment',
      color: '#1890ff',
      label: {
        content: '2',
        direction: 'top',
        offset: [0, -10],
      },
    },
    {
      position: [125.135441, 46.62931],
      name: '大庆市红岗区',
      count: 1,
      unit: '支',
      icon: 'bank',
      color: '#52c41a',
      label: {
        content: '1',
        direction: 'top',
        offset: [0, -10],
      },
    },
    {
      position: [124.891041, 46.398537],
      name: '大庆市萨尔图区',
      count: 14,
      unit: '支',
      icon: 'flag',
      color: '#fa8c16',
      label: {
        content: '14',
        direction: 'top',
        offset: [0, -10],
      },
    },
    {
      position: [106.633162, 30.455947],
      name: '四川省广安市',
      count: 1,
      unit: '支',
      icon: 'pushpin',
      color: '#f5222d',
      label: {
        content: '1',
        direction: 'top',
        offset: [0, -10],
      },
    },
  ];

  centerMapOn(position: number[]) {
    const map = (this.$refs.map as any).$$getInstance();
    map.setZoomAndCenter(14, position);
  }

  toggleFullScreen() {
    console.log('toggleFullScreen');
    this.$emit('toggleFullScreen', this.markers);
  }
}
</script>

<style lang="less" scoped>
.map-statis {
  width: 100%;
  height: 100%;
  position: relative;

  .map-statis-legend {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    z-index: 1000;

    &-items {
      display: grid;
      grid-template-columns: repeat(2, 1fr);
      justify-content: center;
      align-items: center;
      background-color: rgba(255, 255, 255, 0.8);
      box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
      padding: 2px;
    }

    &-item {
      display: flex;
      align-items: center;
      cursor: pointer;
      border-radius: 4px;
      transition: all 0.3s ease;
      padding: 0 10px;

      &:hover {
        background-color: rgba(0, 0, 0, 0.05);
        transform: translateY(-2px);
      }

      &-color {
        width: 10px;
        height: 10px;
        border-radius: 50%;
        margin-right: 5px;
      }

      &-name {
        margin-right: 5px;
        font-weight: 500;
        color: #333;
      }

      &-count {
        color: #666;
        font-size: 12px;
      }
    }
  }

  :deep(.amap-logo) {
    display: none !important;
  }

  :deep(.amap-copyright) {
    display: none !important;
  }

  .amap-demo {
    width: 100%;
    height: 100%;
  }

  .marker-content {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 6px;
    border-radius: 6px;
    background: transparent;
    transform: translateY(-5px);
    transition: all 0.3s ease;

    &:hover {
      transform: translateY(-8px);
    }

    &.pulse {
      animation: markerPulse 1.5s infinite;
    }

    .marker-icon-wrapper {
      position: relative;
      display: flex;
      justify-content: center;
      align-items: center;
    }

    .marker-halo {
      position: absolute;
      width: 20 px;
      height: 28px;
      border-radius: 50%;
      opacity: 0.4;
      filter: blur(6px);
      z-index: -1;
    }

    .marker-icon {
      font-size: 20px;
      filter: drop-shadow(0 2px 5px rgba(0, 0, 0, 0.3));
    }

    .marker-content-data {
      display: flex;
      align-items: baseline;
      margin-bottom: 4px;
      background: transparent;
      padding: 2px 6px;
      border-radius: 10px;
      filter: drop-shadow(0 1px 3px rgba(0, 0, 0, 0.5));
    }

    .marker-content-count {
      font-size: 20px;
      font-weight: bold;
      margin-right: 2px;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
    }

    .marker-content-unit {
      font-size: 14px;
      text-shadow: 0 1px 3px rgba(0, 0, 0, 0.7);
    }
  }

  .map-zoom-control {
    position: absolute;
    top: 10px;
    right: 10px;
    z-index: 1000;

    .zoom-icon {
      font-size: 20px;
      background-color: rgba(255, 255, 255, 0.8);
      padding: 4px;
      border-radius: 4px;
      box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
      cursor: pointer;
      transition: all 0.3s;

      &:hover {
        background-color: white;
        transform: scale(1.1);
      }
    }
  }
}

@keyframes markerPulse {
  0% {
    transform: translateY(-5px) scale(1);
  }
  50% {
    transform: translateY(-5px) scale(1.1);
  }
  100% {
    transform: translateY(-5px) scale(1);
  }
}
</style>
