<template>
  <div class="cenerate-operation-status">
    <div class="chart-container" id="cenerateOperationStatus"></div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop, Emit } from 'vue-property-decorator';
import * as echarts from 'echarts';

@Component({
  name: 'CenerateOperationStatus',
})
export default class CenerateOperationStatus extends Vue {
  private chart: echarts.ECharts | null = null;

  private initChart() {
    const chartDom = document.getElementById(
      'cenerateOperationStatus',
    ) as HTMLElement;
    if (!chartDom) return;

    this.chart = echarts.init(chartDom);

    const option = {
      tooltip: {
        trigger: 'axis',
        axisPointer: {
          type: 'shadow',
        },
        formatter: '{b}: {c}',
        textStyle: {
          color: '#fff',
          fontSize: 12,
        },
        backgroundColor: 'rgba(0,0,0,0.7)',
        borderColor: 'rgba(255,255,255,0.3)',
        borderWidth: 1,
      },
      grid: {
        left: '2%',
        right: '4%',
        bottom: '0',
        top: '10%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        show: false,
      },
      yAxis: {
        type: 'category',
        data: ['天然气', '井下', '基建', '产品'],
        axisLine: {
          lineStyle: {
            color: 'rgba(255,255,255,.5)',
          },
        },
        splitLine: {
          lineStyle: {
            color: 'rgba(255,255,255,.1)',
          },
        },
        axisLabel: {
          color: '#ffffff',
          fontSize: 14,
          fontWeight: 'bold',
        },
      },
      series: [
        {
          type: 'bar',
          data: [
            {
              value: 25,
              itemStyle: { color: '#ADE2BB' },
            },
            {
              value: 90,
              itemStyle: { color: '#F8C0C0' },
            },
            {
              value: 70,
              itemStyle: { color: '#F8D0A0' },
            },
            {
              value: 30,
              itemStyle: { color: '#B8D8F8' },
            },
          ],
          barWidth: 20,
          showBackground: true,
          backgroundStyle: {
            color: 'rgba(220, 220, 220, 0.2)',
          },
          label: {
            show: true,
            position: 'right',
            color: '#ffffff',
            fontSize: 14,
            fontWeight: 'bold',
            formatter: '{c}',
          },
        },
      ],
    };

    this.chart.setOption(option);

    window.addEventListener('resize', this.resizeChart);
    this.$emit('resize', this.chart);
  }

   resizeChart() {
    if (this.chart) {
      this.chart.resize();
    }
  }

  mounted() {
    this.initChart();
    
  }

  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  }
}
</script>

<style lang="less" scoped>
.cenerate-operation-status {
  width: 100%;
  height: 100%;
  padding: 7% 3% 2% 3%;
  box-sizing: border-box;
  .chart-container {
    width: 100%;
    height: 100%;
    background-color: rgba(1,202,217,.1);
    border-radius: 0;
     clip-path: polygon(
      10px 0,
      calc(100% - 10px) 0,
      /* 右上角斜角 */ 100% 10px,
      /* 右上角斜角 */ 100% calc(100% - 10px),
      /* 右下角斜角 */ calc(100% - 10px) 100%,
      /* 右下角斜角 */ 10px 100%,
      /* 左下角斜角 */ 0 calc(100% - 10px),
      /* 左下角斜角 */ 0 10px /* 左上角斜角 */
    );
  }
}
</style>
