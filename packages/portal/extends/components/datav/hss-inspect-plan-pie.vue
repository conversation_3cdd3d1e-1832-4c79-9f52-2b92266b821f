<template>
  <div class="hss-inspect-plan-pie">
    <div class="hss-inspect-plan-pie-scroll">
      <div ref="hssInspectPlanPieChart" class="hss-inspect-plan-pie-chart"></div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import * as echarts from 'echarts';

@Component({
  name: 'HssInspectPlanPie',
})
export default class HssInspectPlanPie extends Vue {
  @Prop({ type: Array, default: () => [] }) hssInspectPlanPieList!: any[];

  private chart: any = null;

  mounted() {
    this.initChart();
  }

  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
      window.removeEventListener('resize', this.resizeChart);
    }
  }

  private initChart() {
    const chartDom = this.$refs.hssInspectPlanPieChart as HTMLElement;
    this.chart = echarts.init(chartDom);
    
    // Store a reference to the chart for use in the labelLayout function
    const chart = this.chart;

    const option = {
      color: ['#FF4D4F', '#FAAD14', '#2970FF'],
      backgroundColor: 'transparent',
      tooltip: {
        trigger: 'item',
        formatter: '{b}: {c} ({d}%)',
      },
      legend: {
        show: false,
      },
      series: [
        {
          name: '隐患类型',
          type: 'pie',
          radius: ['30%', '80%'],
          center: ['50%', '50%'],
          avoidLabelOverlap: true,
          itemStyle: {
            borderRadius: 4,
            borderColor: 'rgba(0, 0, 0, 0.1)',
            borderWidth: 2,
          },
          label: {
            show: true,
            formatter: '{b}\n{d}%',
            color: '#fff',
            fontSize: 14,
            lineHeight: 20,
            alignTo: 'edge',
            edgeDistance: 10,
            minMargin: 5,
          },
          emphasis: {
            label: {
              fontSize: 16,
              fontWeight: 'bold',
            },
          },
          labelLine: {
            show: true,
            length: 15,
            length2: 30,
            maxSurfaceAngle: 80,
          },
          data: [
            { value: 35, name: '重大隐患' },
            { value: 25, name: '较大隐患' },
            { value: 40, name: '一般问题' },
          ],
          labelLayout: function (params) {
            const isLeft = params.labelRect.x < chart.getWidth() / 2;
            const points = params.labelLinePoints;
            // 更新端点
            points[2][0] = isLeft
              ? params.labelRect.x
              : params.labelRect.x + params.labelRect.width;
            return {
              labelLinePoints: points
            };
          },
        },
      ],
    };

    this.chart.setOption(option);
    window.addEventListener('resize', this.resizeChart);
    this.$emit('resize', this.chart);
  }

  private resizeChart() {
    if (this.chart) {
      this.chart.resize();
    }
  }
}
</script>

<style lang="less" scoped>
.hss-inspect-plan-pie {
  width: 100%;
  height: 100%;
  padding: 6.5% 3% 2.5% 3%;
  box-sizing: border-box;

  .hss-inspect-plan-pie-scroll {
    width: 100%;
    height: 100%;
    overflow: hidden;
    background-color: rgba(1, 202, 217, 0.1);
    border-radius: 0;
    padding: 0 4%;
    box-sizing: border-box;
    // 使用clip-path创建斜角效果
    clip-path: polygon(
      10px 0,
      calc(100% - 10px) 0,
      100% 10px,
      100% calc(100% - 10px),
      calc(100% - 10px) 100%,
      10px 100%,
      0 calc(100% - 10px),
      0 10px
    );
  }

  .hss-inspect-plan-pie-chart {
    width: 100%;
    height: 100%;
  }
}
</style>
