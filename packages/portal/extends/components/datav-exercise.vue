<template>
  <div class="data-exercise">
    <div id="percentChart" class="chart-container"></div>
  </div>
</template>
<script lang="ts">
import { Vue, Component } from 'vue-property-decorator';
import * as echarts from 'echarts';

@Component({
  name: 'DatavExercise',
})
export default class DatavExercise extends Vue {
  private chart: echarts.ECharts | null = null;

  mounted() {
    this.initChart();
  }

  beforeDestroy() {
    if (this.chart) {
      this.chart.dispose();
      this.chart = null;
    }
  }

  private initChart() {
    const chartDom = document.getElementById('percentChart');
    if (!chartDom) return;

    this.chart = echarts.init(chartDom);

    const option = {
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        top: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'value',
        max: 100,
        axisLabel: {
          formatter: '{value}%',
        },
        show: false,
      },
      yAxis: {
        type: 'category',
        data: ['25%', '90%', '70%', '30%'],
        axisTick: { show: false },
        axisLine: { show: false },
      },
      series: [
        {
          type: 'bar',
          data: [
            {
              value: 25,
              itemStyle: { color: '#ADE2BB' },
            },
            {
              value: 90,
              itemStyle: { color: '#F8C0C0' },
            },
            {
              value: 70,
              itemStyle: { color: '#F8D0A0' },
            },
            {
              value: 30,
              itemStyle: { color: '#B8D8F8' },
            },
          ],
          barWidth: 20,
          showBackground: true,
          backgroundStyle: {
            color: 'rgba(220, 220, 220, 0.2)',
          },
        },
      ],
    };

    this.chart.setOption(option);

    window.addEventListener('resize', this.resizeChart);
  }

  private resizeChart() {
    if (this.chart) {
      this.chart.resize();
    }
  }
}
</script>
<style lang="less" scoped>
.data-exercise {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  
  .chart-container {
    width: 100%;
    height: 100%;
  }
}
</style>
