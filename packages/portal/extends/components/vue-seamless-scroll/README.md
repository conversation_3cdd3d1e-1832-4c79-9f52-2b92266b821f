<h1 align="center">vue-seamless-scroll</h1>


<p align="center">
<a href="https://www.npmjs.com/package/vue-seamless-scroll">
    <img src="https://img.shields.io/npm/v/vue-seamless-scroll.svg"/>
    <img src="https://img.shields.io/npm/dm/vue-seamless-scroll.svg"/>
</a>

<a href="https://chenxuan0000.github.io/vue-seamless-scroll/zh/">
    <img src="https://img.shields.io/github/stars/chenxuan0000/vue-seamless-scroll.svg"/>
</a>
</a>

<h3 align="center">A simple, Seamless scrolling for Vue.js</h3>

## Features

* Initial configuration support
* Compatible with multiple platforms
* Multi-technology stack version support

## Documentation
To check out [live examples](https://chenxuan0000.github.io/vue-seamless-scroll/guide/01-basic.html) and docs, visit [vue-seamless-scroll-doc](https://chenxuan0000.github.io/vue-seamless-scroll/).

## Cares
If you want js to scroll seamlessly (without dependencies) you can switch to here.[seamscroll](https://github.com/chenxuan0000/seamless-scroll).

## Contribution
Welcome to give some Suggestions and optimizations, and look forward to your `Pull Request`.

## License
vue-seamless-scroll is open source and released under the [MIT License](LICENSE).