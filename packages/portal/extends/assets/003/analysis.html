<!doctype html>
<meta charset="utf-8">
<title>警情警力分析</title>
<link href="css/style.css"  rel="stylesheet" type="text/css" media="all" />
<script src="js/echarts.min.js" charset="utf-8"></script>


<body>
<div class="wpbox">
<div class="bnt">
  <div class="topbnt_left fl">
   <ul><li class="active"><a href="#">警情警力</a></li>
      <li><a href="#">实有人口</a></li>
      <li><a href="#">流动人口</a></li>
      <li><a href="#">实名制</a></li>
   </ul>
  </div>
  <h1 class="tith1 fl">矛盾纠纷分析</h1>
  <div class=" fr topbnt_right">
    <ul>
       <li><a href="#">返回</a></li>
       <li><a href="#">分析报告</a></li>
       <li><a href="#">交通</a></li>
       <li><a href="#">舆情</a></li>
    </ul>
   </ul>
  </div>
</div>
<!-- bnt end -->
<div class="left1">
    <div class="aleftboxttop"><h2 class="tith2">今日矛盾纠纷数据统计</h2>
    <div class="lefttoday_tit" style=" height:8%"><p class="fl">地区：甘孜</p><p class="fr">2018-06-14</p></div>
    <div class="lefttoday_number">
      <div class="widget-inline-box text-center fl">
        <p>矛盾纠纷</p>
        <h3 class="ceeb1fd">54</h3>
        <h4 class="text-muted pt6">环比<img src="img/iconup.png" height="16" />2%</h4>
      </div>
      <div class="widget-inline-box text-center fl">
         <p>已调节</p>
        <h3 class="c24c9ff">54</h3>
        <h4 class="text-muted pt6">环比<img src="img/icondown.png" height="16" />3%</h4>
      </div>
      <div class="widget-inline-box text-center fl">
         <p>未调节</p>
        <h3 class="cffff00">4</h3>
        <h4 class="text-muted pt6">环比<img src="img/icondown.png" height="16" />3%</h4>
      </div>
      <div class="widget-inline-box text-center fl">
         <p>处理中</p>
        <h3 class="c11e2dd">4</h3>
        <h4 class="text-muted pt6">环比<img src="img/icondown.png" height="16" />3%</h4>
      </div>
    </div>
    <!-- lefttoday_number end -->
    </div>
    <div class="aleftboxtmidd">
      <h2 class="tith2">矛盾纠纷地区统计</h2>
          <div class="lefttoday_tit height ht"><p class="fl">状态：已调节</p><p class="fr">时间段：2018-06-10 至 2018-06-14</p></div>
      <div id="aleftboxtmidd" class="aleftboxtmiddcont"></div>
  </div>
  <div class="aleftboxtbott">
    <h2 class="tith2">矛盾纠纷类型统计</h2>
        <div class="lefttoday_tit height"><p class="fl">状态：已调节</p><p class="fr">时间段：2018-06-10 至 2018-06-14</p></div>
    <div id="aleftboxtbott" class="aleftboxtbott_cont" ></div>
</div>
</div>
<!--  left1 end -->
<div class="mrbox">
      <div class="mrbox_topmidd" style="width: 69%;">
          <div class="amiddboxttop">
              <h2 class="tith2 pt1">实时监控统计</h2>
                <div class="amiddboxttop_map" style="">
                 <span class="camera_l"  style=" top:34%;left:32%"></span>
                 <span class="camera_l" style=" top:10%;left:10%"></span>
                 <span class="camera_l" style=" top:5%;left:40%"></span>
                 <span class="camera_l" style=" top:10%;left:50%"></span>
                 <span style=" top:30%;left:75%"></span>
                 <span style=" top:5%;left:92%"></span>
                 <span style=" top:40%;left:83%"></span>
                </div>
            </div>
            <!--  amiddboxttop end-->
                <div class="amidd_bott">
                  <div class="amiddboxtbott1 fl" >
                    <h2 class="tith2 pt1">矛盾纠纷环比分析</h2>
                    <div id="amiddboxtbott1" class="amiddboxtbott1content" ></div>
                  </div>

                <div class="amiddboxtbott2 fl"><h2 class="tith2 pt1">案件统计</h2>
                    <div id="amiddboxtbott2" class="amiddboxtbott2content"></div>
                </div>
            </div>
            <!-- amidd_bott end -->
          </div>
        <!-- mrbox_top end -->
        <div class="mrbox_top_right">
          <div class="arightboxtop"><h2 class="tith2">警力分析</h2>
            <div class="lefttoday_tit"><p class="fl">状态：已调节</p><p class="fr">时间段：2018-06-10</p></div>
            <div class="left2_table">
               <ul>
                    <li>
                    <p class="fl"><b>康定市公安局</b><br>
                      村名王某因为被隔壁邻居的狗咬了，产生了纠纷，村名报警。<br>
                    </p>
                    <p class="fr pt17">2018-06-22</p>
                    </li>
                    <li class="bg">
                    <p class="fl"><b>康定市公安局</b><br>
                      村名王某因为被隔壁邻居的狗咬了，产生了纠纷，村名报警。<br>
                    </p>
                    <p class="fr pt17">2018-06-22</p>
                    </li>
                    <li>
                    <p class="fl"><b>康定市公安局</b><br>
                      村名王某因为被隔壁邻居的狗咬了，产生了纠纷，村名报警。<br>
                    </p>
                    <p class="fr pt17">2018-06-22</p>
                    </li>
                    <li class="bg">
                    <p class="fl"><b>康定市公安局</b><br>
                      村名王某因为被隔壁邻居的狗咬了，产生了纠纷，村名报警。<br>
                    </p>
                    <p class="fr pt17">2018-06-22</p>
                    </li>
                    <li>
                    <p class="fl"><b>康定市公安局</b><br>
                      村名王某因为被隔壁邻居的狗咬了，产生了纠纷，村名报警。<br>
                    </p>
                    <p class="fr pt17">2018-06-22</p>
                    </li>
                    <li class="bg">
                    <p class="fl"><b>康定市公安局</b><br>
                      村名王某因为被隔壁邻居的狗咬了，产生了纠纷，村名报警。<br>
                    </p>
                    <p class="fr pt17">2018-06-22</p>
                    </li>
                    <li>
                    <p class="fl"><b>康定市公安局</b><br>
                      村名王某因为被隔壁邻居的狗咬了，产生了纠纷，村名报警。<br>
                    </p>
                    <p class="fr pt17">2018-06-22</p>
                    </li>
                    <li class="bg">
                    <p class="fl"><b>康定市公安局</b><br>
                      村名王某因为被隔壁邻居的狗咬了，产生了纠纷，村名报警。<br>
                    </p>
                    <p class="fr pt17">2018-06-22</p>
                    </li>

               </ul>
            </div>

          </div>
            <div class="arightboxbott"><h2 class="tith2 ">矛盾纠纷七日数据分析</h2>
              <div class="lefttoday_tit"><p class="fl">状态：已调节</p><p class="fr">时间：2018-06-14</p></div>
              <div id="arightboxbott" class="arightboxbottcont" style=""></div>
            </div>
        </div>
        <!-- mrbox_top_right end -->
      </div>

    </div>
</div>

                <script type="text/javascript">
                        var myChart = echarts.init(document.getElementById('aleftboxtmidd'));
                        option = {
                          color:['#76c4bf','#e5ffc7','#508097','#4d72d9'],
                            backgroundColor: 'rgba(1,202,217,.2)',
                            grid: {
                											left:10,
                											right:40,
                											top:20,
                											bottom:0,
                                      containLabel: true
                										},
                                      // legend: {
                                      //     x : 'center',
                                      //     y : '70%',
                                      //     textStyle:{
                                      //       fontSize: 10,
                                      //       color:'rgba(255,255,255,.7)'
                                      //     },
                                      //     data:['康定市','丹巴县','甘孜县','理塘县']
                                      // },
                                      calculable : true,
                                      series : [

                                          {
                                              name:'面积模式',
                                              type:'pie',
                                              radius : [5, 60],
                                              center : ['50%', '55%'],
                                              roseType : 'area',
                                              data:[
                                                  {value:10, name:'康定市'},
                                                  {value:5, name:'丹巴县'},
                                                  {value:15, name:'甘孜县'},
                                                  {value:25, name:'理塘县'}
                                              ]
                                          }
                                      ]
                                  };
                        myChart.setOption(option);
                    </script>
                    <script type="text/javascript">
                            var myChart = echarts.init(document.getElementById('aleftboxtbott'));
                            option = {
                              color:['#7ecef4'],
                                backgroundColor: 'rgba(1,202,217,.2)',
                                grid: {
                    											left:20,
                    											right:20,
                    											top:13,
                    											bottom:6,
                                          containLabel: true
                    										},

                                      xAxis: {
                                          type: 'value',
                                          axisLine:{
                                           lineStyle:{
                                             color:'rgba(255,255,255,.2)'
                                           }
                                         },
                                         splitLine:{
                                           lineStyle:{
                                             color:'rgba(255,255,255,0)'
                                           }
                                         },
                                         axisLabel:{
                                             color:"rgba(255,255,255,0)"
                                         },
                                          boundaryGap: [0, 0.01]
                                      },
                                      yAxis: {
                                          type: 'category',
                                          axisLine:{
                                           lineStyle:{
                                             color:'rgba(255,255,255,.5)'
                                           }
                                         },
                                         splitLine:{
                                           lineStyle:{
                                             color:'rgba(255,255,255,.1)'
                                           }
                                         },
                                         axisLabel:{
                                             color:"rgba(255,255,255,.5)"
                                         },
                                          data: ['未调节','调节中','未调节']
                                      },
                                      series: [
                                          {
                                              name: '2011年',
                                              type: 'bar',
                                              barWidth :30,
                                              itemStyle: {
                                                  normal: {
                                                      color: new echarts.graphic.LinearGradient(
                                                          1, 0, 0, 1,
                                                          [
                                                              {offset: 0, color: 'rgba(230,253,139,.7)'},
                                                              {offset: 1, color: 'rgba(41,220,205,.7)'}
                                                          ]
                                                      )
                                                  }
                                              },
                                              data: [18203, 23489, 29034]
                                          }
                                      ]
                                  };
                            myChart.setOption(option);
                        </script>
                        <script type="text/javascript">
                                var myChart = echarts.init(document.getElementById('amiddboxtbott1'));
                                var data = [
                                    [[28604,77,17099,'Australia',1990],[31163,77.4,2440,'Canada',1990],[1516,68,1605773,'China',1990],[13670,74.7,10082,'Cuba',1990],[28599,75,49805,'Finland',1990],[29476,77.1,569499,'France',1990],[31476,75.4,789237,'Germany',1990],[28666,78.1,254830,'Iceland',1990],[1777,57.7,870776,'India',1990],[29550,79.1,129285,'Japan',1990],[2076,67.9,201954,'North Korea',1990],[12087,72,42954,'South Korea',1990],[24021,75.4,33934,'New Zealand',1990],[43296,76.8,4240375,'Norway',1990],[10088,70.8,381958,'Poland',1990],[19349,69.6,1475652,'Russia',1990],[10670,67.3,53905,'Turkey',1990],[26424,75.7,57117,'United Kingdom',1990],[37062,75.4,252810,'United States',1990]],
                                    [[44056,81.8,23973,'Australia',2015],[43294,81.7,35927,'Canada',2015],[13334,76.9,1376043,'China',2015],[21291,78.5,11562,'Cuba',2015],[38923,80.8,55057,'Finland',2015],[37599,81.9,64345,'France',2015],[44053,81.1,80545,'Germany',2015],[42182,82.8,329425,'Iceland',2015],[5903,66.8,1311027,'India',2015],[36162,83.5,126571,'Japan',2015],[1390,71.4,251317,'North Korea',2015],[34644,80.7,503439,'South Korea',2015],[34186,80.6,4528526,'New Zealand',2015],[64304,81.6,5210967,'Norway',2015],[24787,77.3,386194,'Poland',2015],[23038,73.13,143918,'Russia',2015],[19360,76.5,78630,'Turkey',2015],[38225,81.4,64715810,'United Kingdom',2015],[53354,79.1,321771,'United States',2015]]
                                ];

                                option = {
                                    backgroundColor: 'rgba(1,202,217,.2)',
                                    grid: {
                                      left:40,
                                      right:40,
                                      top:50,
                                      bottom:40
                        										},
                                    title: {
                                      top: 5,
                                      left:20,
                                        textStyle:{
                                          fontSize:10,
                                          color:'rgba(255,255,255,.6)'
                                        },
                                        text: '环比类型：日环比'
                                    },
                                    legend: {
                                        right: 10,
                                        top: 5,
                                        textStyle:{
                                          fontSize:10,
                                          color:'rgba(255,255,255,.6)'
                                        },
                                        data: ['1990', '2015']
                                    },
                                    xAxis: {
                                      axisLine:{
                                        lineStyle:{
                                          color:'rgba(255,255,255,.2)'
                                        }
                                      },
                                      splitLine:{
                                        lineStyle:{
                                          color:'rgba(255,255,255,.1)'
                                        }
                                      },
                                      axisLabel:{
                                          color:"rgba(255,255,255,.7)"
                                      }
                                    },
                                    yAxis: {
                                      axisLine:{
                                        lineStyle:{
                                          color:'rgba(255,255,255,.2)'
                                        }
                                      },
                                      splitLine:{
                                        lineStyle:{
                                          color:'rgba(255,255,255,.1)'
                                        }
                                      },
                                      axisLabel:{
                                          color:"rgba(255,255,255,.7)"
                                      },
                                        scale: true
                                    },
                                    series: [{
                                        name: '1990',
                                        data: data[0],
                                        type: 'scatter',
                                        symbolSize: function (data) {
                                            return Math.sqrt(data[2]) / 5e2;
                                        },
                                        label: {
                                            emphasis: {
                                                show: true,
                                                formatter: function (param) {
                                                    return param.data[3];
                                                },
                                                position: 'top'
                                            }
                                        },
                                        itemStyle: {
                                            normal: {
                                                shadowBlur: 10,
                                                shadowColor: 'rgba(120, 36, 50, 0.5)',
                                                shadowOffsetY: 5,
                                                color: new echarts.graphic.RadialGradient(0.4, 0.3, 1, [{
                                                    offset: 0,
                                                    color: 'rgb(251, 118, 123)'
                                                }, {
                                                    offset: 1,
                                                    color: 'rgb(204, 46, 72)'
                                                }])
                                            }
                                        }
                                    }, {
                                        name: '2015',
                                        data: data[1],
                                        type: 'scatter',
                                        symbolSize: function (data) {
                                            return Math.sqrt(data[2]) / 5e2;
                                        },
                                        label: {
                                            emphasis: {
                                                show: true,
                                                formatter: function (param) {
                                                    return param.data[3];
                                                },
                                                position: 'top'
                                            }
                                        },
                                        itemStyle: {
                                            normal: {
                                                shadowBlur: 10,
                                                shadowColor: 'rgba(25, 100, 150, 0.5)',
                                                shadowOffsetY: 5,
                                                color: new echarts.graphic.RadialGradient(0.4, 0.3, 1, [{
                                                    offset: 0,
                                                    color: 'rgb(129, 227, 238)'
                                                }, {
                                                    offset: 1,
                                                    color: 'rgb(25, 183, 207)'
                                                }])
                                            }
                                        }
                                    }]
                                };
                                myChart.setOption(option);
                            </script>

                        <script type="text/javascript">
                                var myChart = echarts.init(document.getElementById('amiddboxtbott2'));
                                option = {
                                    backgroundColor: 'rgba(1,202,217,.2)',
                                    grid: {
                        											left:60,
                        											right:60,
                        											top:50,
                        											bottom:40
                        										},

                              toolbox: {
                                  feature: {
                                      dataView: {show: true, readOnly: false},
                                      magicType: {show: true, type: ['line', 'bar']},
                                      restore: {show: true},
                                      saveAsImage: {show: true}
                                  }
                              },
                              legend: {
                                top:10,
                                textStyle:{
                                  fontSize: 10,
                                  color:'rgba(255,255,255,.7)'
                                },
                                  data:['2017年','2018年','同比增速']
                              },
                              xAxis: [
                                  {
                                      type: 'category',
                                      axisLine:{
                												lineStyle:{
                													color:'rgba(255,255,255,.2)'
                												}
                											},
                											splitLine:{
                												lineStyle:{
                													color:'rgba(255,255,255,.1)'
                												}
                											},
                											axisLabel:{
                													color:"rgba(255,255,255,.7)"
                											},

                                      data: ['1','2','3','4','5','6','7','8','9','10','11','12'],
                                      axisPointer: {
                                          type: 'shadow'
                                      }
                                  }
                              ],
                              yAxis: [
                                  {
                                      type: 'value',
                                      name: '',
                                      min: 0,
                                      max: 250,
                                      interval: 50,
                                      axisLine:{
                												lineStyle:{
                													color:'rgba(255,255,255,.3)'
                												}
                											},
                											splitLine:{
                												lineStyle:{
                													color:'rgba(255,255,255,.01)'
                												}
                											},

                                      axisLabel: {
                                          formatter: '{value} ml'
                                      }
                                  },
                                  {
                                      type: 'value',
                                      name: '',
                                      max: 25,
                                      interval: 5,
                                      axisLine:{
                												lineStyle:{
                													color:'rgba(255,255,255,.3)'
                												}
                											},
                											splitLine:{
                												lineStyle:{
                													color:'rgba(255,255,255,.1)'
                												}
                											},
                                      axisLabel: {
                                          formatter: '{value} °C'
                                      }
                                  }
                              ],
                              series: [

                                  {
                                      name:'2017年',
                                      type:'bar',
                                      itemStyle: {
                                                      normal: {
                                                          color: new echarts.graphic.LinearGradient(
                                                              0, 0, 0, 1,
                                                              [
                                                                  {offset: 0, color: '#b266ff'},
                                                                  {offset: 1, color: '#121b87'}
                                                              ]
                                                          )
                                                      }
                                                  },
                                      data:[2.0, 4.9, 7.0, 23.2, 25.6, 76.7, 135.6, 162.2, 32.6, 20.0, 6.4, 3.3]
                                  },
                                  {
                                      name:'2018年',
                                      type:'bar',
                                      itemStyle: {
                                                      normal: {
                                                          color: new echarts.graphic.LinearGradient(
                                                              0, 0, 0, 1,
                                                              [
                                                                  {offset: 0, color: '#00f0ff'},
                                                                  {offset: 1, color: '#032a72'}
                                                              ]
                                                          )
                                                      }
                                                  },
                                      data:[2.6, 5.9, 9.0, 26.4, 28.7, 70.7, 175.6, 182.2, 48.7, 18.8, 6.0, 2.3]
                                  },
                                  {
                                      name:'同比增速',
                                      type:'line',
                                      itemStyle: {
                                                      normal: {
                                                          color: new echarts.graphic.LinearGradient(
                                                              0, 0, 0, 1,
                                                              [
                                                                  {offset: 0, color: '#fffb34'},
                                                                  {offset: 1, color: '#fffb34'}
                                                              ]
                                                          )
                                                      }
                                                  },
                                      yAxisIndex: 1,
                                      data:[2.0, 2.2, 3.3, 4.5, 6.3, 10.2, 20.3, 23.4, 23.0, 16.5, 12.0, 6.2]
                                  }
                              ]
                          };
                                myChart.setOption(option);
                            </script>
                            <script type="text/javascript">
                                    var myChart = echarts.init(document.getElementById('arightboxbott'));
                                    option = {
                                      color:['#7de494','#7fd7b1', '#5578cf', '#5ebbeb', '#d16ad8','#f8e19a',  '#00b7ee', '#81dabe','#5fc5ce'],
                                        backgroundColor: 'rgba(1,202,217,.2)',

                                        grid: {
                                            left: '5%',
                                            right: '8%',
                                            bottom: '7%',
                                            top:'8%',
                                            containLabel: true
                                        },
                                        toolbox: {
                                            feature: {
                                                saveAsImage: {}
                                            }
                                        },
                                        xAxis: {
                                            type: 'category',
                                            boundaryGap: false,
                                            axisLine:{
                                              lineStyle:{
                                                color:'rgba(255,255,255,.2)'
                                              }
                                            },
                                            splitLine:{
                                              lineStyle:{
                                                color:'rgba(255,255,255,.1)'
                                              }
                                            },
                                            axisLabel:{
                                                color:"rgba(255,255,255,.7)"
                                            },
                                            data: ['6-08','6-09','6-10','6-11','6-12','6-13','6-14']
                                        },
                                        yAxis: {
                                            type: 'value',
                                            axisLine:{
                                              lineStyle:{
                                                color:'rgba(255,255,255,.2)'
                                              }
                                            },
                                            splitLine:{
                                              lineStyle:{
                                                color:'rgba(255,255,255,.1)'
                                              }
                                            },
                                            axisLabel:{
                                                color:"rgba(255,255,255,.7)"
                                            },
                                        },
                                        series: [
                                            {
                                                name:'2014年',
                                                type:'line',
                                                stack: '总量',
                                                  areaStyle: {normal: {}},
                                                data:[120, 132, 101, 134, 90, 230, 210]
                                            }

                                        ]
                                        };
                                    myChart.setOption(option);
                                </script>


</body>
</html>
