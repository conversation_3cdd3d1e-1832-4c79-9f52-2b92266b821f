<template>
  <div class="jsgjzl-tjb-container">
    <div v-if="loading" class="loading">正在加载，请稍后...</div>
    <div v-else class="main">
      <div class="title-container" style="position: relative;">
        <h1 class="title" style="text-align: center;">考试测验</h1>
        <div class="pagination" style="position: absolute; right: 0; top: -2px; transform: translateY(-50%);">
        </div>
      </div>

      <div class="question-list">
        <div v-for="(item, index) in examQuestions" :key="item.id" class="question-item">
          <div class="question-header">
            <span class="question-type">{{ item.data.stlx }}</span>
            <span class="question-number">第{{ index + 1 }}题</span>
            <span class="question-tag">{{ item.data.tx }}</span>
          </div>
          <div class="question-content">
            <div style=" align-items: center;">
              <p>{{ item.data.stnr }}</p>
              <div v-for="(image, index) in item.data.sttp" :key="index" style="margin-left: 10px;">
                <img :src="`https://kltz.dyjkyl.com/api/api/aliyun/download?refId=${image.refId}`" alt="试题图片"
                  @click="showImagePreview(image)" style="width: 100px; height: 100px;" />
              </div>
            </div>
            <div v-if="item.data.stlx === '单选' || item.data.stlx === '多选'" class="options">
              <div v-for="(option, key) in ['xxa', 'xxb', 'xxc', 'xxd', 'xxe']" :key="key" v-if="item.data[option]">
                <label>
                  <input :type="item.data.stlx === '单选' ? 'radio' : 'checkbox'" :name="'q' + index"
                    :value="String.fromCharCode(65 + key)" v-model="userAnswers[index]"
                    :checked="Array.isArray(userAnswers[index]) ? userAnswers[index].includes(String.fromCharCode(65 + key)) : userAnswers[index] === String.fromCharCode(65 + key)">
                  {{ String.fromCharCode(65 + key) }}. {{ item.data[option] }}
                </label>
              </div>
            </div>
            <div v-else-if="item.data.stlx === '判断'" class="options">
              <label>
                <input type="radio" :name="'q' + index" value="A" v-model="userAnswers[index]">
                A. {{ item.data.xxa }}
              </label>
              <label>
                <input type="radio" :name="'q' + index" value="B" v-model="userAnswers[index]">
                B. {{ item.data.xxb }}
              </label>
            </div>
            <!-- <div class="correct-answer">正确答案: {{ item.data.zqxx }}</div> -->
          </div>
        </div>
      </div>

      <div class="actions" v-if="!loading" style="text-align: center; margin-top: 20px;">
        <button @click="handCorfirm"
          style="padding: 8px 16px; background: #1890ff; color: white; border: none; border-radius: 4px;">
          提交答案
        </button>
      </div>
      <a-modal v-model="previewVisible" :footer="null">
        <img :src="previewImageUrl" style="width: 100%" />
      </a-modal>
    </div>
  </div>
</template>



<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { Table, Modal, Button, Icon, DatePicker, Pagination } from '@h3/antd-vue';
import Api from '../../ly-api';

@Component({
  name: 'JsgjzlTjb',
  components: {
    ATable: Table,
    AButton: Button,
    AIcon: Icon,
    ARangePicker: DatePicker.RangePicker,
    APagination: Pagination,
    AModal: Modal,
  },
})

export default class JsgjzlTjb extends Vue {

  allQuestions = [];  // 所有题目数据
  examQuestions = [];  // 考试题目
  userAnswers = [];    // 用户答案
  showAnswers = false; // 是否显示答案
  score = 0;           // 得分
  replayToken: any = '';
  userInfo: any = null; // 用户信息
  loading = true; // 加载状态
  previewVisible = false;
  previewImageUrl = '';
  // 生命周期钩子
  mounted() {
    const userData = sessionStorage.getItem('user');
    this.userInfo = userData ? JSON.parse(userData) : null;
    console.log('userInfo', this.userInfo);

    this.fetchReplayToken();
    this.fetchData();
  }
  //获取令牌
  async fetchReplayToken() {
    this.replayToken = await Api.exportReplayToken();
  }
  // 获取数据方法
  async fetchData() {
    try {
      this.loading = true;
      const data = await Api.getList({
        filters: [],
        mobile: false,
        page: 0,
        queryCode: "tkxx",
        schemaCode: 'tkxx',
        size: 5000,
        queryVersion: 1,
        queryCondition: [],
        showTotal: true,
      });

      this.allQuestions = data.data.content;
      this.prepareExam();
    } catch (error) {
      console.error('获取题目失败:', error);
    } finally {
      this.loading = false;
    }
  }

  // 准备考试题目
  prepareExam() {
    // 筛选不同类型的题目
    const judgeQuestions = this.allQuestions.filter(q => q.data.stlx === '判断');
    const singleChoiceQuestions = this.allQuestions.filter(q => q.data.stlx === '单选');
    const multiChoiceQuestions = this.allQuestions.filter(q => q.data.stlx === '多选');

    // 随机抽取题目
    const shuffle = (array) => {
      return array.sort(() => Math.random() - 0.5);
    };
    // const selectedJudge = judgeQuestions.slice(0, 10);
    // const selectedSingle = singleChoiceQuestions.slice(0, 20);
    // const selectedMulti = multiChoiceQuestions.slice(0, 20);


    const selectedJudge = shuffle(judgeQuestions).slice(0, 10);
    const selectedSingle = shuffle(singleChoiceQuestions).slice(0, 20);
    const selectedMulti = shuffle(multiChoiceQuestions).slice(0, 20);

    this.examQuestions = shuffle([...selectedJudge, ...selectedSingle, ...selectedMulti]);
    this.userAnswers = this.examQuestions.map(q =>
      q.data.stlx === '多选' ? [] : ''
    );
  }



  handCorfirm() {
    const that = this;
    this.$confirm({
      title: "提示",
      content: '您确定要提交答题吗?',
      onOk() {
        that.submitExams();
      },
      okText: '确定',
      cancelText: '取消',
      onCancel() {
        console.log('Cancel');
      },
    });
  }


  //新版提交考试
  async submitExams() {
    const wrongQuestions = [];
    let score = 0; // 初始化得分

    this.examQuestions.forEach((question, index) => {
      const correctAnswer = String(question.data.zqxx).split(',').map(item => item.trim()).sort();
      let userAnswer = this.userAnswers[index];

      if (!Array.isArray(userAnswer)) {
        userAnswer = String(userAnswer).split(',').map(item => item.trim());
      }

      userAnswer = userAnswer.sort();
      console.log(`第 ${index + 1} 题 正确答案为：${correctAnswer.join(',')}，您的选项为：${userAnswer.join(',')}`);
      // 比较用户答案和正确答案是否一致
      const isEqual = correctAnswer.length === userAnswer.length &&
        correctAnswer.every((value, i) => value === userAnswer[i]);

      if (isEqual) {
        score += 2; // 答对加 2 分
      } else {
        const wrongQuestion: any = {
          dtr: this.userInfo.id,
          tmid: question.id,
          tmlx: question.data.stlx,
          tm: question.data.stnr,
          xxa: question.data.xxa,
          xxb: question.data.xxb,
          xxc: question.data.xxc,
          xxd: question.data.xxd,
          xxe: question.data.xxe,
          zqxx: question.data.zqxx,
          tmxh: question.data.xhsz
        };

        if (question.data.sttp && question.data.sttp.length > 0) {
          wrongQuestion.attachmentModelList = question.data.sttp.map(item => ({
            refId: item.refId,
            name: item.name,
            fileExtension: item.fileExtension,
            fileSize: item.fileSize,
            mimeType: item.mimeType,
            base64ImageStr: item.base64ImageStr,
            schemaCode: 'ctk'
          }));
        }

        wrongQuestions.push(wrongQuestion);
      }
    });
    // 向试题内容提交
    const jsonData = {
      Sheet1752110256787: this.examQuestions.map((question, index) => {
        const correctAnswer = question.data.zqxx;
        const userAnswer = this.userAnswers[index];
        let hdqk = '';

        if (question.data.stlx === '多选') {
          const correctArray = String(correctAnswer).split(',').map(item => item.trim()).sort();
          const userArray = Array.isArray(userAnswer) ? [...userAnswer].sort() : String(userAnswer).split(',').map(item => item.trim()).sort();
          hdqk = correctArray.length === userArray.length && correctArray.every((value, i) => value === userArray[i]) ? '回答正确' : '回答错误';
        } else {
          hdqk = userAnswer === correctAnswer ? '回答正确' : '回答错误';
        }

        return {
          rowStatus: 'Added',
          stlx: question.data.stlx,
          stnr: question.data.stnr,
          xxa: question.data.xxa,
          xxb: question.data.xxb,
          xxc: question.data.xxc,
          xxd: question.data.xxd,
          xxe: question.data.xxe,
          zqxx: question.data.zqxx,
          wdxx: Array.isArray(this.userAnswers[index]) ? this.userAnswers[index].join(',') : this.userAnswers[index],
          hdqk: hdqk,
        };
      }),
      dtr: [{
        id: this.userInfo.id,
        name: this.userInfo.name,
        type: this.userInfo.unitType,
      }],
      zdf: score, // 给 zdf 字段赋值
    };
    console.log('jsonData', jsonData);
    const data = await Api.exportRelease({
      actionCode: "submit",
      agree: true,
      bizObject: {
        data: jsonData,
        schemaCode: "stnr",
        sheetCode: "stnr",
        workflowInstanceId: '',
      },
      workflowCode: null,
      workItemId: null,
      queryId: "",
      latestSign: null,
      formType: "2",
      replayToken: this.replayToken.data,
    });

    // 上传错题数据
    if (wrongQuestions.length > 0) {
      try {
        await Api.exportError(wrongQuestions);
        // console.log('错题数据上传成功',wrongQuestions);
        this.$message.success('提交成功！');
        this.fetchData();
      } catch (error) {
        console.error('错题数据上传失败:', error);
      }
    }
  }
  // 图片预览方法
  showImagePreview(image) {
    this.previewImageUrl = `https://kltz.dyjkyl.com/api/api/aliyun/download?refId=${image.refId}`;
    this.previewVisible = true;
  }
}
</script>
<style lang="less" scoped>
.jsgjzl-tjb-container {
  height: 100%;
  padding: 12px;
  background: #f8f8f8;

  .main {
    height: calc(100% - 24px);
    background: #fff;
    border-radius: 4px;
    padding: 12px;
    overflow: auto;

    .title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 12px;
      text-align: center;
    }

    .question-list {
      margin-bottom: 20px;

      .question-item {
        margin-bottom: 20px;
        padding: 15px;
        border: 1px solid #e8e8e8;
        border-radius: 4px;

        .question-header {
          display: flex;
          align-items: center;
          margin-bottom: 10px;

          .question-type {
            padding: 2px 6px;
            background: #1890ff !important;
            color: white;
            border-radius: 2px;
            font-size: 12px;
            margin-right: 10px;
          }

          .question-number {
            font-weight: bold;
            margin-right: 10px;
          }

          .question-tag {
            color: #faad14;
            font-size: 12px;
          }
        }

        .question-content {
          p {
            margin-bottom: 10px;
            font-size: 14px;
          }

          .options {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 10px 0;

            div {
              margin-bottom: 5px;
              padding: 5px;

              &:hover {
                background: #f0f7ff;
              }
            }
          }

          .correct-answer {
            margin-top: 10px;
            padding: 5px;
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
            font-size: 14px;
          }
        }
      }
    }


  }
}

.loading {
  text-align: center;
  padding: 20px;
  font-size: 16px;
  color: #666;
}
</style>
