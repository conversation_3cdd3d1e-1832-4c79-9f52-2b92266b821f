<template>
  <div class="jsgjzl-tjb-container">
       <div v-if="loading" class="loading">正在加载，请稍后...</div>
    <div class="main" v-else>
    
      <div class="title-container"
        style="position: relative; display: flex; justify-content: space-between; align-items: center;">
        <h1 class="title" style="text-align: center; flex: 1;">错题练习</h1>

        <div class="pagination" style="display: flex; gap: 8px;">

          <div style="margin-right: auto;">
            <span style="font-size: 14px; color: #333; margin-right: 10px; vertical-align: middle;">
              问题类型:
            </span>
            <a-select v-model="questionType" @change="handleQuestionTypeChange" placeholder="请选择问题类型"
              style="width: 150px;">
              <a-select-option value="多选">多选</a-select-option>
              <a-select-option value="单选">单选</a-select-option>
              <a-select-option value="判断">判断</a-select-option>
            </a-select>
          </div>
          <button @click="sortByErrorCount" class="sort-button">
            错题次数排序
            <span v-if="sortByCtcs">↓</span>
            <span v-else>↑</span>
          </button>
          <button @click="sortByIndexs" class="sort-button">序号排序</button>
          <button @click="refresh" class="sort-button">重置</button>
        </div>
      </div>


      <div class="question-list">
        <div v-for="(item, index) in dataSource" :key="item.id" class="question-item">
          <div class="question-header">
            <span class="question-type">{{ item.data.tmlx }}</span>
            <span class="question-number">第{{ item.data.tmxh }}题</span>
            <span class="question-tag">错题次数:{{ item.data.ctcs }}</span>
          </div>
          <div class="question-content">
            <div style=" align-items: center;">
              <p>{{ item.data.tm }}</p>
              <div v-for="(image, index) in item.data.tp" :key="index" style="margin-left: 10px;">
                <img :src="`https://kltz.dyjkyl.com/api/api/aliyun/download?refId=${image.refId}`" alt="试题图片"
                  @click="showImagePreview(image)" style="width: 100px; height: 100px;" />
              </div>
            </div>
            <div v-if="item.data.tmlx === '单选'">
              <a-radio-group v-model="item.userAnswer">
                <a-radio v-for="(option, key) in ['xxa', 'xxb', 'xxc', 'xxd', 'xxe']" :key="key"
                  v-if="item.data[option]" :value="String.fromCharCode(65 + key)">
                  <span>{{ String.fromCharCode(65 + key) }}. {{ item.data[option] }}</span>
                </a-radio>
              </a-radio-group>
            </div>
            <div v-else-if="item.data.tmlx === '多选'">
              <a-checkbox-group v-model="item.userAnswer">
                <a-checkbox v-for="(option, key) in ['xxa', 'xxb', 'xxc', 'xxd', 'xxe']" :key="key"
                  v-if="item.data[option]" :value="String.fromCharCode(65 + key)">
                  <span>{{ String.fromCharCode(65 + key) }}. {{ item.data[option] }}</span>
                </a-checkbox>
              </a-checkbox-group>
            </div>
            <div v-else-if="item.data.tmlx === '判断'">
              <a-radio-group v-model="item.userAnswer">
                <a-radio v-if="item.data.xxa" value="A">{{ item.data.xxa }}</a-radio>
                <a-radio v-if="item.data.xxb" value="B">{{ item.data.xxb }}</a-radio>
              </a-radio-group>
            </div>

            <div class="submit-button">
              <div class="button-and-answer">
                <div>
                  <button @click="checkAnswer(item)" :disabled="(item.data.tmlx === '多选' && item.userAnswer.length === 0) ||
                    (item.data.tmlx !== '多选' && !item.userAnswer)">提交答案</button>
                </div>
                <div class="answer-result">
                  <div v-if="item.answerChecked" class="correct-answer" v-show="item.isCorrect">回答正确，正确答案：{{
                    item.data.zqxx }}</div>
                  <div v-if="item.answerChecked" class="wrong-answer" v-show="!item.isCorrect">回答错误，正确答案：{{
                    item.data.zqxx
                  }}</div>
                </div>
              </div>
            </div>

          </div>
        </div>
      </div>

      <!--      <div class="pagination">
        <a-pagination v-model="currentPage" :total="total" :pageSize="pageSize" @change="handlePageChange"
          show-less-items />
      </div>  -->
      <a-modal v-model="previewVisible" :footer="null">
        <img :src="previewImageUrl" style="width: 100%" />
      </a-modal>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { Table, Modal, Button, Icon, DatePicker, Pagination, Radio, Checkbox, Select } from '@h3/antd-vue';
import Api from '../../ly-api';
@Component({
  name: 'JsgjzlTjb',
  components: {
    ATable: Table,
    AButton: Button,
    AIcon: Icon,
    ARangePicker: DatePicker.RangePicker,
    APagination: Pagination,
    ARadio: Radio,
    ARadioGroup: Radio.Group,
    ACheckbox: Checkbox,
    ACheckboxGroup: Checkbox.Group,
    ASelect: Select,
    ASelectOption: Select.Option,
    AModal: Modal,

  },
})
export default class JsgjzlTjb extends Vue {
  dataSource = [];  // 表格数据
  pageSize = 10000;  // 每页显示多少条数据
  currentPage = 1;  // 当前页码
  total = 0;  // 总条数
  loading = false;  // 加载状态
  userInfo: any = null;
  sortByCtcs = false; // 新增排序状态
  questionType = undefined // 问题类型
  previewVisible = false; // 图片预览状态
  previewImageUrl = ''; // 图片预览地址
  // 生命周期钩子
  mounted() {
    const userData = sessionStorage.getItem('user');
    this.userInfo = userData ? JSON.parse(userData) : null;
    console.log('userInfo', this.userInfo);

    const storedItem = localStorage.getItem(this.userInfo?.id + 'exam');
    let storedUserId = null;
    if (storedItem) {
      storedUserId = JSON.parse(storedItem).userId;
    }
    if (!storedItem || storedUserId !== this.userInfo?.id) {
      localStorage.setItem(this.userInfo.id + 'exam', JSON.stringify({
        page: 1,
        userId: this.userInfo.id
      }));
      this.currentPage = 1;
    } else {
      const { page } = JSON.parse(storedItem);
      this.currentPage = page;
    }

    this.fetchData();  // 初始加载数据
  }

  // 获取数据方法
  async fetchData(type?: string) {
    this.loading = true;
    try {
      const queryCondition = [
        [
          [
            {
              propertyCode: "dtr",
              propertyType: 50,
              queryFilterType: "Eq",
              propertyValue: this.userInfo ?
                JSON.stringify([{
                  id: this.userInfo.id,
                  unitType: this.userInfo.unitType,
                  name: this.userInfo.name
                }]) : null
            },
            ...(type ? [{
              propertyCode: "tmlx",
              propertyType: 0,
              queryFilterType: "Like",
              propertyValue: type
            }] : [])
          ]
        ]
      ];

      const data = await Api.getList({
        filters: [],
        mobile: false,
        page: this.currentPage - 1,
        queryCode: "ctk",
        schemaCode: 'ctk',
        size: this.pageSize,
        queryVersion: 1,
        queryCondition,
        showTotal: true,
      });
      this.dataSource = data.data.content.map(item => ({
        ...item,
        userAnswer: item.data.tmlx === '多选' ? [] : '',
        answerChecked: false,
        isCorrect: false
      }));
      this.total = data.data.totalElements;
    } finally {
      this.loading = false;
    }
  }

  // 分页变化
  handlePageChange(page: number) {
    this.currentPage = page;
    localStorage.setItem(this.userInfo.id + 'exam', JSON.stringify({
      page: this.currentPage,
      userId: this.userInfo.id
    }));
    this.fetchData();
  }

  // 检查答案方法
  async checkAnswer(item) {
    // 去除正确答案中的逗号并排序
    const correctAnswer = item.data.zqxx.replace(/,/g, '').split('').sort().join('');
    let userAnswer = '';
    if (Array.isArray(item.userAnswer)) {
      userAnswer = item.userAnswer.sort().join('');
    } else {
      userAnswer = item.userAnswer;
    }

    if (userAnswer === correctAnswer) {
      item.isCorrect = true;
      console.log('正确答案');

    } else {
      item.isCorrect = false;
      // 调用回答错误接口
      console.log('正确答案', item.data.zqxx);
      console.log('提交错误答案:', item.id, item.userAnswer); 
      const imgFize = (item.data.tp || []).map(obj => {
        console.log('obj 的值:', obj);
        return {
          refId: obj.refId,
          name: obj.name,
          fileExtension: obj.fileExtension,
          fileSize: obj.fileSize,
          mimeType: obj.mimeType,
          base64ImageStr: obj.base64ImageStr,
          schemaCode: 'ctk'
        };
      });
      try {
        await Api.exportError([{
          dtr: this.userInfo.id,
          tmid: item.data.tmid,
          tmlx: item.data.tmlx,
          tm: item.data.tm,
          xxa: item.data.xxa || '',
          xxb: item.data.xxb || '',
          xxc: item.data.xxc || '',
          xxd: item.data.xxd || '',
          xxe: item.data.xxe || '',
          zqxx: item.data.zqxx || '',
          tmxh: item.data.tmxh,
          attachmentModelList: imgFize,
        }]);
      } catch (error) {
        console.error('提交错误答案失败:', error);
      }
    }
    item.answerChecked = true;
  }

  //错题次数
  sortByErrorCount() {
    this.sortByCtcs = !this.sortByCtcs;
    this.dataSource.sort((a, b) => {
      if (this.sortByCtcs) {
        return b.data.ctcs - a.data.ctcs;
      } else {
        return a.data.ctcs - b.data.ctcs;
      }
    });
  }
  // 错题次数排序方法
  sortByIndex() {
    this.dataSource.sort((a, b) => {
      return a.id.localeCompare(b.id);
    });
  }
  //序号
  sortByIndexs() {
    this.fetchData(this.questionType);
  }
  refresh() {
    this.questionType = undefined;
    this.fetchData();
  }
  //筛选
  handleQuestionTypeChange(value) {
    this.fetchData(value);
  }
  // 图片预览方法
  showImagePreview(image) {
    this.previewImageUrl = `https://kltz.dyjkyl.com/api/api/aliyun/download?refId=${image.refId}`;
    this.previewVisible = true;
  }
}
</script>

<style lang="less" scoped>
.jsgjzl-tjb-container {
  height: 100%;
  padding: 12px;
  background: #f8f8f8;

  .main {
    height: calc(100% - 24px);
    background: #fff;
    border-radius: 4px;
    padding: 12px;
    overflow: auto;

    .title {
      font-size: 16px;
      font-weight: 500;
      margin-bottom: 12px;
      text-align: center;
      margin-left: 50px;
    }

    .question-list {
      margin-bottom: 20px;

      .question-item {
        margin-bottom: 20px;
        padding: 15px;
        border: 1px solid #e8e8e8;
        border-radius: 4px;

        .question-header {
          display: flex;
          align-items: center;
          margin-bottom: 10px;

          .question-type {
            padding: 2px 6px;
            background: #1890ff !important;
            color: white;
            border-radius: 2px;
            font-size: 12px;
            margin-right: 10px;
          }

          .question-number {
            font-weight: bold;
            margin-right: 10px;
          }

          .question-tag {
            color: #faad14;
            font-size: 12px;
          }
        }

        .question-content {
          p {
            margin-bottom: 10px;
            font-size: 14px;
          }

          .options {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin: 10px 0;

            div {
              margin-bottom: 5px;
              padding: 5px;

              &:hover {
                background: #f0f7ff;
              }
            }
          }

          .correct-answer {
            padding: 5px 10px;
            background: #f6ffed;
            border: 1px solid #b7eb8f;
            color: #389e0d;
            font-size: 12px;
          }
        }
      }
    }

    .pagination {
      display: flex;
      justify-content: center;
      margin-top: 10px;
    }
  }
}

.wrong-answer {
  padding: 5px 10px;
  background: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
  font-size: 12px;
}

.submit-button {
  margin-top: 15px;
  display: flex;

  // justify-content: center;
  button {
    background: #1890ff !important;
    color: white;
    border-radius: 2px;
    font-size: 12px;
    padding: 5px 10px;

    &:hover {
      background: #40a9ff !important;
      color: white;
      cursor: pointer;
      transition: all .3s ease-in-out;
      border: none;
    }

    &:disabled {
      background: #40a9ff !important;
      color: white;
      cursor: not-allowed;
      border: none;
    }

    &:active {
      padding: 5px 10px;
      font-size: 12px;
    }
  }

  .button-and-answer {
    display: flex;
    gap: 10px;
  }


}

.sort-button {
  padding: 6px 12px;
  color: white;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: background-color 0.3s;
  margin-right: 10px;
  background-color: #ff4d4f !important;
}
.loading {
  text-align: center;
  padding: 20px;
  font-size: 16px;
  color: #666;
}
</style>
