<template>
  <div class="container">
    <div class="header">
      <div class="title">{{ title }}</div>
      <div class="control-box">
        <div class="czpwlfx-query">
        </div>

        <div class="btns-row">
          <a-button type="printer" icon="export" @click="handleExport">导出</a-button>
          <a-button type="printer" icon="printer" @click="handlePrint">打印</a-button>
        </div>
      </div>
    </div>
    <div class="table" id="summaryPrintTable">
      <div id="printHeader">
        <div>{{title}}</div>
        <!-- <div>{{month}}</div> -->
      </div>
      <a-table id="summaryPrintTableList" :rowClassName="customRowClassName" :columns="columns" :data-source="data" bordered :pagination="false" size="middle" :customRow="customTableRowStyle">
        <!-- <template slot="name" slot-scope="text, record">
            <div class="table-item-txt" v-if="text.indexOf('、') < 0" style="padding-left: 20px;">{{text}}</div>
            <div class="table-item-txt" v-else>{{text}}</div>
          </template> -->
      </a-table>
    </div>
    <!-- <iframe name="printFrame" width="0" height="0" frameborder="0" src="about:blank"></iframe> -->
  </div>
</template>
  
  <script lang="ts">
import moment from 'moment';
import { DatePicker, Empty, Tooltip } from '@h3/antd-vue';
import { Component, Vue, Provide } from 'vue-property-decorator';
import Api from '../ly-api';
import printHtml from '../utils/print';
import { exportExcel } from '../utils/file';

interface columnsParams {
  title: String;
  dataIndex: String;
  key: String;
  width: Number;
}

@Component({
  name: 'applicationSummary',
  components: {
    DatePicker,
  },
})
export default class applicationSummary extends Vue {
  data: any = [];
  month: string = '';
  title: string = '固定资产租赁台账汇总表';
  columns: any = [
    {
      title: '所属单位',
      dataIndex: 'ssdw',
      key: 'ssdw',
    },
    {
      title: '基层单位',
      dataIndex: 'jcdw',
      key: 'jcdw',
    },
    {
      title: '合同面积(㎡)',
      dataIndex: 'htmj',
      key: 'htmj',
    },
    {
      title: '合同租金(元)',
      dataIndex: 'htzj',
      key: 'htzj',
    },
    {
      title: '本年租金含税(元)',
      dataIndex: 'bnzjhs',
      key: 'bnzjhs',
    },
    {
      title: '本年租金不含税(元)',
      dataIndex: 'bnzjbhs',
      key: 'bnzjbhs',
    },
    {
      title: '租赁类别',
      dataIndex: 'lb',
      key: 'lb',
    },
    {
      title: '原值',
      dataIndex: 'yz',
      key: 'yz',
    },
    {
      title: '净额',
      dataIndex: 'je',
      key: 'je',
    },
    {
      title: '年折旧额',
      dataIndex: 'nzje',
      key: 'nzje',
    },
  ];

  created() {
    this.month = moment(new Date()).format('YYYY-MM');
    this.getData(this.month);
  }

  getData(m) {
    Api.getList({
      filters: [],
      mobile: false,
      page: 0,
      queryCode: 'cxgdzczltzhz_fzcx',
      schemaCode: 'cxgdzczltzhz_fzcx',
      size: 99999,
      queryVersion: 1,
      queryCondition: [
        [
          [
            {
              queryFilterType: 'Eq',
              propertyCode: 'ejdwmc',
              propertyType: 0,
              propertyValue: this.departmentName,
            },
          ],
        ],
      ],
      showTotal: true,
    }).then((res) => {
      if (res.errcode == 0) {
        let list = [];
        res.data.content.map((item) => {
          list.push({
            ...item.data,
          });
        });
        this.data = [...list].map((item) => {
          // 对象的每个键值对
          return Object.fromEntries(
            Object.entries(item).map(([key, value]) =>
              // 如果值为null，则替换为0
              [
                key,
                value === null
                  ? 0
                  : this.isNumberComprehensive(value)
                  ? this.formatMoney(value)
                  : value,
              ],
            ),
          );
        });
        console.log(
          '🚀 ~ applicationProject ~ getData ~ this.data:',
          this.data,
        );
      }
    });
  }

  get departmentName() {
    let dept = JSON.parse(
      window.sessionStorage.getItem('user'),
    ).parentDepartmentName;
    return dept.length ? dept.split('/')[1] : '';
  }

  handleMonthQuery(e) {
    this.month = moment(e).format('YYYY-MM');
    this.getData(this.month);
  }

  customTableRowStyle(record, index) {
    const style = {};
    console.log('🚀 ~ record:', record);
    if (record.xm == '租出小计' || record.xm == '租入小计') {
      style['background-color'] = '#faeadb';
    }
    if (record.xm == '房屋租出' || record.xm == '房屋租入') {
      style['background-color'] = '#e3dfeb';
    }
    return { className: 'table-row', style };
  }

  customRowClassName(record, index) {
    console.log('🚀 ~ applicationProject ~ customRowClassName ~ index:', index);
    console.log(
      '🚀 ~ applicationProject ~ customRowClassName ~ record:',
      record,
    );
    if (record.xm == '租出小计' || record.xm == '租入小计') {
      return 'czxj_crxj';
    }
    if (record.xm == '房屋租出' || record.xm == '房屋租入') {
      return 'fwcz_fwzr';
    }
  }

  handlePrint() {
    printHtml('summaryPrintTable', true);
  }

  /**
   * 格式化金额
   * @param {number} num - 金额数值
   * @returns {string} 格式化后的金额字符串已逗号分隔
   */
  formatMoney(num) {
    return num.toLocaleString();
  }

  /**
   * 检查提供的值是否为综合意义上的数字。
   * 该函数尝试将输入转换为数字，并确保转换后的值是有限的、非 NaN 的数字。
   * @param {any} value - 需要检查的值。
   * @returns {boolean} - 如果提供的值是综合意义上的数字，则返回 true；否则返回 false。
   */
  isNumberComprehensive(value) {
    const num = Number(value); // 尝试将输入转换为数字
    // 检查转换后的值是否为有限的、非 NaN 的数字
    return (
      typeof num === 'number' && // 确保转换后是数字类型
      isFinite(num) && // 排除 Infinity 和 -Infinity
      !isNaN(num) // 排除 NaN
    );
  }

  handleExport() {
    const titles = this.columns.map((item) => item.title);
    console.log('🚀 ~ applicationProject ~ handleExport ~ titles:', titles);
    console.log(
      '🚀 ~ applicationProject ~ handleExport ~ this.data:',
      this.data,
    );
    if (this.data.length > 0) {
      // const translatedData = this.data.map((item) => {
      //   // 使用 columns 配置动态构建转换逻辑
      //   return this.columns.reduce((acc, { title, dataIndex }) => {
      //     // 确保原始数据中有对应的 dataIndex
      //     if (item.hasOwnProperty(dataIndex)) {
      //       acc[title] = item[dataIndex];
      //     }
      //     return acc;
      //   }, {});
      // });
      const options = {
        data: document.querySelector('#summaryPrintTableList'),
        fileName: this.title + moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        columnsWidth: 120,
        columns: this.columns.length,
        style: {
          z: true,
          type: 'number',
        },
      };
      exportExcel(options);
    }
  }

  mounted() {}
}
</script>
  
  <style scoped>
.container {
  width: 100%;
  height: 100%;
  padding: 12px;
  position: relative;
}

.container .header {
  width: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;
  padding: 0 12px;
  position: relative;
}

.container .header .title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 18px 0;
  text-align: center;
}

.container .control-box {
  display: flex;
  justify-content: space-between;
}

.container .control-box .btns-row button {
  margin-left: 10px;
}

.container .table {
  position: absolute;
  top: 110px;
  bottom: 12px;
  left: 12px;
  right: 12px;
  box-sizing: border-box;
  background: #fff;
  padding: 12px;
  overflow-y: auto;
}

.container .table #printHeader {
  display: none;
}

.container .table :deep(.ant-table-tbody > tr > td) {
  padding: 10px 16px !important;
}

.container .table :deep(.czxj_crxj) {
  background-color: #faeadb;
}
.container .table :deep(.fwcz_fwzr) {
  background-color: #e3dfeb;
}
</style>