<template>
  <div class="container">
    <div class="header">
      <div class="title">{{ title }}</div>
      <div class="control-box">
        <div class="czpwlfx-query">
        </div>

        <div class="btns-row">
          <a-button type="printer" icon="export" @click="handleExport">导出</a-button>
          <a-button type="printer" icon="printer" @click="handlePrint">打印</a-button>
        </div>
      </div>
    </div>
    <div class="table" id="projectPrintTable">
      <div id="printHeader">
        <div>{{title}}</div>
        <!-- <div>{{month}}</div> -->
      </div>
      <a-table id="projectPrintTableList" :columns="columns" :data-source="data" bordered :pagination="false" size="middle">
      </a-table>
    </div>
  </div>
</template>
  
  <script lang="ts">
import moment from 'moment';
import { DatePicker, Empty, Tooltip } from '@h3/antd-vue';
import { Component, Vue, Provide } from 'vue-property-decorator';
import Api from '../../ly-api';
import printHtml from '../../utils/print';
import { exportExcel } from '../../utils/file';

interface columnsParams {
  title: String;
  dataIndex: String;
  key: String;
  width: Number;
}

@Component({
  name: 'zbjhTwoLevel',
  components: {
    DatePicker,
  },
})
export default class zbjhTwoLevel extends Vue {
  data: any = [];
  year: string = '';
  title: string = '装备计划二级单位汇总';
  columns: any = [
    {
      title: '年份',
      dataIndex: 'nf',
      key: 'nf',
    },
    {
      title: '单位',
      dataIndex: 'dw',
      key: 'dw',
    },
    {
      title: '计划数量',
      dataIndex: 'jhsl',
      key: 'jhsl',
    },
    {
      title: '计划金额',
      dataIndex: 'jhje',
      key: 'jhje',
    },
    {
      title: '控制数量审批',
      dataIndex: 'kzslsp',
      key: 'kzslsp',
    },
    {
      title: '限价审批',
      dataIndex: 'xjsp',
      key: 'xjsp',
    },
    {
      title: '合同数量',
      dataIndex: 'htsl',
      key: 'htsl',
    },
    {
      title: '合同金额',
      dataIndex: 'htje',
      key: 'htje',
    },
    {
      title: '完资数量',
      dataIndex: 'wzsl',
      key: 'wzsl',
    },
    {
      title: '完资比限价节约',
      dataIndex: 'wzbxjjy',
      key: 'wzbxjjy',
    },
    {
      title: '完资比计划节约',
      dataIndex: 'wzbjhjy',
      key: 'wzbjhjy',
    },
  ];

  isFirstInGroup(value, rowIndex) {
    // 简化逻辑，根据实际需求调整
    if (rowIndex === 0) return true; // 第一行总是显示
    return this.data[rowIndex - 1].nf !== value;
  }

  created() {
    this.year = moment(new Date()).format('YYYY');
    this.getData(this.year);
  }

  getData(m) {
    Api.getList({
      filters: [],
      mobile: false,
      page: 0,
      queryCode: 'zbejdw_zjb',
      schemaCode: 'zbejdw_zjb',
      size: 99999,
      queryVersion: 1,
      queryCondition: [
        [
          [
            {
              queryFilterType: 'Eq',
              propertyCode: 'nf',
              propertyType: 3,
              propertyValue: `${this.year}-01-01 00:00:00`,
            },
          ],
        ],
      ],
      showTotal: false,
    }).then((res) => {
      if (res.errcode == 0) {
        console.log('🚀 ~ zbjhTwoLevel ~ getData ~ res:', res);
        let list = [];
        res.data.content.map((item) => {
          item.data.Sheet1714015475169.map((child) => {
            list.push({
              ...child,
              nf: String(this.year),
            });
          });
        });
        this.data = list;
        this.columns[0].customRender = (text, record, index) => {
          return {
            children: this.isFirstInGroup(record.nf, index) ? text : null,
            attrs: {
              rowSpan: this.isFirstInGroup(record.nf, index) ? list.length : 0,
            },
          };
        };
        console.log(
          '🚀 ~ applicationProject ~ getData ~ this.data:',
          this.data,
        );
      }
    });
  }

  /**
   * 处理月份查询操作。
   * 当用户触发相关事件时，此函数会将事件所代表的日期格式化为'YYYY-MM'的形式，
   * 并以此形式的月份作为参数去获取相关数据。
   * @param {Object} e - 事件对象。预期为一个包含日期信息的事件，具体格式依据实际触发事件而定。
   * @returns {undefined} 该函数没有返回值。
   */
  // handleMonthQuery(e) {
  //   // 使用moment.js库将事件日期格式化为'YYYY-MM'格式，并更新month属性
  //   this.month = moment(e).format('YYYY-MM');
  //   // 调用getData方法，以当前month为参数进行数据查询
  //   this.getData(this.month);
  // }

  /**
   * 处理打印操作。
   * 调用 printHtml 函数，打印指定的 HTML 元素。
   * @param 无
   * @return 无
   */
  handlePrint() {
    printHtml('projectPrintTable', true);
  }

  /**
   * 格式化金额
   * @param {number} num - 金额数值
   * @returns {string} 格式化后的金额字符串已逗号分隔
   */
  formatMoney(num) {
    return num.toLocaleString();
  }

  /**
   * 检查提供的值是否为综合意义上的数字。
   * 该函数尝试将输入转换为数字，并确保转换后的值是有限的、非 NaN 的数字。
   * @param {any} value - 需要检查的值。
   * @returns {boolean} - 如果提供的值是综合意义上的数字，则返回 true；否则返回 false。
   */
  isNumberComprehensive(value) {
    const num = Number(value); // 尝试将输入转换为数字
    // 检查转换后的值是否为有限的、非 NaN 的数字
    return (
      typeof num === 'number' && // 确保转换后是数字类型
      isFinite(num) && // 排除 Infinity 和 -Infinity
      !isNaN(num) // 排除 NaN
    );
  }

  handleExport() {
    const titles = this.columns.map((item) => item.title);
    if (this.data.length > 0) {
      const options = {
        data: document.querySelector('#projectPrintTableList'),
        fileName: this.title + moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        columnsWidth: 120,
        columns: this.columns.length,
        style: {
          s: true,
          table: 'zbjh-two',
        },
      };
      exportExcel(options);
    }
  }

  mounted() {}
}
</script>
  
  <style scoped>
.container {
  width: 100%;
  height: 100%;
  padding: 12px;
  position: relative;
}

.container .header {
  width: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;
  padding: 0 12px;
  position: relative;
}

.container .header .title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 18px 0;
  text-align: center;
}

.container .control-box {
  display: flex;
  justify-content: space-between;
}

.container .control-box .btns-row button {
  margin-left: 10px;
}

.container .table {
  position: absolute;
  top: 110px;
  bottom: 12px;
  left: 12px;
  right: 12px;
  box-sizing: border-box;
  background: #fff;
  padding: 12px;
  overflow-y: auto;
}

.container .table #printHeader {
  display: none;
}

.container .table :deep(.ant-table-tbody > tr > td) {
  padding: 10px 16px !important;
}
</style>