<template>
  <div class="weather-risk-demo">
    <div class="demo-container">
      <div class="demo-header">
        <h1>天气风险预警系统演示</h1>
        <p>科技感大屏组件展示</p>
      </div>
      
      <div class="demo-content">
        <DatavWeatherRisk />
      </div>
      
      <div class="demo-controls">
        <button @click="refreshData" class="refresh-btn">刷新数据</button>
        <button @click="toggleFullscreen" class="fullscreen-btn">全屏模式</button>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Vue, Component } from 'vue-property-decorator'
import DatavWeatherRisk from '../components/datav-weather-risk.vue'

@Component({
  name: 'WeatherRiskDemo',
  components: {
    DatavWeatherRisk
  }
})
export default class WeatherRiskDemo extends Vue {
  
  refreshData() {
    // 刷新组件数据
    this.$forceUpdate()
  }
  
  toggleFullscreen() {
    if (!document.fullscreenElement) {
      document.documentElement.requestFullscreen()
    } else {
      document.exitFullscreen()
    }
  }
}
</script>

<style lang="less" scoped>
.weather-risk-demo {
  width: 100vw;
  height: 100vh;
  background: #00065b url(../assets/images/datav-bg.jpg) no-repeat center center;
  background-size: cover;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-sizing: border-box;
  
  .demo-container {
    width: 100%;
    max-width: 1200px;
    height: 100%;
    max-height: 800px;
    display: flex;
    flex-direction: column;
    background: rgba(0, 10, 30, 0.8);
    border: 2px solid rgba(0, 195, 255, 0.3);
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 0 30px rgba(0, 195, 255, 0.2);
    backdrop-filter: blur(10px);
  }
  
  .demo-header {
    padding: 20px;
    text-align: center;
    background: linear-gradient(135deg, rgba(0, 50, 100, 0.8), rgba(0, 80, 150, 0.6));
    border-bottom: 1px solid rgba(0, 195, 255, 0.3);
    
    h1 {
      color: #00ffff;
      font-size: 28px;
      margin: 0 0 10px 0;
      text-shadow: 0 0 15px rgba(0, 255, 255, 0.6);
      letter-spacing: 2px;
    }
    
    p {
      color: rgba(255, 255, 255, 0.8);
      font-size: 14px;
      margin: 0;
    }
  }
  
  .demo-content {
    flex: 1;
    position: relative;
    overflow: hidden;
  }
  
  .demo-controls {
    padding: 15px 20px;
    display: flex;
    justify-content: center;
    gap: 15px;
    background: linear-gradient(135deg, rgba(0, 30, 60, 0.8), rgba(0, 50, 100, 0.6));
    border-top: 1px solid rgba(0, 195, 255, 0.3);
    
    button {
      padding: 8px 20px;
      background: linear-gradient(135deg, #00c3ff, #0080ff);
      border: none;
      border-radius: 6px;
      color: #fff;
      font-size: 14px;
      font-weight: bold;
      cursor: pointer;
      transition: all 0.3s ease;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
      
      &:hover {
        background: linear-gradient(135deg, #00ffff, #00c3ff);
        box-shadow: 0 0 15px rgba(0, 255, 255, 0.5);
        transform: translateY(-2px);
      }
      
      &:active {
        transform: translateY(0);
      }
    }
  }
}

// 全屏模式样式
:global(.weather-risk-demo:fullscreen) {
  .demo-container {
    max-width: none;
    max-height: none;
    border-radius: 0;
  }
}
</style>
