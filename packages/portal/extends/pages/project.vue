<template>
  <div class="container">
    <div class="header">
      <div class="title">{{ title }}</div>
      <div class="control-box">
        <div class="czpwlfx-query"></div>

        <div class="btns-row">
          <a-button type="printer" icon="export" @click="handleExport"
            >导出</a-button
          >
          <a-button type="printer" icon="printer" @click="handlePrint"
            >打印</a-button
          >
        </div>
      </div>
    </div>
    <div class="table" id="projectPrintTable">
      <div id="printHeader">
        <div>{{ title }}</div>
        <!-- <div>{{month}}</div> -->
      </div>
      <a-table
        id="projectPrintTableList"
        :rowClassName="customRowClassName"
        :columns="columns"
        :data-source="data"
        bordered
        :pagination="false"
        size="middle"
        :customRow="customTableRowStyle"
      >
        <!-- <template slot="name" slot-scope="text, record">
          <div class="table-item-txt" v-if="text.indexOf('、') < 0" style="padding-left: 20px;">{{text}}</div>
          <div class="table-item-txt" v-else>{{text}}</div>
        </template> -->
      </a-table>
    </div>
    <!-- <iframe name="printFrame" width="0" height="0" frameborder="0" src="about:blank"></iframe> -->
  </div>
</template>

<script lang="ts">
import moment from 'moment';
import { DatePicker, Empty, Tooltip } from '@h3/antd-vue';
import { Component, Vue, Provide } from 'vue-property-decorator';
import Api from '../ly-api';
import printHtml from '../utils/print';
import { exportExcel } from '../utils/file';

interface columnsParams {
  title: String;
  dataIndex: String;
  key: String;
  width: Number;
}

@Component({
  name: 'applicationProject',
  components: {
    DatePicker,
  },
})
export default class applicationProject extends Vue {
  data: any = [];
  month: string = '';
  title: string = '固定资产租赁项目统计表';
  columns: any = [
    {
      title: '项目名称',
      dataIndex: 'xm',
      key: 'xm',
    },
    {
      title: '租赁项数',
      dataIndex: 'zlxs',
      key: 'zlxs',
    },
    {
      title: '合同租金',
      dataIndex: 'htzj',
      key: 'htzj',
    },
    {
      title: '本年租金含税',
      dataIndex: 'bnzjhs',
      key: 'bnzjhs',
    },
    {
      title: '本年租金不含税',
      dataIndex: 'bnzbjhs',
      key: 'bnzbjhs',
    },
    {
      title: '房屋面积',
      dataIndex: 'fwmj',
      key: 'fwmj',
    },
    {
      title: '原值',
      dataIndex: 'yz',
      key: 'fwdj',
    },
    {
      title: '净额',
      dataIndex: 'je',
      key: 'fwzj',
    },
    {
      title: '年折旧',
      dataIndex: 'nzje',
      key: 'nzje',
    },
    {
      title: '备注',
      dataIndex: 'bz',
      key: 'bz',
    },
  ];

  queryParams = {
    filters: [],
    mobile: false,
    page: 0,
    queryCode: '',
    schemaCode: '',
    size: 99999,
    queryVersion: 1,
    queryCondition: [],
    showTotal: false,
  };

  paramCode = [
    { queryCode: 'sbzczltz', schemaCode: 'sbtz_zc' },
    { queryCode: 'zltz', schemaCode: 'fwzc_zc' },
    { queryCode: 'sbzczltz', schemaCode: 'sbtz_zr' },
    { queryCode: 'zltz', schemaCode: 'fwzr' },
  ];

  created() {
    this.month = moment(new Date()).format('YYYY-MM');
    this.getData(this.month);
  }

  getData(m) {
    // console.log(this.paramCode);
    // // 根据paramCode数组 for 进行请求
    // const apiList = this.paramCode.map(async (item) => {
    //   return await Api.getList(
    //     Object.assign(this.queryParams, { queryCode:item.queryCode, schemaCode: item.schemaCode }),
    //   );
    // });

    // Promise.all(apiList).then((res) => {
    //   console.log('res', res);
    // });

    Api.getList({
      filters: [],
      mobile: false,
      page: 0,
      queryCode: 'gdzczltj_fzcx',
      schemaCode: 'gdzczltj_fzcx',
      size: 99999,
      queryVersion: 1,
      queryCondition: [
        [
          [
            {
              queryFilterType: 'Eq',
              propertyCode: 'ejdwmc',
              propertyType: 0,
              propertyValue: this.departmentName,
            },
          ],
        ],
      ],
      showTotal: false,
    }).then((res) => {
      if (res.errcode == 0) {
        let list = [];
        res.data.content.map((item) => {
          list.push({
            ...item.data,
          });
        });
        this.data = [...list].map((item) => {
          // 对象的每个键值对
          return Object.fromEntries(
            Object.entries(item).map(([key, value]) =>
              // 如果值为null，则替换为0
              [
                key,
                value === null
                  ? 0
                  : this.isNumberComprehensive(value)
                  ? this.formatMoney(value)
                  : value,
              ],
            ),
          );
        });
        console.log(
          '🚀 ~ applicationProject ~ getData ~ this.data:',
          this.data,
        );
      }
    });
  }

  /**
   * 获取当前用户的部门名称
   * 该getter不接受任何参数
   * @returns {string} 返回从sessionStorage中获取的当前用户所属部门的名称
   */
  get departmentName() {
    // 从sessionStorage中解析并返回用户信息中的部门名称
    let dept = JSON.parse(
      window.sessionStorage.getItem('user'),
    ).parentDepartmentName;
    return dept.length ? dept.split('/')[1] : '';
  }

  /**
   * 处理月份查询操作。
   * 当用户触发相关事件时，此函数会将事件所代表的日期格式化为'YYYY-MM'的形式，
   * 并以此形式的月份作为参数去获取相关数据。
   * @param {Object} e - 事件对象。预期为一个包含日期信息的事件，具体格式依据实际触发事件而定。
   * @returns {undefined} 该函数没有返回值。
   */
  handleMonthQuery(e) {
    // 使用moment.js库将事件日期格式化为'YYYY-MM'格式，并更新month属性
    this.month = moment(e).format('YYYY-MM');
    // 调用getData方法，以当前month为参数进行数据查询
    this.getData(this.month);
  }

  /**
   * 根据记录和索引自定义表格行的样式。
   * @param {Object} record 表格中的记录对象，包含各种字段，如‘xm’。
   * @param {number} index 表格中行的索引。
   * @returns {Object} 返回一个包含 className 和 style 的对象。style 对象用于设置行的样式，基于记录的‘xm’字段的不同值，设置不同的背景颜色。
   */
  customTableRowStyle(record, index) {
    const style = {};
    // 为租出或租入小计设置特定背景色
    if (record.xm == '租出小计' || record.xm == '租入小计')
      style['background-color'] = '#faeadb';
    // 为房屋租出或租入设置特定背景色
    if (record.xm == '房屋租出' || record.xm == '房屋租入')
      style['background-color'] = '#e3dfeb';
      if (record.xm == '设备租出' || record.xm == '设备租入')
      style['background-color'] = '#e3dfeb';
    return { className: 'table-row', style };
  }

  /**
   * 根据记录和索引为表格行提供自定义类名
   * @param {Object} record - 行记录，包含各种数据字段
   * @param {number} index - 行索引
   * @returns {string|undefined} 返回一个字符串作为自定义类名，如果不满足条件则返回undefined
   */
  customRowClassName(record, index) {
    // 判断记录是否为特定的租出或租入小计，如果是则应用特定类名
    if (record.xm == '租出小计' || record.xm == '租入小计') return 'czxj_crxj';
    // 判断记录是否为特定的房屋租出或租入，如果是则应用另一个特定类名
    if (record.xm == '房屋租出' || record.xm == '房屋租入') return 'fwcz_fwzr';
  }

  /**
   * 处理打印操作。
   * 调用 printHtml 函数，打印指定的 HTML 元素。
   * @param 无
   * @return 无
   */
  handlePrint() {
    printHtml('projectPrintTable', true);
  }

  /**
   * 格式化金额
   * @param {number} num - 金额数值
   * @returns {string} 格式化后的金额字符串已逗号分隔
   */
  formatMoney(num) {
    return num.toLocaleString();
  }

  /**
   * 检查提供的值是否为综合意义上的数字。
   * 该函数尝试将输入转换为数字，并确保转换后的值是有限的、非 NaN 的数字。
   * @param {any} value - 需要检查的值。
   * @returns {boolean} - 如果提供的值是综合意义上的数字，则返回 true；否则返回 false。
   */
  isNumberComprehensive(value) {
    const num = Number(value); // 尝试将输入转换为数字
    // 检查转换后的值是否为有限的、非 NaN 的数字
    return (
      typeof num === 'number' && // 确保转换后是数字类型
      isFinite(num) && // 排除 Infinity 和 -Infinity
      !isNaN(num) // 排除 NaN
    );
  }

  handleExport() {
    const titles = this.columns.map((item) => item.title);
    if (this.data.length > 0) {
      // const translatedData = this.data.map((item) => {
      //   if (item)
      //     // 使用 columns 配置动态构建转换逻辑
      //     return this.columns.reduce((acc, { title, dataIndex }) => {
      //       // 确保原始数据中有对应的 dataIndex
      //       if (item.hasOwnProperty(dataIndex)) {
      //         acc[title] = item[dataIndex];
      //       }
      //       return acc;
      //     }, {});
      // });

      const options = {
        data: document.querySelector('#projectPrintTableList'),
        fileName: this.title + moment(new Date()).format('YYYY-MM-DD HH:mm:ss'),
        columnsWidth: 120,
        columns: this.columns.length,
        style: {
          s: true,
          z: true,
          type: 'number',
        },
      };

      exportExcel(options);
    }
  }

  mounted() {}
}
</script>

<style scoped>
.container {
  width: 100%;
  height: 100%;
  padding: 12px;
  position: relative;
}

.container .header {
  width: 100%;
  background: #fff;
  display: flex;
  flex-direction: column;
  padding: 0 12px;
  position: relative;
}

.container .header .title {
  font-size: 20px;
  font-weight: 600;
  color: #333;
  margin: 18px 0;
  text-align: center;
}

.container .control-box {
  display: flex;
  justify-content: space-between;
}

.container .control-box .btns-row button {
  margin-left: 10px;
}

.container .table {
  position: absolute;
  top: 110px;
  bottom: 12px;
  left: 12px;
  right: 12px;
  box-sizing: border-box;
  background: #fff;
  padding: 12px;
  overflow-y: auto;
}

.container .table #printHeader {
  display: none;
}

.container .table :deep(.ant-table-tbody > tr > td) {
  padding: 10px 16px !important;
}

.container .table :deep(.czxj_crxj) {
  background-color: #faeadb;
}
.container .table :deep(.fwcz_fwzr) {
  background-color: #e3dfeb;
}
</style>
