<template>
  <div class="datav-container" ref="datavContainer">
    <dv-loading v-if="loading" style="color: #fff">数据加载中...</dv-loading>
    <div class="header-title">龙丰掌上安全</div>
    <!-- <div class="header-nav">
      <ADatavNav @nav-click="handleNavClick" :is-fullscreen="isFullscreen" />
    </div> -->
    <div class="datav-content">
      <div class="datav-content-left">
        <div class="datav-content-left-item">
          <dv-border-box-12
            
            title="开发工程"
            :key="componentKey"
          >
            <div class="chart-container" id="chart1"></div>
          </dv-border-box-12>
        </div>
        <div class="datav-content-left-progress-item">
          <dv-border-box-12
            
            title="施工进度"
            :key="componentKey"
          >
            <div class="chart-container" id="chart2"></div>
            <div class="tabs-container">
              <div
                v-for="(tab, index) in progressTabs"
                :key="index"
                class="tab-item"
                :class="{ 'tab-active': activeProgressTab == index }"
                @click="handleProgressTabClick(index)"
              >
                {{ tab.name }}
              </div>
            </div>
          </dv-border-box-12>
        </div>
        <div class="datav-content-left-check-item">
          <dv-border-box-12
            
            title="安全检查"
            :key="componentKey"
          >
            <div class="chart-container" id="chart3"></div>
            <div class="tabs-container">
              <div
                v-for="(tab, index) in checkTabs"
                :key="index"
                class="tab-item"
                :class="{ 'tab-active': activeCheckTab == index }"
                @click="handleCheckTabClick(index)"
              >
                {{ tab.name }}
              </div>
            </div>
          </dv-border-box-12>
        </div>
      </div>
      <div class="datav-content-center">
        <div class="datav-content-center-item">
          <dv-border-box-12
            
            title="安全数字"
            :key="componentKey"
          >
            <ADatavSafetyDigital />
          </dv-border-box-12>
        </div>
        <div class="datav-content-center-double-item">
          <div class="datav-content-center-double-item-left">
            <dv-border-box-12
              
              title="重要通知"
              :key="componentKey"
            >
              <div class="datav-content-center-double-item-left-content">
                <dv-scroll-board
                  :key="componentKey"
                  :config="importantNotice"
                  @click="handleImportantNoticeClick"
                />
              </div>
            </dv-border-box-12>
          </div>
          <div class="datav-content-center-double-item-right">
            <dv-border-box-12
              
              title="月度监督考核报告"
              :key="componentKey"
            >
              <div class="datav-content-center-double-item-right-content">
                <dv-scroll-board :key="componentKey" :config="config" />
              </div>
            </dv-border-box-12>
          </div>
        </div>
      </div>
      <div class="datav-content-right">
        <div class="datav-content-right-item">
          <dv-border-box-12
            
            title="天气风险预警"
            :key="componentKey"
          >
            <ADatavWeatherRisk />
          </dv-border-box-12>
        </div>
        <div class="datav-content-right-item">
          <dv-border-box-12
            
            title="安全案例警示"
            :key="componentKey"
          >
            <div class="datav-content-right-item-content">
              <dv-scroll-board :key="componentKey" :config="config1" />
            </div>
          </dv-border-box-12>
        </div>
        <div class="datav-content-right-exercise-item">
          <dv-border-box-12
            
            title="综合管理"
            :key="componentKey"
          >
            <ADatavExercise />
            <div class="tabs-container">
              <div
                v-for="(tab, index) in exerciseTabs"
                :key="index"
                class="tab-item"
                :class="{ 'tab-active': activeProgressTab == index }"
                @click="handleProgressTabClick(index)"
              >
                {{ tab.name }}
              </div>
            </div>
          </dv-border-box-12>
        </div>
      </div>
    </div>
    <ADatavModal
      :visible="importantNoticeModalVisible"
      :title="importantNoticeTitle"
      :showOk="false"
      :width="'60%'"
      @ok="handleModalOk"
      @cancel="handleModalOk"
    >
      <ADatavNoticeDetail
        :noticeData="importantNoticeData"
        
      />
    </ADatavModal>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Provide } from 'vue-property-decorator';
import * as echarts from 'echarts';
import moment from 'moment';
import { initCharts } from './chart.js';
import lyApi from '../../ly-api';
import { getListParams, loadFormParams } from '../../utils/params';
import DatavSafetyDigital from '../../components/datav-safety-digital.vue';
import DatavSubTitle from '../../components/datav-sub-title.vue';
import DatavNav from '../../components/datav-nav.vue';
import DatavWeatherRisk from '../../components/datav-weather-risk.vue';
import DatavExercise from '../../components/datav-exercise.vue';
import DatavModal from '../../components/datav-modal.vue';
import DatavNoticeDetail from '../../components/datav-notice-detail.vue';
import dataV from '@jiaminghi/data-view';
Vue.use(dataV);
@Component({
  name: 'Datav',
  components: {
    ADatavSafetyDigital: DatavSafetyDigital,
    ADatavSubTitle: DatavSubTitle,
    ADatavNav: DatavNav,
    ADatavWeatherRisk: DatavWeatherRisk,
    ADatavExercise: DatavExercise,
    ADatavModal: DatavModal,
    ADatavNoticeDetail: DatavNoticeDetail,
  },
})
export default class Datav extends Vue {
  isFullscreen = false;
  activeProgressTab = 0;
  activeCheckTab = 0;
  resizeObserver: any = null;
  componentKey: number = 1;

  // 重要通知
  importantNoticeModalVisible = false;
  importantNoticeTitle = '重要通知';
  importantNoticeData: any = {};

  // 施工进度
  progressTabs = [
    { name: '危险运输', data: [65, 50, 75, 30] },
    { name: '特种设备', data: [45, 70, 55, 60] },
    { name: '危险片', data: [80, 40, 65, 35] },
    { name: '培训', data: [55, 65, 45, 75] },
  ];

  // 安全检查
  checkTabs = [
    { name: '红黑榜', data: [65, 50, 75, 30] },
    { name: '承包商评估', data: [45, 70, 55, 60] },
  ];

  // 综合管理
  exerciseTabs = [
    { name: 'HES检查计划', data: [65, 50, 75, 30] },
    { name: '培训计划', data: [45, 70, 55, 60] },
    { name: '应急演练', data: [80, 40, 65, 35] },
    { name: '其他', data: [55, 65, 45, 75] },
  ];
  charts: any[] = [];
  // 重要通知
  importantNotice: any = {
    data: [],
    rowNum: 8,
    oddRowBGC: 'rgba(3, 37, 108, 0.4)',
    evenRowBGC: 'rgba(1, 19, 56, 0.7)',
    columnWidth: [190, 110],
    oldData: [],
  };
  loading: boolean = true;

  config: any = {
    data: [
      ['张三', '2025-06-01', '20'],
      ['李四', '2025-06-01', '20'],
      ['王五', '2025-06-01', '20'],
      ['赵六', '2025-06-01', '20'],
      ['孙七', '2025-06-01', '20'],
      ['周八', '2025-06-01', '20'],
      ['吴九', '2025-06-01', '20'],
      ['孙七', '2025-06-01', '20'],
      ['周八', '2025-06-01', '20'],
      ['吴九', '2025-06-01', '20'],
    ],
    rowNum: 8,
    oddRowBGC: 'rgba(3, 37, 108, 0.4)',
    evenRowBGC: 'rgba(1, 19, 56, 0.7)',
  };

  config1: any = {
    data: [
      ['安全检查', '2025-06-01', '20'],
      ['安全检查', '2025-06-01', '20'],
      ['安全检查', '2025-06-01', '20'],
      ['安全检查', '2025-06-01', '20'],
      ['安全检查', '2025-06-01', '20'],
      ['安全检查', '2025-06-01', '20'],
      ['安全检查', '2025-06-01', '20'],
      ['安全检查', '2025-06-01', '20'],
      ['安全检查', '2025-06-01', '20'],
    ],
    rowNum: 6,
    oddRowBGC: 'rgba(3, 37, 108, 0.4)',
    evenRowBGC: 'rgba(1, 19, 56, 0.7)',
  };

  get _window() {
    return window as any;
  }

  get _document() {
    return this._window.document as any;
  }

  handleNavClick(index: number) {
    if (index == 4) {
      // 完成网页全屏显示
      this.windowFullscreen();
      this.handleResize();
    }
  }

  windowFullscreen() {
    this.isFullscreen = !this.isFullscreen;
    const container = this.$refs.datavContainer as any;

    if (!this._document.fullscreenElement) {
      // 进入全屏模式，针对容器元素
      if (container.requestFullscreen) {
        container.requestFullscreen();
      } else if (container.webkitRequestFullscreen) {
        container.webkitRequestFullscreen();
      } else if (container.msRequestFullscreen) {
        container.msRequestFullscreen();
      }
    } else {
      // 退出全屏模式
      if (this._document.exitFullscreen) {
        this._document.exitFullscreen();
      } else if (this._document.webkitExitFullscreen) {
        this._document.webkitExitFullscreen();
      } else if (this._document.msExitFullscreen) {
        this._document.msExitFullscreen();
      }
    }
  }

  handleProgressTabClick(index: number) {
    this.activeProgressTab = index;
    // 更新图表数据
    if (this.charts[1]) {
      const option = this.charts[1].getOption();
      option.series[0].data = this.progressTabs[index].data;
      this.charts[1].setOption(option);
    }
  }

  handleCheckTabClick(index: number) {
    this.activeCheckTab = index;
    if (this.charts[2]) {
      const option = this.charts[2].getOption();
      option.series[0].data = this.checkTabs[index].data;
      this.charts[2].setOption(option);
    }
  }

  // 添加新方法用于初始化所有图表
  initAllCharts() {
    this.charts = initCharts(this.progressTabs[this.activeProgressTab].data);
  }

  // 添加新方法用于重新初始化图表
  reInitCharts() {
    // 先清除所有旧图表
    this.charts.forEach((chart) => {
      chart.dispose();
    });

    // 延迟一点时间再初始化，确保DOM已经完全更新
    setTimeout(() => {
      this.initAllCharts();
    }, 100);
  }

  handleResize() {
    const asideContainer =
      this._window.document.querySelector('.aside-container');
    if (asideContainer) {
      const resizeObserver = new ResizeObserver((entries) => {
        for (const entry of entries) {
          const newWidth = entry.contentRect.width;
          // 使用$nextTick确保DOM已更新
          this.$nextTick(() => {
            // 强制刷新DataV组件
            // 更新componentKey强制刷新DataV组件
            this.componentKey += 1;

            // 在DOM更新后重新初始化图表
            this.$nextTick(() => {
              this.reInitCharts();
            });
          });
        }
      });

      resizeObserver.observe(asideContainer);
      this.resizeObserver = resizeObserver;
    }
  }

  // 获取重要通知
  async getImportantNotice() {
    const params = getListParams('wjgl');
    const { errcode, data } = await lyApi.getList(params);
    if (errcode == 0) {
      this.importantNotice.data = data.content.map((item: any) => {
        return [item.data.bt, moment(item.data.rq).format('YYYY-MM-DD')];
      });
      this.importantNotice.oldData = data.content;
      this.importantNotice = this.importantNotice;
      this.handleResize();
      console.log(
        '🚀 ~ Datav ~ getImportantNotice ~ importantNotice:',
        this.importantNotice,
      );
    }
  }

  /**
   * 重要通知点击
   * @param row 行数据
   */
  async handleImportantNoticeClick(row: any) {
    const item = this.importantNotice.oldData[row.rowIndex];
    console.log('🚀 ~ Datav ~ handleImportantNoticeClick ~ item:', item);
    if (item) {
      const params = loadFormParams('wjgl', { objectId: item.id });
      const { errcode, data } = await lyApi.loadForm(params);
      if (errcode == 0) {
        this.importantNoticeData = data.bizObject.data;
        this.importantNoticeModalVisible = true;
      }
    }
  }

  handleModalOk() {
    this.importantNoticeModalVisible = false;
  }

  mounted() {
    this.$nextTick(() => {
      // 初始化所有图表
      this.initAllCharts();
      // 窗口大小变化时重新调整图表大小
      this._window.addEventListener('resize', () => {
        this.charts.forEach((chart) => {
          chart.resize();
        });
        // 等待DOM更新后再强制更新border box
        this.componentKey += 1;

        this.$nextTick(() => {
          this.reInitCharts();
        });
      });

      this.handleResize();

      // 获取重要通知
      this.getImportantNotice();
    });

    setTimeout(() => {
      this.loading = false;
    }, 1000);
  }

  beforeDestroy() {
    this.charts.forEach((chart) => {
      chart.dispose();
    });

    if (this.resizeObserver) {
      this.resizeObserver.disconnect();
    }
  }
}
</script>

<style scoped lang="less">
.datav-container-fullscreen {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.datav-container {
  width: 100%;
  height: 100%;
  background: #00065b url(../../assets/images/bg.jpg) no-repeat top center;
  background-size: 100% 100%;
  background-repeat: no-repeat;
  transition: all 0.3s ease;

  :deep(.dv-border-box-12-title) {
    font-size: 18px;
    color: #fff;
    letter-spacing: 2px;
    font-weight: bold;
  }

  .header-title {
    width: 100%;
    height: 8%;
    font-weight: bold;
    letter-spacing: 4px;
    font-size: 32px;
    display: flex;
    justify-content: center;
    align-items: center;
    color: #fff;
    // animation: titleGlow 2s infinite alternate;
    text-shadow: 0 0 10px rgba(0, 195, 255, 0.7);
    background-image: url(../../assets/images/head_bg.png);
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  // @keyframes titleGlow {
  //   from {
  //     text-shadow: 0 0 5px rgba(0, 195, 255, 0.7);
  //   }
  //   to {
  //     text-shadow: 0 0 20px rgba(0, 195, 255, 0.9),
  //       0 0 30px rgba(0, 195, 255, 0.5);
  //   }
  // }

  .header-nav {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    padding: 0 84px;
    font-size: 14px;
    color: #fff;
    margin-top: -2%;
  }

  .datav-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 44px;
    height: 87.5%;
    margin-top: 0.5%;
    gap: 8px;

    &-left {
      width: 30%;
      height: 100%;
      display: flex;
      flex-direction: column;

      &-item {
        width: 100%;
        height: 33.33%;
        display: flex;
        flex-direction: column;
      }
      &-progress-item,
      &-check-item {
        width: 100%;
        height: 33.33%;
        display: flex;

        :deep(.border-box-content) {
          display: flex;
        }

        :deep(.dv-border-box-12) {
          padding: 60px 24px 14px 24px;
        }
      }

      .chart-container {
        width: 100%;
        height: 100%;
        box-sizing: border-box;
      }
    }

    &-center {
      width: 40%;
      height: 100%;
      display: flex;
      flex-direction: column;

      &-item {
        width: 100%;
        height: 46%;
        display: flex;
        flex-direction: column;
      }
      &-double-item {
        width: 100%;
        height: 54%;
        display: flex;
        gap: 12px;

        &-left {
          width: 50%;
          height: 100%;

          &-content {
            width: 100%;
            height: 100%;
            padding: 60px 20px 20px 20px;
            border-radius: 10px;

            :deep(.rows) {
              border-radius: 0;

              // 使用clip-path创建斜角效果
              clip-path: polygon(
                10px 0,
                /* 左上角斜角 */ calc(100% - 10px) 0,
                /* 右上角斜角 */ 100% 10px,
                /* 右上角斜角 */ 100% calc(100% - 10px),
                /* 右下角斜角 */ calc(100% - 10px) 100%,
                /* 右下角斜角 */ 10px 100%,
                /* 左下角斜角 */ 0 calc(100% - 10px),
                /* 左下角斜角 */ 0 10px /* 左上角斜角 */
              );
              cursor: pointer;
            }
          }
        }
        &-right {
          width: 50%;
          height: 100%;

          &-content {
            width: 100%;
            height: 100%;
            padding: 60px 20px 20px 20px;
            border-radius: 10px;

            :deep(.rows) {
              border-radius: 0;

              // 使用clip-path创建斜角效果
              clip-path: polygon(
                10px 0,
                /* 左上角斜角 */ calc(100% - 10px) 0,
                /* 右上角斜角 */ 100% 10px,
                /* 右上角斜角 */ 100% calc(100% - 10px),
                /* 右下角斜角 */ calc(100% - 10px) 100%,
                /* 右下角斜角 */ 10px 100%,
                /* 左下角斜角 */ 0 calc(100% - 10px),
                /* 左下角斜角 */ 0 10px /* 左上角斜角 */
              );
            }
          }
        }
      }
    }

    &-right {
      width: 30%;
      height: 100%;
      display: flex;
      flex-direction: column;

      &-item {
        width: 100%;
        height: 33.33%;
        display: flex;
        flex-direction: column;

        &-content {
          width: 100%;
          height: 100%;
          padding: 60px 20px 20px 20px;
          border-radius: 10px;

          :deep(.rows) {
            border-radius: 0;

            // 使用clip-path创建斜角效果
            clip-path: polygon(
              10px 0,
              /* 左上角斜角 */ calc(100% - 10px) 0,
              /* 右上角斜角 */ 100% 10px,
              /* 右上角斜角 */ 100% calc(100% - 10px),
              /* 右下角斜角 */ calc(100% - 10px) 100%,
              /* 右下角斜角 */ 10px 100%,
              /* 左下角斜角 */ 0 calc(100% - 10px),
              /* 左下角斜角 */ 0 10px /* 左上角斜角 */
            );
          }
        }
      }

      &-exercise-item {
        width: 100%;
        height: 33.33%;
        display: flex;

        :deep(.border-box-content) {
          display: flex;
        }

        :deep(.dv-border-box-12) {
          padding: 60px 24px 14px 24px;
        }
      }
    }
  }

  .tabs-container {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-direction: column;
    gap: 10px;
    box-sizing: border-box;
    width: 20%;
  }

  .tab-item {
    padding: 4px 6px;
    background: rgba(0, 65, 210, 0.3);
    border: 1px solid rgba(0, 195, 255, 0.3);
    border-radius: 4px;
    color: rgba(255, 255, 255, 0.7);
    font-size: 12px;
    cursor: pointer;
    transition: all 0.3s ease;
    width: 100%;
    text-align: center;
  }

  .tab-active {
    background: rgba(0, 195, 255, 0.3);
    border: 1px solid #00c3ff;
    color: #fff;
    box-shadow: 0 0 8px rgba(0, 195, 255, 0.5);
  }
}
</style>
