import * as echarts from 'echarts';

/**
 * 开发工程饼图配置
 */
export const getChart1Option = () => {
  return {
    color: ['#00c3ff', '#0082ff', '#005aff', '#2b43ff'],
    backgroundColor: 'transparent',
    tooltip: {
      trigger: 'item',
    },
    series: [
      {
        name: '开发工程',
        type: 'pie',
        radius: '50%',
        center: ['50%', '56%'],
        data: [
          { value: 35, name: '土建工程' },
          { value: 25, name: '电气工程' },
          { value: 20, name: '管道工程' },
          { value: 20, name: '设备安装' },
        ],
        itemStyle: {
          emphasis: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 195, 255, 0.5)',
          },
        },
        label: {
          color: '#fff',
        },
      },
    ],
  };
};

/**
 * 施工进度柱状图配置
 */
export const getChart2Option = (data) => {
  return {
    backgroundColor: 'transparent',
    grid: {
      left: '6%',
      right: '4%',
      top: '6%',
      bottom: '10%',
    },
    xAxis: {
      type: 'category',
      data: ['一区', '二区', '三区', '四区'],
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
        },
      },
      axisLabel: {
        color: '#fff',
        fontSize: 10,
      },
    },
    yAxis: {
      type: 'value',
      max: 100,
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#fff',
        fontSize: 10,
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
    },
    series: [
      {
        data: data,
        type: 'bar',
        barWidth: '40%',
        itemStyle: {
          normal: {
            color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
              { offset: 0, color: '#00c3ff' },
              { offset: 1, color: '#0041d2' },
            ]),
          },
        },
      },
    ],
  };
};

/**
 * 安全检查折线图配置
 */
export const getChart3Option = () => {
  return {
    backgroundColor: 'transparent',
    grid: {
      left: '6%',
      right: '4%',
      top: '6%',
      bottom: '10%',
    },
    xAxis: {
      type: 'category',
      data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
      axisLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.3)',
        },
      },
      axisLabel: {
        color: '#fff',
        fontSize: 10,
      },
    },
    yAxis: {
      type: 'value',
      axisLine: {
        show: false,
      },
      axisTick: {
        show: false,
      },
      axisLabel: {
        color: '#fff',
        fontSize: 10,
      },
      splitLine: {
        lineStyle: {
          color: 'rgba(255, 255, 255, 0.1)',
        },
      },
    },
    series: [
      {
        data: [8, 12, 5, 15, 10, 6, 9],
        type: 'line',
        smooth: true,
        symbol: 'circle',
        symbolSize: 8,
        itemStyle: {
          color: '#00c3ff',
        },
        lineStyle: {
          color: '#00c3ff',
          width: 2,
        },
        areaStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: 'rgba(0, 195, 255, 0.5)' },
            { offset: 1, color: 'rgba(0, 195, 255, 0.1)' },
          ]),
        },
      },
    ],
  };
};

/**
 * 初始化图表
 */
export const initCharts = (progressTabData) => {
  const charts = [];

  // 开发工程饼图
  const chart1 = echarts.init(document.getElementById('chart1'));
  chart1.setOption(getChart1Option());

  // 施工进度柱状图
  const chart2 = echarts.init(document.getElementById('chart2'));
  chart2.setOption(getChart2Option(progressTabData));

  // 安全检查折线图
  const chart3 = echarts.init(document.getElementById('chart3'));
  chart3.setOption(getChart3Option());

  charts.push(chart1, chart2, chart3);
  
  return charts;
};
