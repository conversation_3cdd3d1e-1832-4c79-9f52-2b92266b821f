// const webpack = require('webpack');
const path = require('path');
const fs = require('fs');
const mockDir = path.resolve(__dirname, './mock');
const proxy = require('../../config/proxy');
const defaultCssVars = require('cloudpivot/common/styles/variable').pc;
const extendLessVars = require('./extends/theme');
const modifyVars = Object.assign({}, defaultCssVars, extendLessVars);

const { ModuleFederationPlugin } = require('webpack').container;

const CopyWebpackPlugin = require('copy-webpack-plugin');
// const momentIgnore = new webpack.IgnorePlugin(/^\.\/locale$/, /moment$/);
const ThemeColorReplacerPlugin = require('./webpack_theme_plugin');

const Themes = [ThemeColorReplacerPlugin()];

const { smp } = require('../../config/common-config');
const report = process.env.npm_config_report;

const copy = new CopyWebpackPlugin({
  patterns: [
    {
      from: path.resolve(
        __dirname,
        '../../node_modules/cloudpivot-form/form/src/renderer/components/pc/input-textarea/tinymcelib',
      ),
      to: 'tinymcelib',
      //ignore: ['.*'],
    },
  ],
});

const pages = {
  main: {
    entry: 'src/main.ts',
    template: 'public/index.html',
    filename: 'index.html',
  },
  externalLink: {
    entry: 'src/views/externalLink/externalLink/main.ts',
    template: 'src/views/externalLink/externalLink/el.html',
    filename: 'el.html',
  },
};

module.exports = {
  pages,
  parallel: false,
  filenameHashing: true,
  productionSourceMap: false,
  lintOnSave: false,
  // 处理IE兼容————vuex持久化脚本语法转译
  transpileDependencies: [
    'vuex-persist',
    'flatted',
    'ansi-regex',
    '@h3print/designer',
    '@h3print/excellit',
    '@h3print/runtime',
  ],
  devServer: {
    port: 9100,
    open: true,
    proxy: proxy,
    client: {
      overlay: false,
    },
  },
  css: {
    loaderOptions: {
      less: {
        modifyVars: modifyVars,
        javascriptEnabled: true,
      },
    },
    sourceMap: true,
  },
  chainWebpack: (config) => {
    config.resolve.alias
      .set('@', path.resolve(__dirname, './src'))
      .set('assets', path.resolve(__dirname, './src/assets'))
      .set('styles', path.resolve(__dirname, './src/styles'))
      .set('components', path.resolve(__dirname, './src/components'));

    // 移除 prefetch 插件
    ['externalLinkOld', 'main', 'externalLink'].forEach((name) =>
      config.plugins.delete(`prefetch-${name}`),
    );

    // 优化打包
    config.optimization.splitChunks({
      minSize: 300000,
      maxInitialRequests: 6,
      cacheGroups: {
        xlsx: {
          name: 'xlsx',
          test: /[\\/]node_modules[\\/]xlsx[\\/]/,
          minSize: 0,
          minChunks: 1,
          reuseExistingChunk: true,
          chunks: 'all',
        },
        '@h3/antd-vue': {
          name: '@h3/antd-vue',
          test: /[\\/]node_modules[\\/]@h3[\\/]antd-vue[\\/]/,
          minSize: 0,
          minChunks: 1,
          reuseExistingChunk: true,
          chunks: 'all',
        },
        '@h3/report': {
          name: '@h3/report',
          test: /[\\/]node_modules[\\/]@h3[\\/]report[\\/]/,
          minSize: 0,
          minChunks: 1,
          reuseExistingChunk: true,
          chunks: 'all',
        },
        '@h3/thinking-ui': {
          name: '@h3/thinking-ui',
          test: /[\\/]node_modules[\\/]@h3[\\/]thinking-ui[\\/]/,
          minSize: 0,
          minChunks: 1,
          reuseExistingChunk: true,
          chunks: 'all',
        },
        '@ant-design': {
          name: 'ant-design',
          test: /[\\/]node_modules[\\/]@ant-design[\\/]/,
          minSize: 0,
          minChunks: 1,
          reuseExistingChunk: true,
          chunks: 'all',
        },
        'h3-mobile-vue': {
          name: 'h3-mobile-vue',
          test: /[\\/]node_modules[\\/]h3-mobile-vue[\\/]/,
          minSize: 0,
          minChunks: 1,
          reuseExistingChunk: true,
          chunks: 'all',
        },
        '@cloudpivot-shared': {
          name: 'cloudpivot-shared',
          test: /[\\/]node_modules[\\/]@cloudpivot-shared[\\/]/,
          minSize: 0,
          minChunks: 1,
          reuseExistingChunk: true,
          chunks: 'all',
        },
        'cloudpivot-list': {
          name: 'cloudpivot-list',
          test: /[\\/]cloudpivot-list[\\/]/,
          minSize: 0,
          minChunks: 1,
          reuseExistingChunk: true,
          chunks: 'all',
        },
        'cloudpivot-form': {
          name: 'cloudpivot-form',
          test: /[\\/]cloudpivot-form[\\/]/,
          minSize: 0,
          minChunks: 1,
          reuseExistingChunk: true,
          chunks: 'all',
        },
        cloudpivot: {
          name: 'cloudpivot',
          test: /[\\/]cloudpivot[\\/]/,
          minSize: 0,
          minChunks: 1,
          reuseExistingChunk: true,
          chunks: 'all',
        },
        vendors: {
          name: 'chunk-vendors',
          test: /[\\/]node_modules[\\/]/,
          priority: -10,
          chunks: 'initial',
        },
        common: {
          name: 'chunk-common',
          minChunks: 2,
          priority: -20,
          chunks: 'initial',
          reuseExistingChunk: true,
        },
      },
    });

    // config.module
    //   .rule('images')
    //   .use('url-loader')
    //   .loader('url-loader')
    //   .tap((options) => Object.assign(options, { limit: 1024 }));
  },
  configureWebpack: (config) => {
    config.plugins.push(copy);
    config.plugins.push(...Themes);
    const getShared = () => {
      const res = {};
      const sharedList = require('./sharedList.json');
      sharedList.forEach((key) => {
        res[key] = {
          // packageName: key,
          shareScope: 'default',
          shareKey: key,
          singleton: true,
          eager: true,
        };
      });
      return res;
    };

    // const mfp = new ModuleFederationPlugin({
    //   name: 'main_app',
    //   shared: getShared(),
    // });
    // config.plugins.push(mfp);
  },
};
