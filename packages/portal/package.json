{"name": "cloudpivot-portal", "version": "6.15.15", "private": true, "description": "云枢portal端项目", "scripts": {"dll": "webpack -p --progress --config ./webpack.dll.conf.js", "serve": "npm run install-fix && rimraf node_modules/.cache && vue-cli-service serve", "dev": "rimraf node_modules/.cache && cross-env node --max-http-header-size=8192 --max_old_space_size=8192 node_modules/@vue/cli-service/bin/vue-cli-service.js serve --mode debug", "local": "rimraf node_modules/.cache && cross-env node --max_old_space_size=8192 node_modules/@vue/cli-service/bin/vue-cli-service.js serve --mode local", "build-dev": "vue-cli-service build --mode development", "build-prod": "rimraf node_modules/.cache && vue-cli-service build --max_old_space_size=8192", "build": "npm run install-fix && npm run build-prod", "lint": "vue-cli-service lint", "analyze": "vue-cli-service build --report", "test": "vue-cli-service test:unit", "install-fix": "cd ../../bin && node ./install-fix.js"}, "dependencies": {"@ant-design/colors": "^6.0.0", "cloudpivot": "6.15.11", "cloudpivot-designer": "6.15.1", "cloudpivot-flow": "6.15.8", "cloudpivot-form": "6.15.15", "cloudpivot-forms": "6.15.7", "cloudpivot-icons": "6.15.1", "cloudpivot-kindeditor": "6.15.0", "cloudpivot-list": "6.15.13", "cloudpivot-platform": "6.15.0", "cloudpivot-portal-designer": "6.15.9", "file-saver": "^2.0.5", "install": "^0.13.0", "sheetjs-style": "^0.15.8", "v-scale-screen": "^2.3.0", "vue-html-to-paper": "1.1.1", "vue-infinite-scroll": "^2.0.2", "vue-seamless-scroll": "^1.1.23", "webpack-theme-color-replacer": "^1.3.26", "xlsx": "^0.18.5"}, "devDependencies": {"@babel/preset-env": "^7.8.4", "@vue/cli-plugin-babel": "~5.0.0-alpha.7", "@vue/cli-plugin-eslint": "~5.0.0-alpha.7", "@vue/cli-plugin-router": "~5.0.0-alpha.7", "@vue/cli-plugin-typescript": "~5.0.0-alpha.7", "@vue/cli-plugin-vuex": "~5.0.0-alpha.7", "@vue/cli-service": "~5.0.0-alpha.7", "@vue/eslint-config-typescript": "^7.0.0", "babel-core": "7.0.0-bridge.0", "babel-eslint": "^10.0.3", "babel-plugin-import": "^1.13.1", "babel-plugin-syntax-dynamic-import": "^6.18.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "less": "^3.0.4", "less-loader": "^5.0.0", "typescript": "~4.1.5"}}