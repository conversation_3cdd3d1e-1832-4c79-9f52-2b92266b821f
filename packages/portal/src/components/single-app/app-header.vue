<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <div class="app-header" :class="{ dark: outwardType === 'dark' }">
    <div class="app-logo">
      <img v-if="appLogo" :src="appLogo" class="item-icon" />
      <img v-else src="../../assets/icons/application.png" class="item-icon" />
    </div>
    <div class="app-name">{{ getAppName() }}</div>
  </div>
</template>

<script lang="ts">
import { workflowCenterApi, listApi } from 'cloudpivot/api';
import { Badge, Divider, Menu } from '@h3/antd-vue';
import { Component, Vue } from 'vue-property-decorator';
import getDownloadUrlNew from 'cloudpivot/common/src/utils/getDownloadUrlNew';
import { namespace } from 'vuex-class';
const WorkflowCenterModule = namespace('WorkflowCenter/WorkflowCenter');

/**
 * 高亮滑块的样式
 * */
interface ClockBlock {
  width: number;
  left: number;
}

@Component({
  name: 'app-header',
  components: {
    AMenu: Menu,
    AMenuItem: Menu.Item,
    ABadge: Badge,
    ADivider: Divider,
  },
})
export default class AppHeader extends Vue {
  @WorkflowCenterModule.Mutation('setAppName') setAppName: any;

  @WorkflowCenterModule.Mutation('setAppCode') setAppCode: any;

  appObject: any = {};

  appLogo: string = '';

  get appCode() {
    return (
      window.Environment.appCode ||
      this.$route.query.appCode ||
      this.$route.params.appCode
    );
  }

  get outwardType() {
    // 获取主题外观
    return this.$store.state.outwardType;
  }

  getAppName() {
    let name = this.appObject.name;
    if (!name) {
      return '';
    }
    if (this.appObject.name_i18n) {
      const name_i18n =
        typeof this.appObject.name_i18n === 'string'
          ? JSON.parse(this.appObject.name_i18n)
          : this.appObject.name_i18n;
      name = name_i18n[this.$i18n.locale] || name;
    }
    return name;
  }

  getAppLogo() {
    const logoUrl = this.appObject.logoUrl;
    if (!logoUrl) {
      this.appLogo = '';
      return;
    }
    if (~logoUrl.indexOf('http://') || ~logoUrl.indexOf('https://')) {
      this.appLogo = logoUrl;
    } else {
      this.appLogo = getDownloadUrlNew.getImageUrl(logoUrl);
    }
  }

  mounted() {
    this.initApp();
  }

  async initApp() {
    let res: any = {};
    try {
      res = await listApi.list({ isMobile: false, isPortal: true });
    } catch (e) {
      res = await listApi.list({ isMobile: false, isPortal: true });
    }
    if (res.errcode === 0) {
      const appList = res.data;
      this.appObject = appList.find((app) => app.code === this.appCode);
      this.getAppLogo();
      this.setAppCode(this.appObject.code);
      this.setAppName(this.appObject.name);
    }
  }
}
</script>
<style lang="less" scoped>
@import '../../styles/themes/default.less';
@import '~cloudpivot-list/application/src/components/pc/style/custom-themes';
.app-header {
  display: flex;
  justify-content: flex-start;
  align-items: center;
  width: 100%;
  height: 60px;
  background: @headBGColor;
  position: relative;
  box-shadow: 0px 1px 0px 0px #eeeeee;
  padding: 0 20px;
  z-index: 3;
  .app-logo {
    width: 20px;
    height: 20px;
    background: #ffffff;
    border-radius: 1px;
    border: 0.5px solid #eeeeee;
    display: flex;
    text-align: center;
    justify-content: center;
    align-items: center;
    margin-right: 6px;
    .item-icon {
      width: 18px;
      height: 18px;
    }
  }
  .app-name {
    height: 22px;
    font-size: 16px;
    font-weight: 600;
    color: #111218;
    line-height: 22px;
  }
  &.dark {
    box-shadow: unset;
    .app-name {
      color: #fff;
    }
  }
}
</style>
