<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <!-- <div class="app-aside">
    <WorkflowCenterMenu :customMenuConfig="taskMenu" :appCode="appCode" />
    <ApplicationListMenu
      ref="application-list-menu"
      :appCode="appCode"
      :modalCode="modalCode"
      :AppList="AppList"
      @goTo="goTo"
    />
  </div> -->
  <aside
    class="app-aside isAppList"
    :class="{ dark: outwardType === 'dark' }"
    :style="{ width: `${dragWidth}px` }"
  >
    <div class="aside" :style="{ width: `${dragWidth}px` }">
      <a-layout-sider
        ref="slider"
        v-model="isShow"
        :trigger="null"
        collapsible
        collapsedWidth="64"
        class="aside-menu"
        :class="outwardType"
        width="100%"
      >
        <WorkflowCenterMenu
          class="single-workflow-menu"
          :customMenuConfig="taskMenu"
          :appCode="appCode"
          :isSingleApp="true"
          @routerClickCallback="workflowRouteClick"
        />
        <ApplicationListMenu
          ref="application-list-menu"
          :appCode="appCode"
          :modalCode="modalCode"
          :AppList="AppList"
          :appPath="appPath"
          :isSingleApp="true"
          @goTo="goTo"
        />
      </a-layout-sider>
    </div>
    <div
      class="hide-menu"
      :class="isShow ? '' : 'open'"
      :style="{ top: '10px' }"
      @click="hideMenu"
    >
      <img
        v-show="!isShow"
        src="data:image/jpg;base64,/9j/4AAQSkZJRgABAQAASABIAAD/4QBMRXhpZgAATU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAIKADAAQAAAABAAAANgAAAAD/7QA4UGhvdG9zaG9wIDMuMAA4QklNBAQAAAAAAAA4QklNBCUAAAAAABDUHYzZjwCyBOmACZjs+EJ+/8AAEQgANgAgAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/bAEMAAQEBAQEBAgEBAgMCAgIDBAMDAwMEBQQEBAQEBQYFBQUFBQUGBgYGBgYGBgcHBwcHBwgICAgICQkJCQkJCQkJCf/bAEMBAQEBAgICBAICBAkGBQYJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCf/dAAQAAv/aAAwDAQACEQMRAD8A/pgooor7A+fCtjQde1Xw1qsOsaNM0E8DBgVJGcHofUHoQeCKoS2V5BbxXc0TpFPu8t2UhX2nDbSeDg9cdKrUmk1Yauj/0P6YK9Y8C+BtOuNOfxz45drXQrVsADiS7kHSKIe5+83bn3K+T19G6yB8ZvClrc+HT5OqaHbiOTS14jeJestuvr03L16DsN31VaTVl07niUkmeU+OvHF/431JJ5Y1trO1XyrS1j4jgiHRQPU4GT39gAK4ilIKkqwwRSVrGKSsjOUm3dn/0f6YK0dJ1bUtC1KHV9Ima3uYGDJIvUH+oPQg8EcGs6ivsGj59M7/AMe+KND8YXNtrllZGy1GVT9vCY8mSTIxJGOoLclge/qck8BRRUxikrIcpXd2f//S/pgooor7A+fCu3+Hvgy48eeJ7fw/BKsIkbLs2eEHLYwOTgHA457iuIr3n9nL/kpVt/uv/wCgtWdVtRbRdNXkkz//2Q=="
      />
      <img
        v-show="isShow"
        src="data:image/jpg;base64,/9j/4AAQSkZJRgABAQAASABIAAD/4QBMRXhpZgAATU0AKgAAAAgAAYdpAAQAAAABAAAAGgAAAAAAA6ABAAMAAAABAAEAAKACAAQAAAABAAAAIKADAAQAAAABAAAANgAAAAD/7QA4UGhvdG9zaG9wIDMuMAA4QklNBAQAAAAAAAA4QklNBCUAAAAAABDUHYzZjwCyBOmACZjs+EJ+/8AAEQgANgAgAwEiAAIRAQMRAf/EAB8AAAEFAQEBAQEBAAAAAAAAAAABAgMEBQYHCAkKC//EALUQAAIBAwMCBAMFBQQEAAABfQECAwAEEQUSITFBBhNRYQcicRQygZGhCCNCscEVUtHwJDNicoIJChYXGBkaJSYnKCkqNDU2Nzg5OkNERUZHSElKU1RVVldYWVpjZGVmZ2hpanN0dXZ3eHl6g4SFhoeIiYqSk5SVlpeYmZqio6Slpqeoqaqys7S1tre4ubrCw8TFxsfIycrS09TV1tfY2drh4uPk5ebn6Onq8fLz9PX29/j5+v/EAB8BAAMBAQEBAQEBAQEAAAAAAAABAgMEBQYHCAkKC//EALURAAIBAgQEAwQHBQQEAAECdwABAgMRBAUhMQYSQVEHYXETIjKBCBRCkaGxwQkjM1LwFWJy0QoWJDThJfEXGBkaJicoKSo1Njc4OTpDREVGR0hJSlNUVVZXWFlaY2RlZmdoaWpzdHV2d3h5eoKDhIWGh4iJipKTlJWWl5iZmqKjpKWmp6ipqrKztLW2t7i5usLDxMXGx8jJytLT1NXW19jZ2uLj5OXm5+jp6vLz9PX29/j5+v/bAEMAAQEBAQEBAgEBAgMCAgIDBAMDAwMEBQQEBAQEBQYFBQUFBQUGBgYGBgYGBgcHBwcHBwgICAgICQkJCQkJCQkJCf/bAEMBAQEBAgICBAICBAkGBQYJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCQkJCf/dAAQAAv/aAAwDAQACEQMRAD8A/pgooor7A+fCtjQde1Xw1qsOsaNM0E8DBgVJGcHofUHoQeCKx6KTV9GNOx//0P6YK77wDqnhG0vJ9K8a2gmsb9BGbhR++tmByJIz7H7wxyPXoeBor66Ubqx4EZWdztvHHgfUvBGpLb3DLcWlwvmWt1HzHPGejKfX1GePoQTxNfRvgDf/AMK01H/hYuP+Eawfse//AF/2vt9l/Xd/DnPbfXzlUUpt3T6F1IpWaP/R/pgrvvAOl+Ebu8n1XxrdiGxsEEht1P765YnAjjHufvHPA9Oo4GivrpK6seBF2dztvHHjjUvG+pLcXCrb2luvl2trHxHBGOiqPX1OOfoABxNFFOMUlZBKTbuz/9L+mCiiivsD58K7f4e+DLjx54nt/D8EqwiRsuzZ4QctjA5OAcDjnuK4ivef2cv+SlW3+6//AKC1Z1W1FtF01eSTP//Z"
      />
    </div>
  </aside>
</template>
<script lang="ts">
import { listApi, listParams, workbenchApi } from 'cloudpivot/api';
import { Component, Vue, Watch } from 'vue-property-decorator';
import WorkflowCenterMenu from 'cloudpivot-flow/flow-center/src/components/pc/menu/workflow-center-menu.vue';
import ApplicationListMenu from 'cloudpivot-list/application/src/components/pc/menu/application-list-menu.vue';
import common from 'cloudpivot/common';
import { Layout } from '@h3/antd-vue';
import { specificModelConfig } from 'extension-template/src/views/specific-model-list/specific-model-config';
import { namespace } from 'vuex-class';
import * as platform from 'cloudpivot-platform/platform';
const WorkflowCenterModule = namespace('WorkflowCenter/WorkflowCenter');

@Component({
  name: 'app-aside',
  components: {
    ALayoutSider: Layout.Sider,
    WorkflowCenterMenu,
    ApplicationListMenu,
  },
})
export default class AppAside extends Vue {
  @WorkflowCenterModule.State('appName') appName: any;

  get appCode() {
    return (
      window.Environment.appCode ||
      this.$route.query.appCode ||
      this.$route.params.appCode
    );
  }

  // 单应用流程模块路由配置
  get taskMenu() {
    return [
      {
        path: `/app-todo/${this.appCode}?appCode=${this.appCode}`,
        iconClass: 'h-icon-all-edit-square',
        name_i18n: (vm) => {
          return vm.$t('cloudpivot.flowCenter.pc.todoList');
        },
        topDivider: false,
        countField: 'unFinishedListCount',
      },
      {
        path: `/app-done/${this.appCode}?appCode=${this.appCode}`,
        iconClass: 'h-icon-all-carryout',
        name_i18n: (vm) => {
          return vm.$t('cloudpivot.flowCenter.pc.doneList');
        },
        topDivider: false,
      },
      {
        path: `/app-toread/${this.appCode}?appCode=${this.appCode}`,
        iconClass: 'h-icon-all-file',
        name_i18n: (vm) => {
          return vm.$t('cloudpivot.flowCenter.pc.readingTask');
        },
        topDivider: false,
        countField: 'unReadListCount',
      },
      {
        path: `/app-started/${this.appCode}?appCode=${this.appCode}`,
        iconClass: 'h-icon-all-apartment',
        name_i18n: (vm) => {
          return vm.$t('cloudpivot.flowCenter.pc.myFlow');
        },
        topDivider: false,
      },
    ];
  }

  get outwardType() {
    // 获取主题外观
    return this.$store.state.outwardType;
  }

  modalCode: string = '';

  // 模型列表
  AppList: any[] = [];

  // 当前页面路由，用于刷新页面时定位当前模型
  appPath: string = '';

  // false 展开 true收起
  isShow: boolean = false;

  // 侧边栏可拖拽初始宽度
  dragWidth: number = 250;

  startX: number = 250;

  @Watch('$route')
  routeChange(val, oldVal) {
    this.switchMenu();
  }

  created() {
    const { fullPath } = this.$route;
    this.appPath = fullPath;
  }

  mounted() {
    this.getAppGroups();
    this.switchMenu();
    // 拖拽功能
    this.$nextTick(() => {
      const dragLine: any = document.querySelector('#jDragLine');
      const that = this;
      let maxClientWidth;
      let minWidth;
      let maxWidth;
      dragLine.addEventListener(
        'mousedown',
        (e: any) => {
          maxClientWidth = document.body.clientWidth / 3;
          minWidth = that.dragWidth - 8;
          maxWidth = that.dragWidth + 8;
          document.body.setAttribute('unselectable', 'on');
          document.body.setAttribute('onselectstart', 'return false;');
          if (e.clientX >= minWidth && e.clientX <= maxWidth) {
            document.onmousemove = function (ev: any) {
              // 收缩的时候不允许拖拽
              if (that.isShow) {
                return;
              }
              that.dragWidth = ev.clientX;
              if (ev.clientX > maxClientWidth) {
                that.dragWidth = maxClientWidth;
              } else if (ev.clientX < that.startX) {
                that.dragWidth = that.startX;
              } else {
                //Else Empty block statement
              }
              common.utils.Bus.$emit('resize');
            };
            document.onmouseup = function () {
              document.body.removeAttribute('unselectable');
              document.body.removeAttribute('onselectstart');
              document.onmousemove = null;
              document.onmouseup = null;
            };
          }
        },
        false,
      );
    });

    const curDom: any = this.$refs.slider;
    curDom.$el.addEventListener(
      'transitionend',
      this.transitionendEvent,
      false,
    );
  }

  transitionendEvent(e: any) {
    if (e.propertyName === 'width') {
      common.utils.Bus.$emit('resize');
    }
  }

  beforeDestroy() {
    const curDom: any = this.$refs.slider;
    curDom.$el.removeEventListener(
      'transitionend',
      this.transitionendEvent,
      false,
    );
  }

  // 获取模型列表
  async getAppGroups() {
    if (!this.appCode) {
      return;
    }
    const params: listParams.FolderSchema = {
      appCode: this.appCode,
      isMobile: false, // 需要处理
    };
    const res = await listApi.getFolder(params);
    if (res.errcode === 0) {
      if (!Array.isArray(res.data)) {
        return;
      }
      const HomePageRes: any = await workbenchApi.getPortalDesignPageByCode({
        code: `${this.appCode}HomePage`,
      });
      if (
        HomePageRes.errcode === 0 &&
        HomePageRes.data &&
        HomePageRes.data.status === 'ENABLE' &&
        HomePageRes.data.published === true
      ) {
        res.data.unshift({
          appCode: this.appCode,
          bindWorkflow: false,
          children: null,
          code: `${this.appCode}HomePage`,
          icon: 'h-icon-all-home1',
          modelType: 'LIST',
          name: '首页',
          name_i18n: '{"en":"Home Page"}',
          sortKey: 1,
          type: 'HomePage',
        });
      }

      let appList: any[] = res.data.map((i) => {
        i = this.initData(i);
        return i;
      });

      //当分组下没有可见模型时不显示分组
      appList = appList.filter((x) => {
        if (x.type === 'Folder') {
          if (Array.isArray(x.children) && x.children.length > 0) {
            x.children = x.children.filter((y) => {
              if (y.type === 'Folder') {
                return Array.isArray(y.children) && y.children.length > 0;
              } else {
                return y;
              }
            });
          }
          return Array.isArray(x.children) && x.children.length > 0;
        } else {
          return x;
        }
      });
      this.AppList = appList;
    }
  }

  initData(item) {
    item.scopedSlots = {
      title: 'title',
    };
    if (item.children && item.children.length) {
      item.children = item.children.map((i) => {
        i = this.initData(i);
        return i;
      });
    }
    return item;
  }

  queryCode: string = '';

  // 模型列表路由跳转事件
  goTo(item) {
    const customRouterItem: any = specificModelConfig.find(
      (el: any) => el.appCode === item.appCode && el.schemaCode === item.code,
    );
    if (customRouterItem) {
      // 跳转模型存在二开的自定义样式
      this.$router
        .push({
          name: customRouterItem.routerName,
          params: {
            appCode: item.appCode,
            schemaCode: item.code,
          },
          query: {
            parentId: item.parentId,
            code: item.code,
            openMode: item.openMode,
            pcUrl: item.pcUrl,
            queryCode: this.queryCode,
            return: this.$route.query.return || '',
          },
        })
        .catch((err: any) => {
          console.warn(err);
        });
    } else if (item.type === 'BizModel') {
      this.$router
        .push({
          name: 'singleAppList',
          params: {
            appCode: this.appCode,
            schemaCode: item.code,
            displayName: item.name,
          },
          query: {
            appCode: this.appCode,
            code: item.code,
            schemaCode: item.code,
            displayName: item.name,
          },
        })
        .catch((err: any) => {
          console.warn(err);
        });
    } else if (item.type === 'Report') {
      this.$router
        .push({
          name: 'appReport',
          params: {
            appCode: this.appCode,
            reportCode: item.code,
          },
        })
        .catch((err: any) => {
          console.warn(err);
        });
    } else if (item.pcUrl) {
      if (item.openMode === 'RECENT_PAGE_MODE') {
        this.$router
          .push({
            name: 'appDefine',
            params: {
              url: item.pcUrl,
            },
            query: {
              parentId: item.parentId,
              code: item.code,
              openMode: item.openMode,
              pcUrl: item.pcUrl,
            },
          })
          .catch((err: any) => {
            console.warn(err);
          });
      } else if (item.openMode === 'NEW_PAGE_MODE') {
        window.open(item.pcUrl);
      } else {
        this.$router
          .push({
            path: item.pcUrl,
            query: {
              parentId: item.parentId,
              code: item.code,
              openMode: item.openMode,
              pcUrl: item.pcUrl,
            },
          })
          .catch((err: any) => {
            console.warn(err);
          });
      }
    } else if (item.type === 'HomePage') {
      if (this.$route.name !== 'appHome') {
        const query: any = {
          parentId: item.parentId,
          code: item.code,
          openMode: item.openMode || '',
          pcUrl: item.pcUrl || '',
          appCode: this.appCode,
        };
        this.$router
          .push({
            name: 'appHome',
            params: {
              appCode: item.appCode,
            },
            query,
          })
          .catch((err: any) => {
            console.error(err);
          });
      }
    } else {
    }
    const { fullPath } = this.$route;
    this.appPath = fullPath;
    document.title = `${this.appName || '奥哲云枢'}-${item.name}`;
    platform.service.setTitle(document.title);
  }

  hideMenu() {
    this.isShow = !this.isShow;
    if (this.isShow) {
      this.dragWidth = 0;
      this.startX = 0;
    } else {
      this.dragWidth = 250;
      this.startX = 250;
    }
    common.utils.Bus.$emit('customButton', this.isShow);
  }

  switchMenu() {
    if (!this.$route) {
      return;
    }
    const { fullPath } = this.$route;
    const isWorkflowCenterRoute =
      fullPath.includes('app-todo') ||
      fullPath.includes('app-done') ||
      fullPath.includes('app-toread') ||
      fullPath.includes('app-started');
    const isApplicationRoute =
      fullPath.includes('app-list') ||
      fullPath.includes('app-home') ||
      fullPath.includes('app-report') ||
      fullPath.includes('app-define');
    if (isWorkflowCenterRoute) {
      setTimeout(() => {
        const route = this.taskMenu.find((item) => item.path === fullPath);
        document.title = `${this.appName || '奥哲云枢'}-${route.name_i18n(
          this,
        )}`;
        platform.service.setTitle(document.title);
      }, 200);
      (this.$refs['application-list-menu'] as any).defaultKey = [];
    } else if (isApplicationRoute) {
      setTimeout(() => {
        (this.$refs['application-list-menu'] as any).switchMenu(
          this.$route.query.code,
        );
      }, 200);
    } else {
      //Else Empty block statement
    }
  }

  // 流程模块路由点击事件
  workflowRouteClick(event) {
    this.appPath = event.path;
  }
}
</script>
<style lang="less" scoped>
@import '../../styles/themes/default.less';
@import '~cloudpivot-list/application/src/components/pc/style/custom-themes';

/**
 侧边栏容器
*/
.app-aside {
  width: 216px;
  height: 100%;
  position: relative;
  background-color: #f1f2f6;
  .hide-menu {
    width: 16px;
    height: 27px;
    position: absolute;
    top: 24px;
    right: -16px;
    z-index: 97;
    cursor: pointer;
    > img {
      width: 16px;
      height: 27px;
    }
  }
  .main-placeholder {
    width: 16px;
    height: 48px;
    background-color: #fff;
    position: absolute;
    right: 0;
    top: 0;
    box-shadow: 0px 1px 0px 0px #e4e4e4;
  }
}

/deep/.aside {
  height: 100%;
  z-index: 2;
  position: relative;
  overflow-y: hidden;
  background-color: @subColor;
  overflow-x: hidden;
  box-shadow: 2px 0px 11px 0px rgba(199, 205, 215, 0.5), 1px 0px 0px 0px #eeeeee;
  scrollbar-color: transparent transparent;
  padding-top: 8px;
  padding-bottom: 4px;
  &::-webkit-scrollbar {
    width: 0px;
  }
  &::-webkit-scrollbar-thumb {
    background: transparent;
  }
  &:hover::-webkit-scrollbar-thumb {
    background: rgba(255, 255, 255, 0.45);
  }
  &::-webkit-scrollbar-track {
    background: transparent;
  }
  .aside-menu {
    height: 100%;
    overflow: hidden;
    .single-workflow-menu {
      margin-bottom: 8px;
    }
    .ant-menu-item {
      padding: 0 16px !important;
      margin-bottom: 0;
    }
    .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
      background-color: @activeBGColor;
      a {
        .icon,
        span {
          color: @highlightColor !important;
          sup span {
            color: #fff !important;
            // background-color: #ff5219 !important;
          }
          sup {
            // background-color: #ff5219 !important
          }
        }
      }
    }
    .ant-layout-sider-children {
      background-color: transparent;
      height: 100%;
      display: flex;
      flex-flow: column;
      .ant-menu {
        background-color: @subColor;
      }
    }
    &.dark {
      .ant-layout-sider-children {
        background-color: transparent;
        .ant-menu {
          background-color: @subColor;
          a > span,
          a > .icon {
            color: #fff;
          }
        }
      }

      .workflow-menu .ant-menu-item:hover {
        background-color: @hoverColor;
        a {
          .icon,
          span {
            color: #fff !important;
          }
        }
      }
      .ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected {
        &:hover {
          background-color: #fff;
          a {
            .icon,
            span {
              color: @highlightColor !important;
              sup span {
                color: #fff !important;
                background-color: #ff5219 !important;
              }
            }
          }
        }
        a {
          .icon,
          span {
            color: @primaryColor;
            sup {
              // background-color: #ff5219 !important
            }
            sup span {
              color: #fff;
            }
          }
        }
      }
    }
    &.light {
      .workflow-menu .ant-menu-item:hover {
        background-color: @hoveBGColor;
        a {
          .icon,
          span {
            // color: @primaryColor !important;
            sup span {
              color: #fff !important;
            }
          }
        }
      }
    }
  }
  .aside-top {
    width: 100%;
    height: 70px;
    padding: 0 16px;
    color: rgba(17, 18, 24, 0.5);
    display: flex;
    justify-content: space-between;
    align-items: center;
    font-size: 16px;
    font-weight: 400;
    > i {
      vertical-align: baseline;
      overflow: inherit;
      color: rgba(17, 18, 24, 0.5);
      font-size: 18px;
      cursor: pointer;
      width: 28px;
      height: 28px;
      display: block;
      text-align: center;
      line-height: 28px;
      border-radius: 2px;
      &:hover {
        color: @highlightColor;
      }
      &:active,
      &.active {
        // background-color: #eef4fd;
        color: @highlightColor;
      }
      &.hide-text-active {
        text-indent: 0;
        // margin-left: 3px;
      }
    }
  }
  .dark {
    .aside-top {
      span {
        color: #fff;
      }
      > i {
        color: #fff;
        opacity: 0.8;
        &:hover {
          opacity: 1;
        }
        &.active {
          opacity: 1;
          background-color: transparent;
        }
      }
    }
  }
  .hide-text {
    text-indent: -999px;
    padding: 0 18px;
  }

  .hide-menu {
    width: 14px;
    height: 28px;
    line-height: 28px;
    border-radius: 2px 0px 0px 2px;
    position: absolute;
    top: 10px;
    right: 0;
    z-index: 10;
    background: url('../../assets/icons/arrow-right.png') no-repeat center;
    background-color: rgba(255, 255, 255, 0.3);
    cursor: pointer;
  }
  .open {
    background: url('../../assets/icons/arrow-left.png') no-repeat center;
    background-color: rgba(255, 255, 255, 0.3);
  }
}
.ant-menu-submenu-popup.ant-menu-dark .ant-menu-item-selected {
  background-color: unset;
  a {
    color: white !important;
    .icon {
      margin-right: @base4-padding-xs;
    }
  }
}
.app-aside {
  .ant-menu-item {
    margin-bottom: 0 !important;
  }
}
.ant-menu-item > a {
  & > .icon,
  & > span {
    height: 40px;
    vertical-align: top !important;
  }
}
.ant-menu-inline-collapsed-tooltip a {
  color: white !important;
  .icon {
    margin-right: @base4-padding-xs;
  }
  .ant-badge {
    margin-bottom: 3px;
  }
}

.ant-layout-sider {
  transition: unset !important;
  background-color: @subColor !important;
}

.app-aside {
  .ant-tree {
    // overflow-y: hidden;
  }
  .ant-tree li {
    padding: 0 16px;
  }
  .app-list-tree {
    overflow-y: scroll;
    overflow-x: hidden;
    height: calc(100% - 70px);
    &::-webkit-scrollbar {
      width: 0;
    }
    > ul {
      .ant-tree-node-content-wrapper-normal.ant-tree-node-selected,
      .ant-tree-node-content-wrapper-normal:hover {
        // color: @highlightColor;
        &::before {
          content: '';
          position: absolute;
          left: -200px;
          top: 0;
          height: 40px;
          width: 850px;
          background-color: #eef4fd;
        }
      }
    }
    .ant-tree-node-content-wrapper {
      height: 40px;
      line-height: 40px;
      position: relative;
      padding: 0;
    }
    .ant-tree-switcher.ant-tree-switcher-noop {
      display: none;
    }
    .ant-tree li .ant-tree-node-content-wrapper:hover {
      background-color: transparent;
      .ant-tree-title {
        // color: @highlightColor;
      }
    }
    span.ant-tree-node-selected {
      background-color: transparent !important;
      .ant-tree-title {
        // color: @highlightColor;
      }
    }
    .ant-tree-node-content-wrapper-normal {
      .ant-tree-title {
        position: relative;
        .cus-title > i {
          margin-right: 4px;
        }
        // padding-left: 10px;
        // &::before {
        //   content: "\e7bb";
        //   font-family: "aufontAll" !important;
        //   position: relative;
        //   left: -10px;
        // }
      }
    }
    .ant-tree li span.ant-tree-switcher {
      top: 7px;
    }

    &.dark {
      > ul {
        .ant-tree-node-content-wrapper-normal {
          color: #fff;
          &::before {
            background-color: @hoverColor;
          }
        }
        .cus-title,
        .ant-tree-switcher {
          color: #fff;
        }
        .ant-tree-node-content-wrapper-normal.ant-tree-node-selected {
          color: @highlightColor;
          &::before {
            background-color: #fff;
          }
          // &::after{
          //   content: '';
          //   width: 3px;
          //   height: 24px;
          //   position: absolute;
          //   left:  -10px;
          //   top: 50%;
          //   transform: translateY(-50%);
          //   background-color: @primaryColor;
          //   border-radius: 0 2px 2px 0;
          // }
          .cus-title,
          .ant-tree-switcher {
            color: @highlightColor;
          }
        }
      }
    }
    &.light {
      > ul {
        .ant-tree-node-content-wrapper-normal {
          &::before {
            background-color: @activeBGColor;
          }
        }
        .ant-tree-node-content-wrapper-normal.ant-tree-node-selected {
          color: @highlightColor;
          &::before {
            background-color: @activeBGColor;
          }
          // &:hover{
          //   color: @highlightColor;
          // }
        }

        .ant-tree-node-content-wrapper-normal:hover {
          // color:@highlightColor;
        }
      }
    }
  }
}

/deep/.aside {
  .app-list-menu {
    border-top: 1px solid #e9e8ef;
    flex: 1;
    height: 1%;
    display: flex;
    flex-flow: column;
    .app-list-search-box {
      padding-top: 18px;
    }
    .app-list-tree {
      flex: 1;
      height: 100%;
      overflow-x: hidden;
      overflow-y: auto;
    }
  }
}
</style>
