<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <div class="header common-header" :class="outwardType">
    <div class="header-left" :class="isAppList ? 'header-left-width' : ''">
      <div class="left-icon">
        <em
          class="icon aufontAll h-icon-all-arrow-left-o"
          @click="goToBack"
        ></em>
      </div>
      <template v-if="['applications'].includes($route.name)">
        <div class="route-name">
          {{ $t('languages.common.applicationList') }}
        </div>
      </template>

      <template v-else-if="isWorkflowCenter">
        <div class="route-name">
          {{ $t('languages.common.workflowCenter') }}
        </div>
      </template>

      <template v-else-if="['messageNotification'].includes($route.name)">
        <div class="route-name">
          {{ $t('languages.common.notificationCenter') }}
        </div>
      </template>

      <template v-else-if="['myDraft'].includes($route.name)">
        <div class="route-name">
          {{ $t('languages.common.workBenchInfo.MyDraft') }}
        </div>
      </template>

      <!-- 除了上面几种情况，其他的都显示应用下拉列表组件 -->
      <!-- ['appPortalRuntime','applicationList','applicationReport','applicationDefine','applicationCustom','application'].includes($route.name) -->
      <template v-else>
        <div class="left-content">
          <a-popover
            v-model="appListVisible"
            placement="bottomRight"
            trigger="click"
            overlayClassName="left-app-list-popover"
          >
            <template slot="content">
              <ul class="app-list">
                <li
                  v-for="(appItem, index) in childList"
                  :key="index"
                  class="app-list-item"
                  @click="onAppListClick(appItem)"
                >
                  <div class="item-icon-box">
                    <template v-if="appItem.logoUrl">
                      <img
                        v-if="appItem.logoUrl.indexOf('http') > -1"
                        :src="appItem.logoUrl"
                        class="item-icon"
                      />
                      <img
                        v-else
                        :src="getDownloadUrlNew(appItem.logoUrl)"
                        class="item-icon"
                      />
                    </template>
                    <template v-else>
                      <svg class="svg-icon svgIcon" aria-hidden="true">
                        <use xlink:href="#h-icon-all-application1" />
                      </svg>
                    </template>
                  </div>
                  <div class="item-text">
                    <p
                      class="item-name"
                      :title="
                        isChinese
                          ? appItem && appItem.name
                          : (JSON.parse(appItem && appItem.name_i18n) &&
                              JSON.parse(appItem && appItem.name_i18n).en) ||
                            (appItem && appItem.name)
                      "
                    >
                      {{
                        isChinese
                          ? appItem && appItem.name
                          : (JSON.parse(appItem && appItem.name_i18n) &&
                              JSON.parse(appItem && appItem.name_i18n).en) ||
                            (appItem && appItem.name)
                      }}
                    </p>
                  </div>
                </li>
              </ul>
            </template>
            <div class="app-info">
              <!-- {{ appName }} -->
              <!-- <div class="app-icon"><i class="icon aufontAll h-icon-all-application1"></i></div> -->
              <div class="app-icon">
                <template v-if="appName.logoUrl">
                  <img
                    v-if="appName.logoUrl.indexOf('http') > -1"
                    :src="appName.logoUrl"
                    class="item-icon"
                  />
                  <img
                    v-else
                    :src="getDownloadUrlNew(appName.logoUrl)"
                    class="item-icon"
                  />
                </template>
                <template v-else>
                  <svg class="svg-icon svgIcon" aria-hidden="true">
                    <use xlink:href="#h-icon-all-application1" />
                  </svg>
                </template>
              </div>
              <div class="app-name">
                <span v-show="isChinese" :title="appName.name">{{
                  appName.name
                }}</span>
                <span v-show="!isChinese" :title="appName.name_i18n">{{
                  appName.name_i18n
                }}</span>
              </div>
              <i
                v-if="appListVisible"
                class="icon aufontAll h-icon-all-caret-up"
              ></i>
              <em v-else class="icon aufontAll h-icon-all-caret-down"></em>
            </div>
          </a-popover>
        </div>
      </template>
    </div>

    <div class="header-right">
      <div class="menu-right" :class="outwardType">
        <ul>
          <!-- 钉钉终端 -->
          <li v-if="isDingTalk">
            <a class="open-blank" @click.prevent="openBlank">{{
              $t('languages.common.openInBrowser')
            }}</a>
          </li>

          <!-- 动态渲染导航左侧工具栏 -->
          <template v-for="(menu, index) in defaultMenuConfig">
            <li
              v-if="
                !menu.authority ||
                menu.authority.length === 0 ||
                menu.authority.some((auth) => stateSystemAuthority[auth])
              "
              :key="index"
              class="op-item"
            >
              <template v-if="menu.name === 'ToAdmin'">
                <component :is="menu.name" :isDingTalk="isDingTalk" />
              </template>
              <template v-else>
                <component :is="menu.name" />
              </template>
            </li>
          </template>
        </ul>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import OAuthApi from '@/apis/oauth';
import { Popover } from '@h3/antd-vue';
import { listApi } from 'cloudpivot/api';
import MenuToolComponents from 'cloudpivot/common/src/components/pc/menu-tools';
import getDownloadUrlNew from 'cloudpivot/common/src/utils/getDownloadUrlNew';
import changeMenuToolsConfig from 'extension-template/src/portal-designer-extension/menu-operation/menu-tools-config';
import { Component, Provide, Vue, Watch } from 'vue-property-decorator';
import { namespace } from 'vuex-class';
const WorkflowCenterModule = namespace('WorkflowCenter/WorkflowCenter');
const SystemModule = namespace('System/System');

@Component({
  name: 'common-header',
  components: {
    APopover: Popover,
    ...MenuToolComponents,
  },
})
export default class CommonHeader extends Vue {
  @WorkflowCenterModule.Mutation('setUserId') setUserId: any;

  @SystemModule.State('isAdmin') isAdmin: any;

  @SystemModule.State('admin') admin: any;

  @SystemModule.Mutation('setIsAdmin') setIsAdmin: any;

  @SystemModule.Mutation('setAdmin') setAdmin: any;

  @SystemModule.Mutation('setIsPrivilegedPerson') setIsPrivilegedPerson: any;

  @SystemModule.Mutation('setIsAppAdmin') setIsAppAdmin: any;

  @SystemModule.Mutation('setRootAdmin') setRootAdmin: any;

  @SystemModule.Mutation('setUserInfo') setUserInfo: any;

  @SystemModule.Mutation('setIsDeptManager') setIsDeptManager: any;

  @WorkflowCenterModule.Mutation('setIsAppList') setIsAppList: any;

  @WorkflowCenterModule.Mutation('setAppNameList') setAppNameList: any;

  @WorkflowCenterModule.Mutation('setAppName') setAppName: any;

  @WorkflowCenterModule.Mutation('setAppCode') setAppCode: any;

  @WorkflowCenterModule.Mutation('setModalCode') setModalCode: any;

  @WorkflowCenterModule.Mutation('setAppChange') setAppChange: any;

  @WorkflowCenterModule.Mutation('setAppPath') setAppPath: any;

  @WorkflowCenterModule.State('appPath') appPath: any;

  @Provide()
  apiHost() {
    return (window as any).config.apiHost;
  }

  @Provide()
  token() {
    return window.localStorage.getItem('token');
  }

  get isWorkflowCenter() {
    return this.$route.path.indexOf('workflow-center') > -1;
  }

  get isChinese() {
    return !(this.$i18n.locale && this.$i18n.locale === 'en');
  }

  get isAppList() {
    return this.$store.state.WorkflowCenter.WorkflowCenter.isAppList;
  }

  isMove: boolean = false; //应用动画效果

  showAppList: boolean = false;

  appVisible: boolean = false;

  appGroupList: Array<any> = [];

  appList: any[] = [];

  appName: any = {
    name: '',
    name_i18n: '',
    logoUrl: '',
  };

  isLoaded: boolean = false; //是否已加载过

  isInit: boolean = false;

  appListVisible: boolean = false;

  //默认的左侧工具栏菜单
  defaultMenuConfig: any[] = [
    {
      name: 'GlobalSearch',
      components: 'GlobalSearch',
      authority: [],
    },
    {
      name: 'ThemeChange',
      components: 'ThemeChange',
      authority: ['admin'],
    },
    {
      name: 'ToAdmin',
      components: 'ToAdmin',
      authority: ['isAdmin'],
    },
    {
      name: 'NoticeTipsCom',
      components: 'NoticeTipsCom',
      authority: [],
    },
    {
      name: 'HelpCenter',
      components: 'HelpCenter',
      authority: [],
    },
    {
      name: 'LanguageChange',
      components: 'LanguageChange',
      authority: [],
    },
    {
      name: 'LoggedUserInfo',
      components: 'LoggedUserInfo',
      authority: [],
    },
  ];

  stateSystemAuthority: any = {}; // 系统权限

  created() {
    this.stateSystemAuthority = this.$store.state.System.System;

    //获取扩展的工具栏菜单
    this.defaultMenuConfig = changeMenuToolsConfig(this.defaultMenuConfig);
  }

  //应用的弹窗
  isChange2(e) {
    if (!e) {
      this.isMove = false;
      return;
    }
    // 获取当前选中的菜单名称
    // if (!this.userInfo.username) {
    //   this.getUserInfo();
    // }
    //获取应用组列表
    this.getAppGroupList(!this.isInit);
    this.isMove = true;
  }

  childList: any[] = [];

  /**
   * 初始化应用信息
   */
  initApp() {
    let data = location.pathname.split('/');
    data = data.filter((item) => item);
    const code = data[1];
    const item = this.childList.find((item) => item.code === code);
    if (!item) {
      this.$router.replace('/permission');
      return;
    }
    if (this.$route.name !== 'application') {
      this.setAppPath(location.href);
    }
    this.goToDetail(item);
    this.isInit = true;
  }

  /**
   * 应用详情
   */
  goToDetail(item) {
    if (!item) {
      return;
    }
    this.appVisible = false;
    this.appName = {
      name: item.appName || item.name,
      name_i18n:
        (item.appName_i18n && item.appName_i18n.en) ||
        (item.appName_i18n &&
          JSON.parse(item.appName_i18n) &&
          JSON.parse(item.appName_i18n).en) ||
        item.appName ||
        (JSON.parse(item.name_i18n) && JSON.parse(item.name_i18n).en) ||
        item.name,
      logoUrl: item.logoUrl,
    };
    this.setAppName(this.appName);
    this.setAppCode(item.appCode || item.code);
    this.setModalCode(item.appCode ? item.code : '');

    if (
      this.$route.params.appCode &&
      this.$route.params.schemaCode &&
      !item.appCode &&
      !this.isInit
    ) {
      this.setModalCode(this.$route.params.schemaCode);
      this.setAppCode(this.$route.params.appCode);
    }

    if (
      this.$route.params.appCode &&
      this.$route.params.reportCode &&
      !item.appCode &&
      !this.isInit
    ) {
      this.setModalCode(this.$route.params.reportCode);
      this.setAppCode(this.$route.params.appCode);
    }

    this.setAppChange(true);

    if (this.appPath) {
      this.isMove = false;
      return;
    }
    if (
      this.$route.path.indexOf('/application-list/') === -1 &&
      this.$route.path.indexOf('/application-report/') === -1 &&
      item.type !== 'BizModel'
    ) {
      this.$router
        .push(
          `/application/${item.appCode || item.code}?return=${
            this.$route.query.return || ''
          }`,
        )
        .catch((err) => {
          console.log(err);
        });
    } else {
      if (item.type) {
        /**
         * 路由上的traceId信息需要保留
         */
        const query: any = {
          return: this.$route.query.return || '',
        };
        if (this.$route.query.debugId) {
          query.debugId = this.$route.query.debugId;
        }
        this.$router
          .push({
            name: 'applicationList',
            params: {
              appCode: item.appCode || item.code,
              schemaCode: item.code,
            },
            query,
          })
          .catch((err) => {
            console.log(err);
          });
      }
    }
    this.isMove = false;
  }

  @Watch('$route', {
    immediate: true,
  })
  onRouteNameChange(val, oldVal) {
    if (
      val &&
      oldVal &&
      val.name === 'application' &&
      oldVal.name === 'applicationList' &&
      oldVal.path.indexOf(val.path) > -1
    ) {
      history.go(-1);
      return;
    }

    if (
      val &&
      oldVal &&
      val.name === 'application' &&
      oldVal.name === 'applicationList' &&
      oldVal.path.indexOf(val.path) > -1
    ) {
      history.go(-1);
      return;
    }

    if (val.name === 'appPortalRuntime' && val.query && val.query.code) {
      if (this.$store.hasModule(`${val.params.appCode}HomePage`)) {
        this.$store.unregisterModule(`${val.params.appCode}HomePage`);
      }
    }

    if (
      val.fullPath &&
      val.fullPath.includes('/application') &&
      !val.fullPath.includes('/applications') &&
      ((!this.isLoaded &&
        (!oldVal || !oldVal.fullPath.includes('/application'))) ||
        !this.appGroupList.length)
    ) {
      //首次加载
      this.isChange2(true);
      this.isMove = false;
    }
    if (val.fullPath && val.fullPath.includes('/application')) {
      this.setIsAppList(true);
    } else {
      this.setIsAppList(false);
    }
  }

  /**
   * 根据分组顺序获取获取所有应用
   */
  async getAppGroupList(init = false) {
    const res = await listApi.listByGroup({ isMobile: false, isPortal: true });
    if (res.errcode === 0) {
      if (Array.isArray(res.data)) {
        const notAllApps = res.data.filter((x) => {
          return x.code !== 'all';
        });
        this.appList = [];
        this.childList = [];
        notAllApps.forEach((item) => {
          item.children = item.children ? item.children : [];
          item.children.forEach((ele) => {
            ele.appGroupName = item.name;
            this.childList.push(ele);
          });

          this.appList.push(item);
        });

        this.appGroupList = res.data;
      }
      if (init) {
        this.initApp();
      }
    }
  }

  openBlank() {
    var href: any = location.href;
    var iframeAction: any = href.match(/%26iframeAction%3Ddetail/g);
    if (Array.isArray(iframeAction) && iframeAction.length > 1) {
      for (let i = 0; i < iframeAction.length - 1; i++) {
        href = href.replace('%26iframeAction%3Ddetail', '');
      }
    }

    const url = `${href}${
      href.indexOf('?') > -1 ? '&' : '?'
    }T=${localStorage.getItem('token')}`;

    window.open(url, '_blank');
  }

  /**
   * 获取当前用户信息
   * 获取用户信息在loggedUserInfo组件中会初始化，此方法理论上不应该再执行
   */
  async getUserInfo() {
    let userInfo: any = sessionStorage.getItem('user');
    let res: any = {};
    if (!userInfo) {
      res = await OAuthApi.getUserInfo();
    } else {
      res = {
        errcode: 0,
        data: JSON.parse(userInfo),
      };
    }
    if (res.errcode === 0) {
      const info: any = res.data;
      this.setUserId(info);
      sessionStorage.setItem('user', JSON.stringify(info));
      if (localStorage.getItem('temp')) {
        const tempArr = JSON.parse(localStorage.getItem('temp') || '[]');
        if (tempArr > 2) {
          tempArr.shift();
        }
        localStorage.setItem('temp', JSON.stringify(tempArr));
      } else if (localStorage.getItem('daily')) {
        const dailyArr = JSON.parse(localStorage.getItem('daily') || '[]');
        if (dailyArr > 2) {
          dailyArr.shift();
        }
        localStorage.setItem('daily', JSON.stringify(dailyArr));
      } else {
        //Else Empty block statement
      }
      localStorage.setItem(
        'userId',
        JSON.parse(sessionStorage.getItem('user') || '').id,
      ); //用户ID
      // 判断当前用户角色
      const isAppAdmin: boolean = info.permissions.includes('APP_MNG');
      const isSysAdmin: boolean = info.permissions.includes('SYS_MNG');
      const isRootAdmin: boolean = info.permissions.includes('ADMIN');
      const isAdmin: boolean = isAppAdmin || isSysAdmin || isRootAdmin;
      const isDeptManager: boolean = info.deptManager;
      this.setIsAdmin(isAdmin);
      this.setRootAdmin(isRootAdmin);
      this.setAdmin(isSysAdmin || isRootAdmin);
      this.setUserInfo(info);
      // 设置是否特权人
      const isWORKFLOW_ADMIN: boolean =
        info.permissions.includes('WORKFLOW_ADMIN');
      this.setIsPrivilegedPerson(isWORKFLOW_ADMIN);
      this.setIsAppAdmin(isAppAdmin);
      this.setIsDeptManager(isDeptManager);

      // 禁止无权限访问流程查询页面
      if (
        !isSysAdmin &&
        !isRootAdmin &&
        this.$route.name &&
        ['queryInstance', 'queryParticipantWorkItem'].includes(this.$route.name)
      ) {
        this.$router.replace({ name: 'myUnfinishedWorkItem' });
      }
      // 禁止超管访问流程委托页面
      if (
        isRootAdmin &&
        this.$route.name &&
        ['delegationWorkflow'].includes(this.$route.name)
      ) {
        this.$router.replace({ name: 'myUnfinishedWorkItem' });
      }
      if (info.lastLoginTime) {
        const login_jump = localStorage.getItem('login_jump');
        if (!login_jump || login_jump === 'false') {
          localStorage.setItem('login_jump', 'true');
          const vm = this;
          this.$message.info({
            content: `${this.$t('languages.common.lastLoginTime').toString()}${
              info.lastLoginTime
            }`,
            icon: () => {
              return vm.$createElement('span', {
                class: 'aufontAll h-icon-all-clock-circle',
                style: {
                  color: 'rgba(17,18,24,0.25)',
                  'margin-right': '8px',
                },
              });
            },
          });
        }
      }
    }
  }

  getDownloadUrlNew(refId: string) {
    return getDownloadUrlNew.getImageUrl(refId);
  }

  /**
   * 获取主题外观
   */
  get outwardType() {
    return this.$store.state.outwardType;
  }

  /**
   * 应用列表/流程中心返回按钮
   */
  goToBack() {
    const query: any = this.$route.query;

    let url = '';
    if (query.return && query.return !== 'undefined') {
      url = query.return;
    } else if (
      ['appPortalRuntime', 'applicationList'].includes(this.$route.name)
    ) {
      url = '/app-list/applications';
    } else {
      url = '/portal-page/defaultPortalDashboard';
    }
    this.$router.push({
      path: url,
    });
  }

  /**
   * 应用下拉列表点击事件
   */
  onAppListClick(item) {
    this.appListVisible = false;
    this.setAppPath('');
    this.goToDetail(item);
  }
}
</script>
<style lang="less">
.left-app-list-popover {
  top: 50px !important;
  left: 50px !important;
  padding: 4px 0;
  background: #ffffff;
  box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
  border-radius: 2px;
  .ant-popover-inner-content {
    padding: 4px 0;
  }
  .ant-popover-inner {
    box-shadow: none;
  }
  .app-list {
    margin-bottom: 0;
    width: 220px;
    min-height: 208px;
    max-height: calc(100vh - 106px);
    overflow: auto;
  }
  .app-list-item {
    padding: 9px 14px;
    display: flex;
    align-items: center;
    &:hover {
      background: #eef4fd;
    }
    .item-icon-box {
      margin-right: 6px;
      background: #ffffff;
      border-radius: 1px;
      border: 1px solid #eeeeee;
      .item-icon {
        width: 16px;
        height: 16px;
        display: inherit;
      }
      .svg-icon {
        width: 14px;
        height: 14px;
        margin: 1px;
        display: inherit;
      }
    }
    .item-text {
      width: 172px;
      color: #111218;
      line-height: 22px;
      font-size: 14px;
      .item-name {
        margin-bottom: 0;
        white-space: nowrap;
        overflow: hidden;
        text-overflow: ellipsis;
      }
    }
  }
}
</style>
<style lang="less" scoped>
@import "../../../styles/themes/default.less";
@import '~cloudpivot-list/application/src/components/pc/style/custom-themes';
@menu-box-shadow: 0px 4px 11px 0px rgba(35, 110, 235, 0.05);
.left-icon {
  margin-right: 10px;
  font-weight: 600;
  cursor: pointer;
  .aufontAll {
    font-size: 17px;
  }
  color: #111218;
  &:hover {
    opacity: 0.8;
  }
}
.left-content {
  padding: 4px 8px;
  border-radius: 2px;
  cursor: pointer;
  &:hover {
    background: rgba(0,30,116,0.06);
  }
  /deep/.ant-popover-open {
    display: flex;
    align-items: center;
  }
  .app-info {
    display: flex;
    align-items: center;
    .app-icon {
      background: #FFFFFF;
      border-radius: 1px;
      border: 1px solid #EEEEEE;
      width: 20px;
      height: 20px;
      .item-icon {
        width: 100%;
        height: 100%;
        display: inherit;
      }
      .svg-icon {
        padding: 2px;
        width: 100%;
        height: 100%;
        display: inherit;
      }
    }
    .app-name {
      margin-left: 6px;
      margin-right: 8px;
      font-size: 16px;
      font-weight: 600;
      color: #111218;
      line-height: 22px;
      max-width: 130px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    >.aufontAll {
      font-size: 12px;
      color: #A5A5A5;
      margin-top: 1px;
    }
  }
}
.route-name {
  padding-left: 2px;
  font-size: 16px;
  font-weight: 600;
  color: #111218;
}
.common-header {
  height: 64px;
  min-width: 1024px;
  padding: 0 @base4-padding-lg!important;
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: @base4-padding-lg;
  box-shadow: 0px 1px 0px 0px #eeeeee !important;
  &.header{
    background: @headBGColor;
  }
  &.dark{
    box-shadow: unset !important;
    >.header-left{
      &-width{
        .app-list{
          >i{
            color: #fff;
          }
          &:hover{
            background: @hoverColor;
          }
        }
      }
      .route-name{
        color: #fff;
      }
    }
    .left-icon,
    .app-name {
      color: #fff;
    }
  }
  > .header-left {
    display: flex;
    align-items: center;
    height: 100%;
    &-width {
      width: 226px;
      .app-list {
        position: relative;
        margin-right: 33px;
        width: 30px;
        height: 30px;
        background: transparent;
        border-radius: 2px;
        text-align: center;
        line-height: 30px;
        cursor: pointer;
        &:hover,
        // &.app-list-open{
        //   background: #EEF4FD;
        // }
        > i {
          font-size: 20px;
          color: @highlightColor;
        }

        &::after {
          content: "";
          position: absolute;
          width: 1px;
          height: 20px;
          background: #dddddd;
          right: -16px;
          top: 5px;
        }
      }
      .app-list-menu{
        position: fixed;
        top: 45px;
        left: 0;
        padding-top: 19px;
        .app-list-menu-2{
          width: 264px;
          height: 240px;
          background: #FFFFFF;
          box-shadow: 2px 0px 11px 0px rgba(199, 205, 215, 0.5), 1px 0px 0px 0px #EEEEEE;
          padding: 4px 0;
            .app-list-ul {
              width: 264px;
              height: auto;
              min-height: 240px;
              background-color: #fff;
              .app-list-li {
                width: 100%;
                height: 46px;
                padding: 0 27px;
                color: #111218;
                line-height: 46px;
                font-weight: 400;
                cursor: pointer;
                font-size: 16px;
                display: flex;
                align-items: center;
                i{
                  color: #111218;
                  margin-right: 12px;
                  font-size: 20px;
                }
                &:hover{
                  background-color: rgba(0, 30, 116, 0.06);
                }
              }
            }
        }
      }
    }
  }
  > .header-right {
    display: flex;
    justify-content: space-between;
    flex: 1;
    height: 100%;
    > .menu-right {
      flex: 1;
      display: flex;
      justify-content: flex-end;
      > ul {
        display: flex;
        align-items: center;
        margin-bottom: 0 !important;
        > li {
          margin-right: 24px;
          font-weight: 400;
          height: 20px;
          min-width: 20px;
          color: #111218;
          font-size: 14px;
          text-align: center;
          cursor: pointer;
          display: flex;
          align-items: center;
          &:hover {
            color: @highlightColor;
          }
          i {
            // margin: 0 8px 0 6px;
            font-size: 20px;
            &.small {
              margin: -3px 0 0 6px;
              font-size: 12px;
            }
          }
          &:last-child{
            margin-right: 0;
          }

          /deep/.icon-wrapper {
            cursor: pointer;
            em {
              font-size: 20px;
            }
            &:hover {
              color: @highlightColor;
              em {
                color: @highlightColor;
              }
            }
          }
          /deep/em.icon-wrapper {
            font-size: 12px;
            margin-left: 5px;
          }
        }
      }

      &.dark{
        ul li {
          opacity: 0.8;
          &:hover{
            opacity: 1;

          }
          /deep/.icon-wrapper {
            color: #fff;
            &:hover {
              em {
                color: #fff;
              }
            }
          }

          /deep/.notice-icon-wrapper {
            color: #fff;
            &:hover {
              em {
                color: #fff;
              }
            }
          }
        }
      }
    }
  }
}
</style>

<style lang="less">
.help-center {
  .ant-popover-inner {
    background-color: transparent;
    box-shadow: none;
  }
  .ant-popover-inner-content {
    padding: 0;
    .help-placeholder {
      width: 100%;
      height: 4px;
    }
    .help-content {
      box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
      background-color: #fff;
      margin-top: 0px;
      padding: 4px 0;
    }
  }

  .ant-popover-arrow {
    display: none;
  }
}

.help-center2 {
  padding-top: 0 !important;
  .ant-popover-inner {
    background-color: transparent;
    box-shadow: none;
  }
  .content-placeholder {
    height: 4px;
    width: 100%;
    background: transparent;
  }
  .content-box {
    box-shadow: 0px 2px 8px 0px rgba(0, 0, 0, 0.15);
    background-color: #fff;
  }
  .ant-popover-inner-content {
    padding: 15px 0 0;
  }
  .ant-popover-arrow {
    display: none;
  }
}

.search-wrapper {
  top: 60px !important;
  animation: none !important;
  position: fixed !important;
  padding-top: 0 !important;
  left: 0 !important;
  // z-index: -1!important;
  .ant-popover-inner {
    box-shadow: 0 4px 6px #eeeff2;
  }
  .ant-popover-inner-content {
    padding: 0;
    width: 100vw;
    position: relative;
  }
  .search-wrapper-in {
    height: calc(100vh - 56px);
    overflow: auto;
  }
  .ant-popover-inner {
    background: #f1f2f6;
  }
}
</style>
<style lang="less">
@keyframes amplify {
  from {
    transform: translate(-50%, -50%) scale(0.5);
    opacity: 0;
  }
  60% {
    opacity: 0.4;
  }
  90% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(1);
  }
  to {
    transform: translate(-50%, -50%) scale(1);
    opacity: 0;
  }
}
</style>
