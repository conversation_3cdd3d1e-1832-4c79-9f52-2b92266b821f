/*
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
*/
@import '../themes/default.less';

.sheet-menus {
  & .ant-popover-inner-content {
    width: 120px;
    padding: 0;
  }
}

.row-menus {
  & > li {
    padding: 0 12px;
    cursor: pointer;
    height: 32px;
    line-height: 32px;

    &:hover {
      background-color: @primary-1;
    }
  }
}

.field {
  padding: @base10-padding-sm 0;

  &__label {
    margin-right: @base4-padding-xs;
    // flex: 0 0 90px; 
    .field__label-div{
      flex: 0 0 90px;
    }
    &.top {
      padding-top: 0.3em;
    }
  }
  &__control {
    color: @light-color-1;

    .ant-select-selection--multiple {
      .ant-select-selection__rendered {
        margin-left: 11px;
      }
      .ant-select-selection__placeholder {
        margin-left: 0px;
      }
    }

    & div.number:before,
    & div.logic:before {
      content: '';
    }
  }
}

.field.required > .field__label,
.sheet .required {
  position: relative;

  &:before {
    content: '*';
    color: @error-color;
    position: absolute;
    left: -0.5em;
  }
}

.field.edit {
  .field__control {
    .radio,
    .checkbox {
      padding-top: 0.3em;
    }
  }
}

.field.error > .field__control,
.sheet .error {
  .ant-select-selection,
  .ant-select > div,
  .ant-input-number,
  .h3-input-number,
  .date-picker input,
  .ant-time-picker-input,
  .ant-input {
    color: @error-color;
    border-color: @error-bd-color;
    background-color: @error-bg-color;
  }

  .ant-upload > .ant-btn,
  .ant-upload-select-picture-card,
  .pca-selector--content {
    border-color: @error-bd-color;
    background-color: @error-bg-color;
  }

  .ant-radio-group,
  .ant-checkbox-group {
    border: 1px solid @error-bd-color;
    border-radius: @border-radius-lg;
    padding: @base4-padding-base @base4-padding-xs;
  }

  .h3-organization__label {
    border: 1px solid @error-bd-color;
    border-radius: @border-radius-lg;
    background-color: @error-bg-color;
  }
}
.sheet-tab .ant-radio-group {
  max-height: 240px;
  overflow: auto;
}
.ant-select-selection-selected-value {
  height: 30px;
}

.ant-pagination-options {
  line-height: 1;
  height: 32px;

  & > .ant-pagination-options-quick-jumper input {
    vertical-align: baseline;
  }
}

.unwritable > .ant-upload-list .anticon-cross {
  display: none;
}

.full-modal {
  width: 100%;
  height: 100%;
  top: 0;
  padding: 0;
  transition: cubic-bezier(0.23, 1, 0.32, 1);

  & > .ant-modal-content {
    height: 100%;
    display: flex;
    flex-direction: column;
    border-radius: 0;

    & > .ant-modal-body {
      flex-grow: 1;
      overflow: auto;
      height: calc(100vh);
    }

    & > .ant-modal-footer > div {
      //text-align: center;
    }
  }
}

.ant-modal-wrap {
  overflow: auto !important;
}

//时间范围选择器底部自定义样式
.ant-tag-blue {
  background: rgba(240, 247, 255, 1) !important;
  color: rgba(41, 112, 255, 1) !important;
  border-color: rgba(41, 112, 255, 1) !important;
}

// 表单 边框模式 样式 👇
@form-border-padding-left: 10px;
.has-form-boder-mixin() {
  & > div {
    padding: 12px 12px;
    &.field__control {
      display: flex;
      align-items: center;
      height: 100%;
      padding-left: 0;
      padding: 0;
      & > div,
      & div.text,
      & div.number,
      & div.date,
      & div.time,
      & div.staffselector,
      & div.relevance-form-modal,
      & div.relevanceform,
      & div.relevanceformex,
      & div.ant-select,
      & span.ant-calendar-picker,
      & span.ant-time-picker,
      & span.ant-input-affix-wrapper,
      & div.h3-organization,
      & input[type='text'],
      & input.ant-input,
      & div.location,
      & input.ant-input-number-input,
      & div.h3-input-number > input {
        width: 100%;
        // height: 100%;
        display: flex;
        align-items: center;
        & > span {
          margin-left: 10px;
        }
        & > div {
          // height: 100%;
          width: 100%;
        }
      }
      & > div.field-wrapper {
        flex-flow: column;
      }

      & span.ant-calendar-picker {
        min-width: unset !important;
      }

      & span.ant-input-affix-wrapper {
        margin-left: 0 !important;
        & .ant-input-suffix {
          z-index: 3;
        }
      }
      & div.ant-input-number,
      & div.h3-input-number,
      & div.h3-textarea,
      & input.ant-input,
      & div.ant-select-selection,
      & div.h3-organization__label.show-select {
        position: relative;
        border-radius: 0;
        border: 0;
        &:focus {
          border: 1px solid #5291ff;
          z-index: 2;
        }
      }
      & div.ant-input-number,
      & div.h3-input-number {
        display: flex;
      }
      & div.relevanceform,
      & div.relevanceformex {
        & > div > a {
          display: flex;
          align-items: center;
          height: 100%;
          padding-left: 10px;
        }
      }
      & div.tox-tinymce {
        border: 0;
      }
      & div.h3-organization__label,
      & div.ant-select-selection {
        display: flex;
        align-items: center;
      }
      & div.ant-input-number-focused,
      & div.h3-input-number-focused,
      & div.ant-select-focused,
      & div.h3-textarea_focus {
        border: 1px solid #5291ff;
        z-index: 2;
      }
      & div.ant-select-selection__rendered {
        width: 100%;
        padding-right: 14px;
      }
      & div.image {
        padding: 10px;
      }
      & > div.ant-input-disabled {
        border-radius: 0;
      }
      & div.h3-textarea.disabled {
        padding: 0;
      }
      & div.radio,
      & div.checkbox,
      & div.logic,
      & div.location,
      & div.signature,
      & div.attachment {
        display: flex;
        align-items: center;
        padding-top: 5px;
        padding-bottom: 5px;
        padding-left: @form-border-padding-left;
        padding-right: @form-border-padding-left;
        & > div {
          width: 100%;
        }
      }
      & div.staffselector,
      & div.staffmultiselector,
      & div.departmentselector,
      & div.departmentmultiselector,
      & div.staffdeptmixed {
        & > div.form-staff-warp {
          height: 100%;
          & > div.form-staff {
            height: 100%;
            padding: @form-border-padding-left;
          }
        }
        & div.h3-organization__label.disabled {
          border: 0 !important;
          border-radius: 0;
        }
      }
      & div.dropdown {
        & > div.items {
          height: 100%;
          display: flex;
          align-items: center;
          padding-left: @form-border-padding-left;
        }
      }
      & div.textarea {
        & > div > div.text {
          padding: @form-border-padding-left;
        }
        & > div > div.editor-html {
          padding: @form-border-padding-left;
        }
      }
      & div.text {
        & > div > div.text {
          padding-left: @form-border-padding-left;
        }
      }
      & div.location {
        padding: 0;
        & > div {
          padding-left: @form-border-padding-left;
          display: flex;
          align-items: center;
        }
        & > div.h3-location {
          padding: 0;
        }
        & .ant-input-affix-wrapper .ant-input:not(:last-child) {
          padding-left: @form-border-padding-left;
        }
      }
    }
  }
}
.form-detail{
  .has-form-border {
    .h3-panel {
      border-bottom: 1px solid rgba(217, 217, 217, 1);

      .h3-panel-body {
        padding: 0;
      }
    }
  }
}
.has-form-border {
  .ant-tabs-tabpane {
    border-top: 1px solid rgba(217, 217, 217, 1);
  }
  // h3-panel 下面的 border
  .h3-panel > .h3-panel-body > .ant-row-flex {
    position: relative;
    border: 1px solid rgba(217, 217, 217, 1); // 左右边框
    border-bottom: 0;
    //border-left: 0;
    //border-top: 0;
    // &.dashed {
    //   border-bottom: 1px solid rgba(217,217,217,1);
    // }
    & .number:before,
    & .logic:before {
      content: '';
    }
    &.form-title-flex {
      border: 0;
    }
    & > .ant-col {
      border-right: 1px solid rgba(217, 217, 217, 1); // col 里面右边框
      & > div.description {
        &::before {
          position: absolute;
          top: 0;
          left: 0;
          content: '';
          width: 100%;
          //border-top:1px solid rgba(217,217,217,1);
          z-index: 1;
        }
      }
      & > div.field {
        min-height: 49px;
        padding: 0;
        align-items: unset;
        &::before {
          position: absolute;
          top: 0;
          left: 0;
          content: '';
          width: 100%;
          //border-top:1px solid rgba(217,217,217,1);
          z-index: 1;
        }
        & > .field__label {
          display: flex;
          align-items: center;
          border-right: 1px solid #D9D9D9;
          background: rgba(245,246,249,0.85);
          margin-right: 0;
        }
        .has-form-boder-mixin();
      }
      & > div.form-sheet {
        &::before {
          position: absolute;
          top: 0;
          left: 0;
          content: '';
          width: 100%;
          //border-top:1px solid rgba(217,217,217,1)
        }
      }

      div.description {
        padding: 8px;
      }

      div.system {
        & > div {
          &.field__label {
            margin-left: 0;
          }
          &.field__control {
            padding-left: 8px;
          }
        }
      }

      .input-image {
        .field__control {
          padding-bottom: 0;
          padding-left: 0;
        }
      }
    }
    & > .ant-col-0 {
      border: 0;
    }
    & > .ant-col:last-child {
      border-right: 0;
    }
    .field.required > .field__label {
      position: relative;
      &:before {
        content: '*';
        color: @error-color;
        position: absolute;
        left: 0.35em;
      }
    }
    .sheet .required {
      position: relative;
      &:before {
        content: '*';
        color: @error-color;
        position: absolute;
        left: -0.5em;
      }
    }
    .field__control {
      .ant-upload-select-picture-card {
        margin-bottom: 0;
      }
      .dropdown {
        & > div {
          display: block;
        }
      }
      .ant-checkbox-group,
      .ant-radio-group {
        vertical-align: top;
      }
      .ant-checkbox-wrapper,
      .ant-radio-wrapper {
        vertical-align: top;
        top: 0;
      }
    }
  }
  //去除不可见的控件的边框
  .h3-panel > .h3-panel-body > .ant-row-flex.not-visible-border {
    border: none;
    > .ant-col > .form-container-border > .form-container-body > .ant-row-flex {
      border-left: 1px solid #d9d9d9;
    }
  }

  & > .h3-panel {
    & > .h3-panel-body {
      & > .ant-row-flex:last-child {
        // border-bottom: 1px solid rgba(217,217,217,1);
      }
    }
  }
  .form-sheet {
    padding: 0;
    & > div.h3-panel {
      & > div.h3-panel-header,
      & > div.h3-panel-right {
        padding-left: 12px;
        padding-right: 12px;
      }
      .h-icon-all-right-o {
        margin-right: 24px;
      }
      & > div.h3-panel-body {
        & > div.ant-divider,
        & > div.actions {
          padding-left: 12px;
          padding-right: 12px;
        }
        & div.sheet {
          border-left: 0;
          border-right: 0;
          border-radius: 0;
          & > div.sheet__row.total {
            & div.sheet__col {
              border-top: 1px solid #e8e8e8;
              border-bottom: 0;
            }
          }
          & > div.sheet__body {
            & > div.sheet__row:last-child {
              & div.sheet__col {
                border-bottom: 0;
              }
            }
          }
        }
        & div.sheet__row.scrollbar {
          min-height: 0;
        }
      }
    }
  }
}
// 针对 系统控件 特殊处理
.has-form-border > div.ant-row-flex,
.has-form-border .ant-tabs-tabpane > div.ant-row-flex,
.has-form-border .form-container-body > div.ant-row-flex {
  position: relative;
  // border-left: 0;
  // border-right: 1px solid rgba(217, 217, 217, 1);
  //border-bottom: 0;
  //border-top: 0;

  border: 1px solid rgba(217,217,217,1);
  border-top: 0;
  &.dashed {
    border-bottom: 0;
  }
  &.dashed::before {
    position: absolute;
    left: 0;
    bottom: -1px;
    content: '';
    width: 100%;
    border-top:1px solid rgba(217,217,217,1);
    z-index: 1;
  }

  & > .ant-col {
    border-right: 1px solid rgba(217, 217, 217, 1);
    & > div.form-sheet,
    & > div.desc {
      &::before {
        position: absolute;
        left: 0;
        bottom: 0;
        content: '';
        width: 100%;
        // border-top: 1px solid rgba(217, 217, 217, 1);
        z-index: 1;
      }
    }
    & > div.description {
      &::before {
        position: absolute;
        bottom: 0;
        left: 0;
        content: '';
        width: 100%;
        // border-top: 1px solid rgba(217, 217, 217, 1);
        z-index: 1;
      }
    }
    & > div.field {
      padding: 0;
      align-items: unset;
      &::before {
        position: absolute;
        left: 0;
        bottom: 0;
        content: '';
        width: 100%;
        // border-top: 1px solid rgba(217, 217, 217, 1);
        z-index: 1;
      }
      .has-form-boder-mixin();
      & > .field__label {
        border-right: 1px solid rgba(217, 217, 217, 1);
        background-color: rgba(250, 250, 250, 1);
        margin-right: 0;
        display: flex;
        align-items: center;
        .field__label-div {
          padding: 0;
        }
      }
      & > div {
        // padding: 8px 12px;
        &.field__control {
          padding-left: 0;
          display: flex;
          align-items: center;
          & > div {
            flex-basis: 100%;
          }
        }
      }
    }

    div.description {
      padding: 8px;
    }

    div.system {
      & > div {
        &.field__label {
          margin-left: 0;
          min-width: 108px !important;
          max-width: 108px !important;
        }
        &.field__control {
          padding-left: 8px;
        }
      }
    }

    .input-image {
      .field__control {
        padding-bottom: 0;
        padding-left: 0;
      }
    }
  }
  & .h3-panel {
    border-bottom: 0;
  }
  & > .ant-col:last-child {
    border-right: 0;
  }

  // & > .ant-col:first-child{
  //   border-left: 0;
  // }
  & > .ant-col-0 {
    border: 0;
  }
  .field.required > .field__label {
    position: relative;
    &:before {
      content: '*';
      color: @error-color;
      position: absolute;
      left: 0.35em;
    }
  }
  .sheet .required {
    position: relative;
    &:before {
      content: '*';
      color: @error-color;
      position: absolute;
      left: -0.5em;
    }
  }
  .field__control {
    .ant-upload-select-picture-card {
      margin-bottom: 0;
    }
    .dropdown {
      & > div {
        display: block;
      }
    }
    .ant-checkbox-group,
    .ant-radio-group {
      vertical-align: top;
    }
    .ant-checkbox-wrapper,
    .ant-radio-wrapper {
      vertical-align: top;
      top: 0;
    }
    .relevanceform .ant-input-affix-wrapper {
      margin-left: 0 !important;
    }
    .relevanceformex .ant-input-affix-wrapper {
      margin-left: 0 !important;
    }
  }
}
.has-form-border  > div.ant-row-flex:last-child, .has-form-border .ant-tabs-tabpane > div.ant-row-flex:last-child,
.has-form-border  > div.ant-row-flex:last-child, .has-form-border .form-container-body > div.ant-row-flex:last-child {
  border-bottom: 1px solid #d9d9d9;
}

.has-form-border div.form-title-flex {
  border: 0;
  & > .ant-col {
    border: 0;
  }
}
.has-form-border  > div.not-visible-border {
  border: none;
}

.has-form-border div.form-title-flex + div.ant-row-flex {
  border-top: 1px solid rgba(217, 217, 217, 1);
}
.ant-col .vertical .field__control {
  // padding-left: 12px;
}

.field.vertical {
  .field__label .field__label-div{
    flex: 0 0 100%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .field__control {
    margin-left: 0px !important;
  }
}

.sheet {
  .ant-upload-list {
    display: flex;
    justify-content: center;
  }
  .ant-upload-list-picture-card .ant-upload-list-item {
    margin: 0 0 8px 0 !important;
  }
  .ant-upload-list-picture-card-container {
    margin: 0 0 8px 0 !important;
    margin-left: 8px !important;
    &:first-child {
      margin-left: 0px !important;
    }
  }
}


.ant-input[disabled]{
  color: rgba(0, 0, 0, 0.45);
}
.ant-btn-disabled, .ant-btn.disabled, .ant-btn[disabled], .ant-btn-disabled:hover, .ant-btn.disabled:hover, .ant-btn[disabled]:hover, .ant-btn-disabled:focus, .ant-btn.disabled:focus, .ant-btn[disabled]:focus, .ant-btn-disabled:active, .ant-btn.disabled:active, .ant-btn[disabled]:active, .ant-btn-disabled.active, .ant-btn.disabled.active, .ant-btn[disabled].active{
  color: rgba(0, 0, 0, 0.45);
}