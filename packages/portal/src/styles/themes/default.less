/*
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
*/
@import '~cloudpivot/common/common.less';
@import "../color/colors";
// // The prefix to use on all css classes from bpm.
// @prefix : bpm;
// @layout-min-width : 1024px; //布局最小尺寸
// // -------- Colors -----------
// @primary-color : #2970FF; // 品牌色
// @success-color : #32B683;
// @error-color : #F4454E;
// @error-bd-color : #e0b4b4;
// @error-bg-color : #fff6f6;
// @warning-color : #FAAD14;
// @highlight-color : @primary-color;
// @normal-color : #d9d9d9;


// // Color used by default to control hover and active backgrounds and for
// // alert info backgrounds.
@primary-1: color(~`colorPalette("@{primary-color}", 1)`); // 背景选择颜色
// @primary-2: color(~`colorPalette("@{primary-color}", 2)`); // unused
// @primary-3: color(~`colorPalette("@{primary-color}", 3)`); // unused
// @primary-4: color(~`colorPalette("@{primary-color}", 4)`); // unused
// @primary-5: color(~`colorPalette("@{primary-color}", 5)`); // 触发/焦点 Hover/Focus
// @primary-6: @primary-color; // 品牌色
// @primary-7: color(~`colorPalette("@{primary-color}", 7)`); // 点击 Active
// @primary-8: color(~`colorPalette("@{primary-color}", 8)`); // unused
// @primary-9: color(~`colorPalette("@{primary-color}", 9)`); // unused
// @primary-10: color(~`colorPalette("@{primary-color}", 10)`); // unused


// // Background color & border color
// @main-background : #F4F6FC;

// @white-background : #fff;
// @base-border-color : #EAEDF3; // Used on @white-background
// @base-border-color-1 : #D8D8D8;
// // avator color && applaction color
// @random-color-1 : #40A9FF;
// @random-color-2 : #36CFC9;
// @random-color-3 : #73D13D;
// @random-color-4 : #FFA940;
// @random-color-5 : #FF6851;


// // font-family desc
// @font-family : 12px/1.5 tahoma,
// arial,
// 'Hiragino Sans GB',
// '\5b8b\4f53',
// sans-serif;
// //@font-family  : "Chinese Quote", -apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif,
// //"Apple Color Emoji", "Segoe UI Emoji", "Segoe UI Symbol";
// @code-family : "SFMono-Regular",
// Consolas,
// "Liberation Mono",
// Menlo,
// Courier,
// monospace;

// // font-color light && drak
// @light-color-1 : rgba(0, 0, 0, 0.85); // 主字体颜色
// @light-color-2 : rgba(0, 0, 0, 0.65); // 次字体颜色
// @light-color-3 : rgba(0, 0, 0, 0.45); // 提示性字体颜色
// @light-color-4 : rgba(0, 0, 0, 0.25); // 禁止字体颜色

// @dark-color-1 : rgba(255, 255, 255, 1); // 强调字体颜色
// @dark-color-2 : rgba(255, 255, 255, 0.85); // 主字体颜色
// @dark-color-3 : rgba(255, 255, 255, 0.65); // 次字体颜色
// @dark-color-4 : rgba(255, 255, 255, 0.45); // 禁止字体颜色

// // font-size map line-height
// @font-size-40 : 40px;
// @font-size-38 : 38px;
// @font-size-30 : 30px;
// @font-size-24 : 24px;
// @font-size-22 : 22px;
// @font-size-20 : 20px;
// @font-size-18 : 18px;
// @font-size-16 : 16px;
// @font-size-14 : 14px;
// @font-size-13 : 13px;
// @font-size-12 : 12px;
// @font-size-10 : 10px;


// // line-height map font-size
// @line-height-1 : 46px;
// @line-height-2 : 38px;
// @line-height-3 : 32px;
// @line-height-4 : 30px;
// @line-height-5 : 28px;
// @line-height-6 : 24px;
// @line-height-7 : 22px;
// @line-height-8 : 40px;
// @line-height-9 : 36px;
// @line-height-10 : 36px;
// @line-height-11 : 20px;
// @line-height-12 : 18px;

// // font-weight
// @font-weight-base : 400;
// @font-weight-md : 500;
// @font-weight-lg : 600;

// // base4 paddings  4的倍数
// @base4-padding-lg : 24px; // containers
// @base4-padding-md : 16px; // small containers and buttons
// @base4-padding-sm : 12px; // Form controls and items
// @base4-padding-xs : 8px; // small items
// @base4-padding-base : 4px; // base

// // base10 paddings 10的倍数
// @base10-padding-lg : 30px;
// @base10-padding-md : 20px;
// @base10-padding-sm : 10px; // base

// //  radius  list
// @border-radius-base : 2px;
// @border-radius-lg : 4px;

// // z-index list
