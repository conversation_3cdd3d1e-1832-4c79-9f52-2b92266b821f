/*
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
*/
import defaultRoutes from './routes';
import extendRoutes from '../../extends/routes';
import { utils } from 'cloudpivot/common';
import { loadRemoteComponent } from 'cloudpivot-form/form/utils/loadRemoteComponent';

export const getRemoteInfo = () => {
  localStorage.removeItem('remoteSpecificModelConfig');
  localStorage.removeItem('remoteCustomThemes');
  return new Promise((resolve, reject) => {
    const host = '/extensions/route';
    loadRemoteComponent({
      url: `${host}/index.js?time=${+new Date()}`,
      scope: 'extendRoute',
      module: './route',
    })
      .then((res: any) => {
        let routeMap = utils.RouteHelper.assign(defaultRoutes, extendRoutes);
        routeMap = utils.RouteHelper.assign(routeMap, res.default.extendsRoute);
        const routes = utils.RouteHelper.toRoutes(routeMap);
        resolve({
          routes,
          remoteSpecificModelConfig: res.default.specificModelConfig,
          remoteCustomThemes: res.default.themes,
          changeWorkflowCenterMenu: res.default.changeWorkflowCenterMenu,
        });
      })
      .catch(() => {
        const routeMap = utils.RouteHelper.assign(defaultRoutes, extendRoutes);
        const routes = utils.RouteHelper.toRoutes(routeMap);
        resolve({
          routes,
        });
      });
  });
};

export default getRemoteInfo;
