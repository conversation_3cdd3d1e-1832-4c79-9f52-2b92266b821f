<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <a-config-provider :locale="locale">
    <div id="app">
      <router-view class="bpm-container" />
    </div>
  </a-config-provider>
</template>

<script lang="ts">
import { ConfigProvider } from '@h3/antd-vue';
import enUS from '@h3/antd-vue/lib/locale-provider/en_US';
import zhCN from '@h3/antd-vue/lib/locale-provider/zh_CN';
import { Component, Vue } from 'vue-property-decorator';
import { themesApi } from 'cloudpivot/api';
import { updateTheme } from 'cloudpivot/common/src/components/pc/tools/settingConfig';
import getDownloadUrlNew from 'cloudpivot/common/src/utils/getDownloadUrlNew';

@Component({
  components: {
    AConfigProvider: ConfigProvider,
  },
})
export default class App extends Vue {
  async created() {
    await themesApi.getThemesInfo().then((res: any) => {
      this.$store.commit('setThemsConfig', res);
      if (res.icon) {
        this.changeFavicon(this.getIcon(res.icon));
        if (res.name && document.title.indexOf('奥哲云枢-') !== -1) {
          document.title = document.title.replace('奥哲云枢-', res.name + '-');
        } else if (res.name && document.title.indexOf('云枢-') !== -1) {
          document.title = document.title.replace('云枢-', res.name + '-');
        } else {
          //Else Empty block statement
        }
      }
      if (res.name) {
        localStorage.setItem('themsInfo', JSON.stringify({ title: res.name }));
      } else {
        localStorage.removeItem('themsInfo');
      }

      updateTheme({
        color: res.color || 'default',
        outwardType: res.outward || 'light',
        store: this.$store,
      });
    });
  }

  get params() {
    const url = location.search;
    const theRequest: any = new Object();
    if (url.indexOf('?') !== -1) {
      const str: string = url.substr(1);
      const strs: string[] = str.split('&');
      strs.forEach((res: string) => {
        theRequest[res.split('=')[0]] = decodeURI(res.split('=')[1]);
      });
    }
    //
    return theRequest;
  }

  get locale() {
    switch (this.$i18n.locale) {
      case 'en':
        return enUS;
      case 'zh':
      default:
        return zhCN;
    }
  }

  // 修改浏览器标签页上的小图标
  changeFavicon(link) {
    if (!link) {
      return;
    }
    let $favicon: any = document.querySelector('link[rel="icon"]');
    if ($favicon !== null) {
      $favicon.href = link;
    } else {
      $favicon = document.createElement('link');
      $favicon.rel = 'icon';
      $favicon.href = link;
      // @ts-ignore
      document.head.appendChild($favicon);
    }
  }

  getIcon(refId: string) {
    return refId ? this.getDownloadUrlByRefId(refId) : '';
  }

  getDownloadUrlByRefId(refId: string): string {
    return getDownloadUrlNew.getImageUrl(refId, 'logo');
  }
}
</script>
