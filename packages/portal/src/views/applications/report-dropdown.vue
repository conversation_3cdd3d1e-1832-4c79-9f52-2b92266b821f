<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <div>
    <a-select
      v-model="currentValue"
      showSearch
      class="custom-report-dropdown"
      dropdownClassName="custom-report-dropdown-item-box"
      style="width: 100%"
      :placeholder="$t('languages.common.placeholder2')"
      :mode="mode"
      optionFilterProp="children"
      :filterOption="filterOption"
      @change="onChange"
    >
      <!-- <a-select-option :disabled="true" v-if="hasEmpty" key ><span style="color: #bfbfbf;">{{
        emptyValue
      }}</span></a-select-option> -->

      <template v-for="opt in options">
        <a-select-option v-if="typeof opt !== 'object'" :key="opt">
          <span :title="opt" class="select-drop-item">{{ opt }}</span>
        </a-select-option>
        <a-select-option v-else-if="typeof opt === 'object'" :key="opt.key">
          <span :title="opt.label" class="select-drop-item">{{
            opt.label
          }}</span>
        </a-select-option>
      </template>
    </a-select>
  </div>
</template>

<script lang="ts">
import { DataItemType } from 'cloudpivot-form/form/schema';
import { Select, Tooltip } from '@h3/antd-vue';
import axios from 'axios';
import { Component, Model, Prop, Vue } from 'vue-property-decorator';
import { dictionaryApi, listApi } from 'cloudpivot/api';
import common from 'cloudpivot/common';

@Component({
  name: 'report-dropdown',
  components: {
    ATooltip: Tooltip,
    ASelect: Select,
    ASelectOption: Select.Option,
  },
})
export default class ReportDropdown extends Vue {
  @Prop({
    default() {
      return [];
    },
  })
  value!: any[];

  @Model('change')
  @Prop({
    default: () => ({}),
  })
  field!: any;

  @Prop({
    default: '',
  })
  formula!: string;

  currentPage: number = 0; //当前页码

  get mode() {
    if (this.isMultiple) {
      return 'multiple';
    }
    return 'default';
  }

  get isMultiple() {
    return (
      ['In', 'NotIn'].includes(this.formula) ||
      this.field.dataType === DataItemType.Checkbox ||
      this.field.dataType === DataItemType.DropdownMulti
    );
  }

  get currentValue() {
    // 获取值，新
    const value: any[] = [];
    this.value.forEach((i: any) => {
      const option = this.options.find((item: any) => {
        if (typeof item === 'object') {
          return item.label === i;
        } else {
          return item === i;
        }
      });
      if (option) {
        value.push(typeof option === 'object' ? option.key : option);
      } else {
        value.push(...i.split(';'));
      }
    });
    return value;
  }

  set currentValue(value) {
    let val: any = '';
    if (this.isMultiple) {
      val = value.map((v: any) => {
        const opt = this.options.find((o: any) =>
          typeof o === 'object' ? o.key === v : o === v,
        );
        if (opt) {
          return typeof opt === 'object' ? opt.label : opt;
        } else {
          return v;
        }
      });
    } else {
      const opt = this.options.find((o: any) =>
        typeof o === 'object' ? o.key === value : o === value,
      );
      if (opt) {
        val = typeof opt === 'object' ? opt.label : opt;
      } else {
        val = value;
      }
    }
    this.$emit('input', val);
  }

  created() {
    this.init();
  }

  async init() {
    const vm: any = this;
    const params = {
      schemaCode: this.field.parentSchemaCode || this.field.schemaCode,
    };
    axios.get('/api/app/bizproperty/list', { params }).then(async (res) => {
      const data = res.data;
      if (Array.isArray(data)) {
        let item = data.filter((i: any) => {
          return i.code === (vm.field.mainField || vm.field.field);
        });
        if (vm.field.mainField) {
          const subData = item[0].subSchema && item[0].subSchema.properties;
          item = subData.filter((i: any) => {
            return i.code === vm.field.field;
          });
        }
        const optionsController = new common.utils.OptionsStrategyCollector(
          item[0].options,
        );
        this.options = await optionsController.parseOptions(0, '');
      } else {
        return;
      }
    });
  }

  options: any[] = [];

  onChange(val: any[]) {}

  filterOption(input: any, option: any) {
    return (
      option.componentOptions.children[0].text
        .toLowerCase()
        .indexOf(input.toLowerCase()) >= 0
    );
  }

  /**
   * 获取业务模型数据
   */
  getOptions(
    schemaCode: string,
    queryCode: string,
    params: any[],
    sheetDataItem: string,
    orderByFields: string[],
    orderType: number,
    condition: any,
  ) {
    const filters = params.map((x) => {
      let val: any = x.value;
      if (x.propertyCode === 'sequenceStatus') {
        switch (val) {
          case '草稿':
            x.value = 'DRAFT';
            break;
          case '进行中':
            x.value = 'PROCESSING';
            break;
          case '已完成':
            x.value = 'COMPLETED';
            break;
          case '已作废':
            x.value = 'CANCELED';
            break;
          default:
            break;
        }
      }
      switch (x.type) {
        case DataItemType.RelevanceForm:
          val = x.value.id || '';
          break;
        case DataItemType.StaffSingle:
        case DataItemType.StaffMulti:
        case DataItemType.StaffDeptMix:
        case DataItemType.DeptMulti:
        case DataItemType.DeptSingle:
          val = x.value.map((v: any) => ({
            id: v.id,
            type: v.unitType || v.type,
          }));
          val = JSON.stringify(val);
          break;
        case DataItemType.Number:
          if (Array.isArray(x.value)) {
            val = x.value.map((v) => v.toString()).join(';');
          } else {
            val = x.value;
          }
          break;
        default:
          if (Array.isArray(x.value)) {
            val = x.value.map((v) => v.toString()).join(';');
          } else {
            val = x.value;
          }
          break;
      }
      return {
        propertyCode: x.propertyCode,
        propertyValue: val,
        op: 'Eq',
      };
    });
    const options = {
      customDisplayColumns: [sheetDataItem],
    };
    const obj: any = {
      queryCode,
      schemaCode,
      options,
      orderByFields,
      orderType,
      page: 0,
      size: 10000,
      filters,
      condition,
    };
    return listApi.listSkipQueryList(obj).then((res) => {
      if (res.errcode === 0) {
        const data: string[] = [];
        res.data.content.forEach((x: any) => {
          const s = x.data[sheetDataItem];
          let t = '';
          if (s && data.indexOf(s) === -1) {
            if (sheetDataItem === 'sequenceStatus') {
              switch (s) {
                case 'DRAFT':
                  t = '草稿';
                  break;
                case 'PROCESSING':
                  t = '进行中';
                  break;
                case 'COMPLETED':
                  t = '已完成';
                  break;
                case 'CANCELED':
                  t = '已作废';
                  break;
                default:
                  break;
              }
              data.push(t);
            } else {
              data.push(s);
            }
          }
        });
        return data;
      }
      return [];
    });
  }

  getShowOpt(opt: any) {
    if (typeof opt === 'string') {
      return opt;
    } else {
      return opt[0].name;
    }
  }
}
</script>
<style lang="less">
.custom-report-dropdown.ant-select {
  height: 32px;
  .ant-select-selection {
    height: 32px !important;
    .ant-select-selection__rendered {
      line-height: 30px;
    }
  }
}
.custom-report-dropdown-item-box {
  .ant-select-dropdown-menu-item-selected,
  .ant-select-dropdown-menu-item-active:not(
      .ant-select-dropdown-menu-item-disabled
    ) {
    background: rgba(0, 30, 116, 0.06);
  }
}
</style>
