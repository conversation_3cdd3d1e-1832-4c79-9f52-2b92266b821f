<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <div class="report-custom-selector-wrap">
    <a-select
      v-model="current"
      class="custom-radio"
      dropdownClassName="custom-radio-item-box"
      :class="{ 'dynamics-select': current !== 3 }"
      @change="currentChange"
    >
      <a-select-option
        v-if="componentType === 1 || componentType === 3"
        :key="1"
        :value="1"
      >
        {{ $t('languages.common.Applications.Oneself') }}
      </a-select-option>
      <a-select-option
        v-if="componentType === 2 || componentType === 3"
        :key="2"
        :value="2"
      >
        {{ $t('languages.common.Applications.ThisDepartment') }}
      </a-select-option>
      <a-select-option :key="3" :value="3">
        {{ $t('languages.common.Applications.Custom') }}
      </a-select-option>
    </a-select>

    <template v-if="current === 3">
      <div class="customer-pannel">
        <staff-select
          :value="selectValue"
          :options="selectOpts"
          :params="{ sourceType: 'portal', filterType: 'root_display' }"
          @change="onChange"
        />
      </div>
    </template>
  </div>
</template>

<script lang="ts">
import { DataItemType } from 'cloudpivot-form/form/schema';
import rendererComponents from 'cloudpivot-form/form/src/renderer/components/pc';
import { Select } from '@h3/antd-vue';
import { Component, Model, Prop, Vue, Watch } from 'vue-property-decorator';
import { namespace } from 'vuex-class';
enum DataType {
  Staff = 1, // 人员
  Department = 2, // 部门
  Mix = 3, // 混合
}

const UserModule = namespace('System/System');

@Component({
  name: 'ReportCustomSelector',
  components: {
    // ReportStaffSelect: ReportStaffSelect,
    StaffSelect: rendererComponents.StaffSelector,
    ASelect: Select,
    ASelectOption: Select.Option,
  },
})
export default class ReportCustomSelector extends Vue {
  @UserModule.State('loginedUserInfo') userInfo!: any;

  @Prop({
    default: '',
  })
  formula!: string;

  @Prop({})
  field!: any;

  @Model('input', {
    default: () => [],
  })
  value!: any;

  selectValue: any[] = [];

  selectOpts: any = {
    selectOrg: true,
    selectUser: true,
    mulpitle: true,
    showModel: true,
    showSelect: true,
    recursive: true,
  };

  // 控件分类
  get componentType() {
    const dataType = this.field.dataType;
    switch (dataType) {
      case DataItemType.StaffSingle:
      case DataItemType.StaffMulti:
        return DataType.Staff;
      case DataItemType.DeptMulti:
      case DataItemType.DeptSingle:
        return DataType.Department;
      case DataItemType.StaffDeptMix:
        return DataType.Mix;
      default:
        break;
    }
  }

  @Watch('field', {
    immediate: true,
  })
  onFieldChange(val: any) {
    const dataType = val.dataType;
    switch (dataType) {
      case DataItemType.StaffSingle:
        this.selectOpts.selectOrg = false;
        this.selectOpts.selectUser = true;
        this.selectOpts.mulpitle = false;
        break;
      case DataItemType.StaffMulti:
        this.selectOpts.selectOrg = false;
        this.selectOpts.selectUser = true;
        this.selectOpts.mulpitle = true;
        break;
      case DataItemType.DeptMulti:
        this.selectOpts.selectOrg = true;
        this.selectOpts.selectUser = false;
        this.selectOpts.mulpitle = true;
        break;
      case DataItemType.DeptSingle:
        this.selectOpts.selectOrg = true;
        this.selectOpts.selectUser = false;
        this.selectOpts.mulpitle = false;
        break;
      case DataItemType.StaffDeptMix:
        this.selectOpts.selectOrg = true;
        this.selectOpts.selectUser = true;
        this.selectOpts.mulpitle = true;
        break;
      default:
        break;
    }
    if (val.options.root) {
      let root: any = null;
      if (
        this.userInfo.manageDeptList &&
        typeof this.userInfo.manageDeptList === 'string'
      ) {
        try {
          root = JSON.parse(this.userInfo.manageDeptList);
        } catch (error) {}
      } else if (Array.isArray(this.userInfo.manageDeptList)) {
        root = this.userInfo.manageDeptList;
      } else {
        //Else Empty block statement
      }
      if (root && !Array.isArray(root)) {
        root = [root];
      }
      if (root) {
        this.selectOpts.rootNode = root;
      }
    }
    this.initViews();
  }

  current: number = 3;

  @Watch('formula', {
    immediate: true,
  })
  onFormulaChange(formula: string) {
    if (
      [DataItemType.StaffSingle, DataItemType.DeptSingle].includes(
        this.field.dataType,
      )
    ) {
      if (['In', 'NotIn'].includes(formula)) {
        this.selectOpts.mulpitle = true;
      } else {
        this.selectOpts.mulpitle = false;
      }
    }
  }

  @Watch('value', {
    immediate: true,
  })
  onValueChange() {
    if (Array.isArray(this.value)) {
      this.selectValue = this.value.map((x) => ({
        name: x.label,
        id: x.value,
        type: x.type,
        selectType: x.selectType,
      }));
    } else {
      this.selectValue = [];
    }
  }

  onChange(values: any[]) {
    const vals = values.map((x) => ({
      label: x.name,
      value: x.id,
      type: x.type,
    }));
    this.$emit('input', vals);
  }

  currentChange(e: any) {
    // const val = e.target.value;
    const val = e;
    let result: any = {};
    switch (val) {
      case 1:
        result = {
          label: '本人',
          value: 'SELF_P',
        };
        this.$emit('input', result);
        break;
      case 2:
        result = {
          label: '本部门',
          value: 'SELF_D',
        };
        this.$emit('input', result);
        break;
      case 3:
        // 切换自定义清除value
        this.$emit('input', '');
        break;
      default:
        break;
    }
  }

  initViews() {
    // 回显本人本部门自定义
    let result: number = 3;
    if (this.value && this.value.length === 1) {
      this.value.forEach((item: any) => {
        if (item.value === 'SELF_P') {
          result = 1;
        } else if (item.value === 'SELF_D') {
          result = 2;
        } else {
          result = 3;
        }
      });
    }
    this.current = result;
  }
}
</script>

<style lang="less" scoped>
.report-custom-selector-wrap {
  display: flex;
  flex-wrap: wrap;
  align-items: center;
  margin-bottom: -12px;
  .custom-radio {
    margin-right: 8px;
    margin-bottom: 12px;
    /deep/.ant-select-selection {
      width: 90px;
      height: 32px;
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #d4d5d6;
      .ant-select-selection__rendered {
        line-height: 30px;
      }
    }
  }
  .customer-pannel {
    flex: 1;
    min-width: 140px;
    margin-bottom: 12px;
  }
}
</style>
<style lang="less">
.custom-radio-item-box {
  .ant-select-dropdown-menu-item-selected,
  .ant-select-dropdown-menu-item-active:not(
      .ant-select-dropdown-menu-item-disabled
    ) {
    background: rgba(0, 30, 116, 0.06);
  }
}

.h3-global-filter-main-value,
.h3-dashboard-chart-filter-value,
.h3-dashboard-element-wrap.report-type-filterPicker {
  .report-custom-selector-wrap {
    flex-flow: row;
    .custom-radio {
      margin-right: 8px;
      margin-bottom: 12px;
      width: unset;
      .ant-select-selection {
        width: 90px;
      }
      &.dynamics-select {
        flex: 1;
        margin-right: unset;
        .ant-select-selection {
          width: 100%;
        }
      }
    }
    .customer-pannel {
      width: unset;
      flex: 1;
      margin-bottom: 12px;
    }
  }
}
</style>
