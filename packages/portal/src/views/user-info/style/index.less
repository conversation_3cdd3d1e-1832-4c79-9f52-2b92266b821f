/*
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
*/
@import '~cloudpivot/common/common.less';
  @keyframes loading {
    100% {transform: rotate(360deg);}
  }
  .workitem-box{
    height: 100%;
    position: relative;
    .loading-box {
      display: flex;
      align-items: center;
      justify-content: center;
      position: absolute;
      width: 100%;
      height: calc( 100% - 52px );
      background:rgba(255,255,255,0.85);
      z-index: 100;
      .loading {
        text-align: center;
      }
      img {
        animation: loading 1s linear infinite;
      }
      p {
        margin-top: @base4-padding-lg;
        color: @light-color-3;
      }
    }
    .content-top {
      height: 34px;
      display: flex;
      margin-bottom: @base4-padding-md;
      justify-content: space-between;
      align-items: center;
      .content-right {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      h2 {
        color: @light-color-1;
        font-size: @font-size-16;
        font-weight: @font-weight-md;
        padding-left: @base4-padding-md;
      }
      .search-input {
        margin-left: @base4-padding-xs;
      }
      .suffix-icon {
        cursor: pointer;
      }
      .close-icon {
        margin-right: @base4-padding-xs;
        color: @light-color-3;
      }
      .search-icon {
        color: @light-color-3;
      }
    }

    .table-box {
      height: calc( 100% - 52px );
      box-shadow: 0px 2px 8px 0px rgba(30,85,255,0.1);
      position: relative;
    .table {
      height: 100%;
      padding: 0 @base4-padding-md;
      // max-height: calc( 100% - 50px);
      // overflow: auto;
    }
    /deep/.ant-table-empty .ant-table-body {
      overflow: hidden!important;
    }
    background-color: @white-background;
    padding: @base4-padding-md 0;
    border-radius: @border-radius-lg;
    /deep/.ant-table-thead > tr > th {
      color: @light-color-3;
      background-color: @main-background;
    }
    /deep/.ant-table-tbody > tr > td {
      color: @light-color-1;
    }
    /deep/.ant-table-thead > tr > th, /deep/.ant-table-tbody > tr > td {
      padding: @base4-padding-xs @base4-padding-md;
    }
    /deep/.ant-table-placeholder {
      display: none;
    }
    /deep/.ant-pagination-item-active{
      background: @primary-color;
      a {
        color: white;
      }
    }
    .no-data {
      position: absolute;
      top: 100px;
      left: 50%;
      margin-left: -145px;
      text-align: center;
      p {
        font-size: @font-size-14;
        color: @light-color-3;
      }
    }
    .gray {
      color: @light-color-3;
    }
    .fake-btn {
      cursor: pointer;
      transition: all ease .3s;
      &:hover {
        color: @primary-color;
      }
    }
    .load-all {
      text-align: center;
      color: @light-color-3;
      margin-top: @base10-padding-sm * 2;
      // 先注销
      display: none!important;
    }
    .overtime {
      display: inline-block;
      width: 46px;
      height: 18px;
      background-color: @error-color;
      color: @dark-color-2;
      border-radius: 9px;
      font-size: @font-size-10;
      text-align: center;
      margin-left: @base4-padding-md;
    }
    .overtime-icon {
      margin-left: @base4-padding-xs;
      margin-top: -2px;
    }
    .pagination-box {
      margin-top: @base4-padding-xs;
      text-align: right;
      padding: @base4-padding-xs @base4-padding-md;
      border-top: 1px solid @base-border-color;
      // position: absolute;
      // width: 100%;
      // right: 0;
      // bottom: 0;
    }
    /deep/.ant-select-dropdown-menu-item {
      text-align: center;
    }
  }
  .reload-btn {
    margin-top: @base4-padding-md;
  }
  .text-ellipsis {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    /* autoprefixer: ignore next */
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  // 改变列宽指令
  .resize {
    display: inline-block;
    height: 22px;
    width: calc(100% + 32px);
    padding: 0 16px;
    -webkit-transform: translateX(-16px);
    transform: translateX(-16px);
    border-left: 1px solid rgba(0,0,0,0.15);
    // border-left: 1px solid #e8e8e8;
  }

  .resize-first {
    border: none;
  }

  .load-fail-box {
    padding-top: 100px;
    position: absolute;
    top: 100px;
    left: 50%;
    margin-left: -145px;
    text-align: center;
  }
 }
