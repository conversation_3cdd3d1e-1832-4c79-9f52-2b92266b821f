<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<!--
 * @Author: <PERSON>
 * @Date: 2022-04-06 16:14:11
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-04-13 15:56:49
 * @FilePath: \yunshu6.0\packages\portal\src\views\single-app\index.vue
 * @Description: 
-->
<template>
  <div class="single-app">
    <app-header />
    <div class="single-app__main">
      <div class="single-app__main-left">
        <AppAside />
        <div id="jDragLine" class="line"></div>
      </div>
      <div class="single-app__main-right app-list">
        <router-view />
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { OAuthApi } from 'cloudpivot/api';
import { Component, Vue } from 'vue-property-decorator';
import AppHeader from '../../components/single-app/app-header.vue';
import AppAside from '../../components/single-app/app-aside.vue';
import { namespace } from 'vuex-class';
const SystemModule = namespace('System/System');

@Component({
  name: 'single-app',
  components: {
    AppHeader,
    AppAside,
  },
})
export default class SingleApp extends Vue {
  @SystemModule.Mutation('setIsAdmin') setIsAdmin: any;

  @SystemModule.Mutation('setAdmin') setAdmin: any;

  @SystemModule.Mutation('setIsPrivilegedPerson') setIsPrivilegedPerson: any;

  @SystemModule.Mutation('setIsAppAdmin') setIsAppAdmin: any;

  @SystemModule.Mutation('setRootAdmin') setRootAdmin: any;

  @SystemModule.Mutation('setUserInfo') setUserInfo: any;

  created() {
    OAuthApi.getUserInfo().then((res: any) => {
      if (res.errcode === 0) {
        const info = res.data;
        sessionStorage.setItem('user', JSON.stringify(res.data));
        localStorage.setItem(
          'userId',
          JSON.parse(sessionStorage.getItem('user') || '').id,
        ); //用户ID
        // 判断当前用户角色
        const isAppAdmin: boolean = info.permissions.includes('APP_MNG');
        const isSysAdmin: boolean = info.permissions.includes('SYS_MNG');
        const isRootAdmin: boolean = info.permissions.includes('ADMIN');
        const isAdmin: boolean = isAppAdmin || isSysAdmin || isRootAdmin;
        this.setIsAdmin(isAdmin);
        this.setRootAdmin(isRootAdmin);
        this.setAdmin(isSysAdmin || isRootAdmin);
        this.setUserInfo(info);
        // 设置是否特权人
        const isWORKFLOW_ADMIN: boolean =
          info.permissions.includes('WORKFLOW_ADMIN');
        this.setIsPrivilegedPerson(isWORKFLOW_ADMIN);
        this.setIsAppAdmin(isAppAdmin);

        if (info.lastLoginTime) {
          const login_jump = localStorage.getItem('login_jump');
          if (!login_jump || login_jump === 'false') {
            localStorage.setItem('login_jump', 'true');
            const vm = this;
            this.$message.info({
              content: `${this.$t(
                'languages.common.lastLoginTime',
              ).toString()}${info.lastLoginTime}`,
              icon: () => {
                return vm.$createElement('span', {
                  class: 'aufontAll h-icon-all-clock-circle',
                  style: {
                    color: 'rgba(17,18,24,0.25)',
                    'margin-right': '8px',
                  },
                });
              },
            });
          }
        }
      }
    });

    const { appCode } = this.$route.params;
    window.Environment.appCode = appCode;
  }
}
</script>
<style lang="less" scoped>
@import '../../styles/themes/default.less';
.single-app {
  background: @main-background;
  .single-app__main {
    flex: 1;
    position: relative;
    overflow: hidden;
    display: flex;
    .single-app__main-left {
      position: relative;
      z-index: 2;
      & .line {
        width: 8px;
        height: 100%;
        background: transparent;
        cursor: ew-resize;
        position: absolute;
        right: 0;
        top: 0;
        z-index: 3;
      }
    }
    .single-app__main-right {
      flex: 1;
      position: relative;
    }
  }
  /deep/.app-header .ant-menu-item > a > span {
    height: unset;
    vertical-align: middle !important;
  }
}
</style>
