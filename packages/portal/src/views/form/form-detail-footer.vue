<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <div class="footer footerStyle">
    <div>
      <div class="footer-left">
        <slot name="isSaveData"></slot>
      </div>

      <div class="footer-right">
        <slot></slot>
        <slot name="upAndCreate"></slot>
        <!-- 提交并继续 -->
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';

@Component({
  name: 'form-detail-footer',
  components: {},
})
export default class FormDetailFppter extends Vue {}
</script>

<style lang="less" scoped>
@import '~@/styles/themes/default.less';

.footer {
  padding: 0 16px !important;
  position: fixed;
  z-index: 999;
  left: 0;
  right: 0;
  bottom: 0;
  height: 52px;
  display: flex;
  align-items: center;
  background-color: #fff;
  border-top: 1px solid rgba(232, 232, 232, 1);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  &-left {
    display: flex;
    align-items: center;
    h4 {
      font-size: 16px;
      color: #000;
      margin-bottom: 0;
    }
  }

  /deep/ .ant-btn {
    padding: 0 16px;
  }

  .footer-right {
    display: flex;
    & > div {
      margin-left: 8px;
      color: rgba(0, 0, 0, 0.65);
      i {
        margin-right: 8px;
      }
      &:hover {
        span {
          color: #2970ff;
        }
        cursor: pointer;
      }
      &.icon-close {
        margin-right: -16px;
        font-size: 16px;
        color: #d8d8d8;
      }
    }
  }
  &-dropdown {
    display: flex;
    align-items: center;
    margin-left: 16px;
    padding-left: 16px;
    height: 32px;
    border-left: 1px solid rgba(217, 217, 217, 1);
    .ant-dropdown-trigger {
      & > span {
        display: inline-block;
        max-width: 140px;
        // width: 140px;
      }
    }
  }
  .qrcode {
    // margin-left: 33px;
    // margin-right: 16px;
    position: relative;
    & > img {
      width: 26px;
      cursor: url('~@/assets/images/enlarge-o.png'), pointer;
      margin: 2px;
    }
    .qrcode-enlarge {
      position: absolute;
      top: 28px;
      border: 1px solid rgba(221, 221, 221, 1);
      background: #fff;
      img {
        width: 250px;
        height: 250px;
        // max-height: 250px !important;
      }
      p {
        text-align: center;
        padding-bottom: 16px;
      }
      // left: 0;
      right: -1px;
    }
  }
  img.logo {
    cursor: pointer;
    max-height: 30px !important;
  }

  & > div:first-child {
    // border-right: 1px solid rgba(217, 217, 217, 1);
    flex-grow: 1;
    display: flex;
    justify-content: space-between;

    a.aback {
      font-size: 18px;
      margin-right: 8px;

      &::after {
        content: '';
        height: 18px;
        width: 1px;
        background-color: #d8d8d8;
        display: inline-block;
        position: relative;
        top: 3px;
        margin-left: 8px;
      }
    }
  }
}
</style>

<style lang="less">
.node-switch-menu-item {
  width: 180px;
  &.a-menu-item-active {
    background: rgba(240, 247, 255, 1);
    font-weight: 600;
  }
}
</style>
