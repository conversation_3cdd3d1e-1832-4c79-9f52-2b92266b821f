<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <customPop
    v-model="showNoParticipantsList"
    :titleStyle="titleStyle"
    width="1200px"
    height="auto"
    :zIndex="105"
    @OK="showNoParticipantsList = false"
  >
    <template slot="title">
      {{ $t('languages.common.ProcessOperationAndMaintenance.NoParticipant') }}
    </template>
    <template slot="content">
      <a-table
        :columns="columns"
        size="small"
        :pagination="false"
        :loading="false"
        :locale="{ emptyText: '' }"
        :scroll="{ y: 585 }"
        :dataSource="noParticipantsList"
        class="no-participants-table"
      >
        <template slot="state" slot-scope="text, record">
          {{ status[record.state] }}
        </template>
      </a-table>
    </template>

    <template slot="footer">
      <div class="pagination-wrapper">
        <a-pagination
          size="small"
          :pageSizeOptions="pageSizeOptions"
          :total="total"
          :showTotal="
            (total) => $t('languages.common.form.Total', { num: total })
          "
          showSizeChanger
          showQuickJumper
          :defaultPageSize="10"
          :current="page"
          @change="pageChange"
          @showSizeChange="showSizeChange"
        />
      </div>
    </template>
  </customPop>
</template>

<script lang="ts">
import '@/config/h3-form';
import { Component, Vue, Watch, Prop } from 'vue-property-decorator';
import { workflowApi } from 'cloudpivot/api';
import customPop from 'cloudpivot-form/form/src/common/components/customPop.vue';
import { Table, Pagination } from '@h3/antd-vue';
@Component({
  name: 'no-participant-table',
  components: {
    customPop,
    ATable: Table,
    APagination: Pagination,
  },
})
export default class NoParticipantTable extends Vue {
  @Prop()
  currentNode!: any;

  @Prop()
  data!: any;

  @Prop()
  total!: any;

  showNoParticipantsList: boolean = false; // 显示无参与者列表

  page: number = 0;

  titleStyle: any = {
    'border-bottom': '1px solid #eee',
    'margin-bottom': '24px',
    'font-weight': '600',
  };

  columns: any[] = [
    {
      title: '序列',
      key: 'index',
      dataIndex: 'index',
      width: 64,
      align: 'center',
      ellipsis: true,
    },
    {
      title: '流程名称',
      key: 'instanceName',
      dataIndex: 'instanceName',
      width: 226,
      ellipsis: true,
    },
    {
      title: '流程模板',
      key: 'workflowName',
      dataIndex: 'workflowName',
      width: 116,
      ellipsis: true,
    },
    {
      title: '流程版本号',
      key: 'workflowVersion',
      dataIndex: 'workflowVersion',
      width: 116,
      ellipsis: true,
    },
    {
      title: '当前节点',
      key: 'activityName',
      dataIndex: 'activityName',
      width: 227,
      ellipsis: true,
    },
    {
      title: '当前处理人',
      key: 'originatorName',
      dataIndex: 'originatorName',
      ellipsis: true,
    },
    {
      title: '流程状态',
      key: 'state',
      dataIndex: 'state',
      ellipsis: true,
      scopedSlots: { customRender: 'state' },
    },
  ];

  noParticipantsList: any[] = []; // 无参与者列表

  pageSizeOptions: Array<string> = ['10', '20', '50', '100'];

  status: any = {
    DRAFT: '草稿',
    PROCESSING: '进行中',
    APPROVED: '流程通过',
    TOPPING_OFF: '手动结束',
    CANCELED: '已作废',
    EXCEPTION: '流程异常',
  };

  @Watch('showNoParticipantsList')
  onShowNoParticipantsList(val) {
    if (val) {
      this.getListInstances(0, 10);
    }
  }

  showSizeChange(current: number, size: number) {
    this.page = 0;
    this.getListInstances(0, size);
  }

  // 分页 page change 回调
  pageChange(page: number, pageSize: number) {
    this.getListInstances(page - 1, pageSize);
  }

  async getListInstances(page: number, size: number) {
    if (!this.currentNode.activityCode) {
      return;
    }
    await workflowApi
      .getListInstances({
        instanceState: 'PROCESSING',
        workflowCode: this.data.workflowCode,
        workflowVersion: this.data.workflowVersion,
        activityCode: this.currentNode.activityCode,
        workItemSource: 1,
        filterWorkflowInstanceId: this.$route.params.workflowInstanceId,
        page: page,
        size: size,
        queryType: 'list',
      })
      .then((res: any) => {
        if (res.errcode === 0) {
          res.data.content.forEach((element, index) => {
            element.index = index + 1;
          });
          this.noParticipantsList = res.data.content;
        } else {
          this.$message.error(res.errmsg);
        }
      });
  }
}
</script>

<style></style>
