<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <!-- 流程运维弹窗页面 -->
  <div>
    <customPop
      v-model="modalShow"
      :titleStyle="{ 'font-weight': '600' }"
      :top="top"
      :height="popHeigth"
      width="708px"
      :zIndex="100"
      :okText="$t('languages.common.ok')"
      :cancelText="$t('languages.common.cancel')"
      @OK="popOK"
    >
      <template slot="title">
        {{ $t('languages.common.ProcessOperationAndMaintenance.title') }}
      </template>
      <template slot="content">
        <div id="current-node" v-if="participants.length" class="current-node">
          <div class="left">
            <div class="list">
              <label>{{ $t('languages.common.form.curActivity') }}</label>
              <div class="custom-select">
                <p v-if="participants.length && participants.length === 1">
                  {{ currentNode.activityName }}
                </p>
                <a-dropdown v-else>
                  <p>{{ currentNode.activityName }} <a-icon type="down" /></p>
                  <a-menu slot="overlay">
                    <a-menu-item
                      v-for="item in participants"
                      :key="item.activityCode"
                      @click="changeCurrentNode(item)"
                    >
                      {{ item.activityName }}
                    </a-menu-item>
                  </a-menu>
                </a-dropdown>
              </div>
            </div>
            <div class="list" style="flex: 1">
              <label>{{ $t('languages.common.form.resolver') }}</label>
              <div
                class="handler"
                :style="{ 'max-height': showAll ? 'auto' : undefined }"
              >
                <user-popover-ellipsis
                  :popoverClass="'display-popover-customize'"
                  :participantRelations="currentNode.participants"
                  popWidth="660px"
                  baseParent="#current-node"
                />

                <!-- {{currentNode && currentNode.participants && currentNode.participants.map(el => el.name).join('、') || ''}} -->
              </div>
              <!-- <template v-if="currentNode && currentNode.participants && currentNode.participants.map(el => el.name).join('、').length > 76">
                <span @click="showAll = false" v-if="showAll" class="icon-up icon aufontAll h-icon-all-double-up-o"></span>
                <span @click="showAll = true" v-else class="icon-up icon aufontAll h-icon-all-double-down-o"></span>
              </template> -->
            </div>
          </div>
          <div v-if="!isWORKFLOW_ADMIN" class="right">
            <a-popover placement="bottom" trigger="click">
              <a-button style="color: #111218">
                {{ $t('languages.common.form.WorkHandover') }}
              </a-button>

              <template slot="content">
                <div class="to-work-handover" @click="toWorkHandover('dep')">
                  <p>
                    {{ $t('languages.common.form.organization')
                    }}<i class="icon aufontAll h-icon-all-finder-o"></i>
                  </p>
                  <small style="margin-bottom: 20px">
                    {{ $t('languages.common.form.organizationTips1') }}
                  </small>
                </div>
                <div class="to-work-handover" @click="toWorkHandover('role')">
                  <p>
                    {{ $t('languages.common.form.organizationRole')
                    }}<i class="icon aufontAll h-icon-all-finder-o"></i>
                  </p>
                  <small>{{
                    $t('languages.common.form.organizationTips2')
                  }}</small>
                </div>
              </template>
            </a-popover>
          </div>
        </div>

        <!-- 选择流程操作 -->
        <div class="workflow-operation">
          <label>{{ $t('languages.common.form.SelectAction') }}</label>
          <div class="operations">
            <a-radio-group v-model="manage" @change="onManageChange">
              <div class="radio-wrapper">
                <a-radio
                  v-if="isShow('CANCELED_WORKFLOW')"
                  value="CANCELED_WORKFLOW"
                  :title="$t('languages.common.WorkflowActions.Nullify')"
                >
                  {{ $t('languages.common.WorkflowActions.Nullify') }}
                </a-radio>
                <a-radio
                  v-if="isShow('DELETE_WORKFLOW')"
                  value="DELETE_WORKFLOW"
                  :title="$t('languages.common.WorkflowActions.Delete')"
                >
                  {{ $t('languages.common.WorkflowActions.Delete') }}
                </a-radio>
                <a-radio
                  v-if="isShow('ACTIVATE_ACTIVITY')"
                  value="ACTIVATE_ACTIVITY"
                  :title="$t('languages.common.WorkflowActions.ActivateNode')"
                >
                  {{ $t('languages.common.WorkflowActions.ActivateNode') }}
                </a-radio>
                <a-radio
                  v-if="isShow('ADJUST_PARTICIPANT')"
                  value="ADJUST_PARTICIPANT"
                  :title="$t('languages.common.WorkflowActions.AdjustHandler')"
                >
                  {{ $t('languages.common.WorkflowActions.AdjustHandler') }}
                </a-radio>
                <a-radio
                  v-if="isShow('COMPLETED_WORKFLOW')"
                  value="COMPLETED_WORKFLOW"
                  :title="$t('languages.common.WorkflowActions.FinishInstance')"
                >
                  {{ $t('languages.common.WorkflowActions.FinishInstance') }}
                </a-radio>
                <a-radio
                  v-if="isShow('MODIFY_OWNER')"
                  value="MODIFY_OWNER"
                  :title="$t('languages.common.WorkflowActions.EditOwner')"
                >
                  {{ $t('languages.common.WorkflowActions.EditOwner') }}
                </a-radio>
                <a-radio
                  v-if="!isWORKFLOW_ADMIN"
                  value="xiugai"
                  :title="$t('languages.common.WorkflowActions.EditTemplate')"
                >
                  {{ $t('languages.common.WorkflowActions.EditTemplate') }}
                </a-radio>
                <a-radio
                  v-if="isShow('CANCELED_ACTIVITY_WORKITEM')"
                  value="CANCELED_ACTIVITY_WORKITEM"
                  :title="$t('languages.common.WorkflowActions.CancelAllTask')"
                >
                  {{ $t('languages.common.WorkflowActions.CancelAllTask') }}
                </a-radio>
              </div>
            </a-radio-group>
          </div>
        </div>

        <template v-if="manage">
          <!-- <template> -->

          <div v-if="manage === 'CANCELED_ACTIVITY_WORKITEM'" class="row-item">
            <!-- <label></label> -->
            <div>
              <p style="margin-bottom: 0" class="tips">
                <span
                  class="icon aufontAll h-icon-all-Components_Warning"
                ></span>{{ $t('languages.common.WorkflowActions.CancelTips1') }}
              </p>
            </div>
          </div>

          <div v-if="manage === 'ADJUST_PARTICIPANT'" class="row-item">
            <label class="label-required">{{
              $t('languages.common.WorkflowActions.SelectHandler')
            }}</label>
            <div>
              <staff-selector
                v-model="userValue"
                :options="options"
                displayType="plus"
                :params="{ sourceType: 'portal', recursive: true }"
              />
              <!-- <Selector v-model="userValue" :options="options"></Selector> -->
            </div>
          </div>

          <!-- 激活其他节点 -->
          <div v-if="manage === 'ACTIVATE_ACTIVITY'" class="row-item">
            <label class="label-required">{{
              $t('languages.common.WorkflowActions.SelectNode')
            }}</label>
            <div class="select-node">
              <a-select
                style="width: 100%"
                :placeholder="
                  $t('languages.common.WorkflowActions.pleaseSelect')
                "
                :showSearch="true"
                :optionFilterProp="'title'"
                @change="onActivityChange"
              >
                <a-select-option
                  v-for="(opt, index) in nodes"
                  :key="index"
                  :value="opt.activityCode"
                  :title="opt.activityName + '[' + opt.activityCode + ']'"
                  :disabled="opt.status === 1"
                >
                  {{ opt.activityName + '[' + opt.activityCode + ']' }}
                </a-select-option>
              </a-select>
            </div>
            <label class="label-required">
              <span>
                {{ $t('languages.common.WorkflowActions.nodeParticipants') }}
              </span>
            </label>
            <div>
              <staff-selector
                v-model="activityParticipants"
                :options="options"
                displayType="plus"
                :params="{ sourceType: 'portal', recursive: true }"
              />
              <!-- <Selector v-model="userValue" :options="options"></Selector> -->
            </div>
          </div>

          <div v-if="manage === 'xiugai'" class="row-item">
            <label>{{
              $t('languages.common.WorkflowActions.EditTemplate')
            }}</label>
            <div class="to-template">
              <span
                class="to-modify-workflow-template"
                @click="toModifyWorkflowTemplate"
                >{{
                  isChinese
                    ? data.workflowName
                    : (data.name_i18n && data.name_i18n.en) || data.workflowName
                }}</span>
              <span
                class="icon aufontAll h-icon-all-finder-o"
                @click="toModifyWorkflowTemplate"
              ></span>
            </div>
          </div>

          <template v-if="manage === 'MODIFY_OWNER'">
            <div class="row-item">
              <label class="label-required">{{
                $t('languages.common.WorkflowActions.chooseOwner')
              }}</label>
              <div>
                <staff-selector
                  v-model="owner"
                  :options="taffSelectorOpts"
                  displayType="plus"
                  @change="onValueChange"
                />
              </div>
            </div>
            <div class="row-item">
              <label>{{ $t('languages.common.form.ownerDepartment') }}</label>
              <div>
                <a-select
                  v-model="departments"
                  style="width: 100%"
                  :placeholder="
                    $t('languages.common.WorkflowActions.pleaseSelect')
                  "
                  :disabled="!owner.length"
                >
                  <a-select-option
                    v-for="(opt, index) in departmentsList"
                    :key="index"
                    :value="opt.deptId"
                    :title="opt.deptName"
                  >
                    {{ opt.deptName }}
                  </a-select-option>
                </a-select>
              </div>
            </div>
          </template>

          <div
            v-if="!['xiugai', 'DELETE_WORKFLOW'].includes(manage)"
            class="row-item"
          >
            <label>{{
              $t('cloudpivot.flow.pc.approvalLog.handlingSuggestion')
            }}</label>
            <div class="comment-input">
              <a-textarea
                v-model="comment"
                :placeholder="$t('languages.common.placeholder')"
              />
              <span class="str-len">{{ comment.length }}/200</span>
              <comment ref="comment" />
            </div>
          </div>

          <template
            v-if="
              currentNode.workItemSource === 1 &&
              total &&
              ![
                'xiugai',
                'CANCELED_ACTIVITY_WORKITEM',
                'ACTIVATE_ACTIVITY',
              ].includes(manage)
            "
          >
            <div
              class="row-item"
              style="border-top: 1px solid #eee; padding-top: 21px"
            >
              <div class="multiplexing">
                <a-checkbox v-model="isMultiplexing" />
                <span>{{
                  $t(
                    'languages.common.ProcessOperationAndMaintenance.NoParticipantTips1',
                  )
                }}</span>

                <a-popover placement="top" overlayClassName="black-tooltip">
                  <template slot="content">
                    <p>
                      1.{{
                        $t(
                          'languages.common.ProcessOperationAndMaintenance.NoParticipantTips2',
                        )
                      }}
                    </p>
                    <p>
                      2.{{
                        $t(
                          'languages.common.ProcessOperationAndMaintenance.NoParticipantTips3',
                        )
                      }}
                    </p>
                  </template>
                  <i
                    style="font-size: 12px; color: #2970ff; margin-left: 5px"
                    class="icon aufontAll h-icon-all-question-circle-o"
                  ></i>
                </a-popover>
              </div>
            </div>

            <div class="row-item">
              <label></label>
              <div>
                <span class="show-other" @click="showNoParticipantsList">{{
                  $t(
                    'languages.common.ProcessOperationAndMaintenance.NoParticipantTips4',
                    {
                      name: isChinese
                        ? data.workflowName
                        : (data.name_i18n && data.name_i18n.en) ||
                          data.workflowName,
                      num: total,
                    },
                  )
                }}</span>
              </div>
            </div>
          </template>
        </template>
      </template>
    </customPop>

    <NoParticipantTable
      ref="noParticipantTable"
      :currentNode="currentNode"
      :data="data"
      :total="total"
    />
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue, Watch } from 'vue-property-decorator';
import customPop from 'cloudpivot-form/form/src/common/components/customPop.vue';
import {
  Select,
  Radio,
  Input,
  Checkbox,
  Popover,
  Button,
  Dropdown,
  Icon,
  Menu,
  Tooltip,
} from '@h3/antd-vue';
import comment from 'cloudpivot-flow/flow-center/src/components/pc/components/comment.vue';
import formPc from 'cloudpivot-form/form/src/renderer/components/pc';
import { workflowApi } from 'cloudpivot/api';
import StaffSelector from 'cloudpivot-form/form/src/common/components/form-staff-selector/pc/staff-selector.vue';
import { userApi } from 'cloudpivot/api';
import common from 'cloudpivot/common/pc';
import { workflowEnum } from 'cloudpivot-flow/flow';
import NoParticipantTable from './noParticipantTable.vue';

@Component({
  name: 'batchOperationAndMaintenance',
  components: {
    customPop,
    ASelect: Select,
    ASelectOption: Select.Option,
    ARadio: Radio,
    ARadioGroup: Radio.Group,
    ATextarea: Input.TextArea,
    comment: comment,
    Selector: formPc.StaffSelector,
    StaffSelector,
    ACheckbox: Checkbox,
    APopover: Popover,
    AButton: Button,
    ADropdown: Dropdown,
    AIcon: Icon,
    AMenu: Menu,
    AMenuItem: Menu.Item,
    UserPopoverEllipsis: common.components.UserPopoverEllipsis,
    NoParticipantTable,
    ATooltip: Tooltip,
  },
})
export default class batchOperationAndMaintenance extends Vue {
  @Prop({ default: false }) value!: boolean;

  @Prop({
    default: () => {
      return {};
    },
  })
  data!: any;

  @Prop() isWORKFLOW_ADMIN!: boolean;

  @Prop() workflowAdminManageScopes!: string[];

  @Prop() workflowState?: string;

  @Prop({ default: () => {} }) headerAction!: any; // 头部按键权限

  showAll: boolean = false;

  top: any = '8%';

  popHeigth: any = '500px';

  get isChinese() {
    return !(this.$i18n.locale && this.$i18n.locale === 'en');
  }

  isShow(type) {
    let show = true;

    switch (type) {
      case 'COMPLETED_WORKFLOW': // 结束流程
        show =
          !['CANCELED'].includes(this.workflowState as string) &&
          !this.completed &&
          this.headerAction.showAdjust; // 只能结束未完成的流程
        break;
      case 'ADJUST_PARTICIPANT': // 调整当前处理人
      case 'CANCELED_ACTIVITY_WORKITEM': // 取消当前节点所有任务
        show =
          !this.completed &&
          this.showAdjustAndCancelWorkitem &&
          this.headerAction.showAdjust;
        break;
      case 'CANCELED_WORKFLOW': // 作废流程
        show =
          this.headerAction.showCancel && this.workflowState !== 'CANCELED';
        break;
      case 'DELETE_WORKFLOW': // 删除流程
        show = this.headerAction.showRemove;
        break;
      case 'MODIFY_OWNER': // 修改拥有者
        show = this.headerAction.showEditOwner;
        break;

      default:
        break;
    }

    return (
      (!this.isWORKFLOW_ADMIN ||
        (this.isWORKFLOW_ADMIN &&
          this.workflowAdminManageScopes.includes(type))) &&
      show
    );
  }

  @Watch('comment')
  onCommentChange(val: string) {
    if (val.length > 200) {
      this.comment = this.comment.slice(0, 200);
    }
  }

  activationActivityCode: any = undefined;

  async created() {
    const res: any = await workflowApi.getWorkflowNodes({
      workflowInstanceId: this.$route.params.workflowInstanceId,
    });
    this.nodes = res.data;
  }

  get showAdjustAndCancelWorkitem() {
    const unfinishNodesLength = this.nodes.filter(
      (i: { status: any }) => i.status,
    ).length;
    if (unfinishNodesLength <= 0) {
      return false;
    }
    if (
      this.nodes.every(
        (item: any) => item.activityCode !== this.currentNode.activityCode,
      )
    ) {
      return false;
    }
    return true;
  }

  tipsTitle: string = '';

  tipsContent: string = '';

  isMultiplexing: boolean = false;

  toWorkHandover(type) {
    if (type === 'role') {
      this.winOpen('/admin/#/organization/orgrole', '_blank');
    } else {
      this.winOpen('/admin/#/organization/orguser', '_blank');
    }
  }

  winOpen(url, type) {
    let win: any = window;
    if (window.top !== window.self) {
      win = window.parent;
    }
    win.open(url, type);
  }

  @Watch('data')
  onDataChange(value) {
    this.participants = value.participants;
    this.currentNode = value.participants[0] || {};
    this.getListInstances(0, 1);
  }

  total: any = 0; // 无参与者总数

  changeCurrentNode(item) {
    this.currentNode = item;
    this.getListInstances(0, 1);
  }

  async getListInstances(page: number, size: number) {
    if (!this.currentNode.activityCode) {
      return;
    }
    await workflowApi
      .getListInstances({
        instanceState: 'PROCESSING',
        workflowCode: this.data.workflowCode,
        workflowVersion: this.data.workflowVersion,
        activityCode: this.currentNode.activityCode,
        workItemSource: 1,
        filterWorkflowInstanceId: this.$route.params.workflowInstanceId,
        page: page,
        size: size,
        queryType: 'total',
      })
      .then((res: any) => {
        if (res.errcode === 0) {
          this.total = res.data.totalElements;
        } else {
          this.$message.error(res.errmsg);
        }
      });
  }

  currentNode: any = {};

  participants: any[] = [];

  comment: string = ''; // 审批意见

  manage: string = ''; // 选择的流程操作

  userValue: any = []; // 当前处理人

  activityParticipants: any[] = [];

  nodeValue: any = []; // 激活的节点

  selectOptions: any[] = []; // 下拉配置项

  nodes: any[] = []; // 所有节点信息

  owner: Array<any> = []; // 拥有者

  departments: any = ''; // 拥有者部门

  options: any = {
    key: 'test',
    selectOrg: false,
    selectUser: true,
    mulpitle: true,
    showModel: true,
    showSelect: true,
    placeholder: '点击选择',
  };

  taffSelectorOpts: any = {
    selectOrg: false,
    selectUser: true,
    showModel: true,
    mulpitle: false,
    showSelect: true,
    placeholder: '请选择',
    title: '选择拥有者',
  };

  departmentsList: any[] = [];

  onValueChange(value: any[]) {
    if (value.length > 0) {
      this.getUserDepartments(value[0].id);
    } else {
      this.departmentsList = [];
      this.departments = '';
    }
  }

  async getUserDepartments(userId: string) {
    const res: any = await userApi.getUserDepartments(userId);
    if (res && res.errcode === 0) {
      this.departmentsList = res.data;
      const resoure = res.data.find((d: any) => d.isMain);
      this.departments = resoure.deptId;
    } else {
      this.$message.error(res.errmsg);
    }
  }

  onManageChange() {
    if (this.manage === 'ACTIVATE_ACTIVITY') {
    }
  }

  toModifyWorkflowTemplate() {
    const params = this.data;
    // 去修改流程模板
    this.winOpen(
      `/admin/#/apps/model/${params.appCode}/${params.schemaCode}/workflowDesign/${params.workflowCode}`,
      '_blank',
    );
  }

  // 确定批量运维
  popOK() {
    if (this.manage === '') {
      this.modalShow = false;
      return;
    }

    if (this.manage === 'ACTIVATE_ACTIVITY') {
      if (!this.activationActivityCode) {
        this.$message.warning(
          this.$t(
            'languages.common.ProcessOperationAndMaintenance.ActivityTips1',
          ) as string,
        );
        return;
      }

      if (this.activityParticipants.length === 0) {
        this.$message.warning('请设置节点参与者');
        return;
      }

      const node = this.nodes.find(
        (el) => el.activityCode === this.activationActivityCode,
      );
      // 激活其他节点
      this.tipsTitle = this.$t(
        'languages.common.ProcessOperationAndMaintenance.ActivityTips2',
        {
          node: this.isChinese
            ? node.activityName
            : (node.name_i18n && node.name_i18n.en) || node.activityName,
        },
      ) as string;
      this.tipsContent = ``;
      this.openTipsModal();
    } else if (this.manage === 'CANCELED_WORKFLOW') {
      // 作废流程
      this.tipsTitle = this.$t('languages.common.form.NullifyTips1') as string;
      this.tipsContent = this.$t(
        'languages.common.form.NullifyTips2',
      ) as string;
      this.openTipsModal();
    } else if (this.manage === 'ADJUST_PARTICIPANT') {
      if (!this.userValue.length) {
        this.$message.error(
          this.$t(
            'languages.common.ProcessOperationAndMaintenance.AdjustTips1',
          ) as string,
        );
        return;
      }
      // 调整当前处理人
      this.tipsTitle = this.$t(
        'languages.common.ProcessOperationAndMaintenance.AdjustTips2',
      ) as string;
      this.tipsContent = '';
      this.openTipsModal();
    } else if (this.manage === 'COMPLETED_WORKFLOW') {
      // 提前结束流程
      this.tipsTitle = this.$t(
        'languages.common.ProcessOperationAndMaintenance.EndTips1',
      ) as string;
      this.tipsContent = '';
      this.openTipsModal();
    } else if (this.manage === 'CANCELED_ACTIVITY_WORKITEM') {
      // 取消当前节点所有任务
      this.tipsTitle = this.$t(
        'languages.common.ProcessOperationAndMaintenance.CancelAllTaskTips1',
      ) as string;
      this.tipsContent = '';
      this.openTipsModal();
    } else if (this.manage === 'DELETE_WORKFLOW') {
      // 删除流程
      this.tipsTitle = this.$t('languages.common.form.DeleteTips3') as string;
      this.tipsContent = ((this.$t(
        'languages.common.form.DeleteTips1',
      ) as string) + this.$t('languages.common.form.DeleteTips2')) as string;
      this.openTipsModal();
    } else if (this.manage === 'MODIFY_OWNER') {
      // 修改拥有者
      if (this.owner.length === 0) {
        this.$message.warning(
          this.$t(
            'languages.common.ProcessOperationAndMaintenance.ModifyTips1',
          ) as string,
        );
        return;
      }
      if (this.departments.length === 0) {
        this.$message.warning(
          this.$t(
            'languages.common.ProcessOperationAndMaintenance.ModifyTips2',
          ) as string,
        );
        return;
      }

      this.tipsTitle = this.$t(
        'languages.common.ProcessOperationAndMaintenance.ModifyTips3',
      ) as string;
      this.tipsContent = '';
      this.openTipsModal();
    } else if (this.manage === 'xiugai') {
      // 修改流程模板
      this.modalShow = false;
      return;
    } else {
    }
  }

  openTipsModal() {
    const that = this;
    this.$confirm({
      title: (h: any) => h('div', that.tipsTitle),
      class: 'cus-confirm-modal',
      content: (h: any) =>
        h('div', [
          h('i', { class: 'icon aufontAll h-icon-all-question-circle' }),
          h('p', [`${that.tipsTitle}`]),
          `${that.tipsContent}`,
        ]),
      okText: that.$t('cloudpivot.flowCenter.pc.ok').toString(),
      cancelText: that.$t('cloudpivot.flowCenter.pc.cancel').toString(),
      onOk() {
        that.tipsOk();
      },
      onCancel() {
        console.log('Cancel');
      },
    });
  }

  tipsOk() {
    const params: any = {
      activityCode: this.currentNode.activityCode,
      approval: this.getApproval(),
      batchOperation: this.isMultiplexing,
      operationType: this.manage,
      workflowInstanceId: this.$route.params.workflowInstanceId,
    };

    switch (this.manage) {
      case 'ADJUST_PARTICIPANT': // 调整当前处理人
        params.participantors = this.userValue.map((el) => el.id);
        break;
      case 'MODIFY_OWNER': // 修改拥有者
        params.ownerDeptId = this.departments;
        params.ownerId = this.owner[0].id;
        break;
      case 'ACTIVATE_ACTIVITY': // 激活其他节点
        params.activityCode = this.activationActivityCode;
        params.participantors = this.activityParticipants.map((el) => el.id);
        break;
      default:
        break;
    }
    this.workflowOperation(params);
  }

  async workflowOperation(params) {
    await workflowApi.workflowOperation(params).then((res: any) => {
      if (res.errcode === 0) {
        this.modalShow = false;
        this.$message.success(res.errmsg);
        this.$emit('operationOver', this.manage);
      } else {
        this.$message.error(res.errmsg);
      }
    });
  }

  // 获取审批意见参数：意见、附件和签名
  getApproval() {
    const Enclosure = this.getEnclosureValue();
    return {
      activityCode: this.currentNode.activityCode,
      commonSet: false,
      deleted: false,
      resources: Enclosure,
      content: this.comment || null,
    };
  }

  // 获取附件和手写签名
  getEnclosureValue() {
    // @ts-ignore
    return (this.$refs.comment && this.$refs.comment.getValue()) || [];
  }

  // 获取批量运维的流程id[]
  getWorkflowInstanceIds() {
    return this.getWorkflowInstance().map((el) => el.id);
  }

  // 获取批量运维的流程[]
  getWorkflowInstance() {
    return this.data.filter((el) => {
      return (
        el.workflowCode === this.workflow &&
        el.activityCode === this.workflowNode
      );
    });
  }

  workflow: any = null; // 选中的流程模板

  workflowNode: any = null; // 选中的流程节点

  get checkedWorkflow() {
    if (!this.workflows || !this.workflow) {
      return {};
    }
    return this.workflows[this.workflow];
  }

  workflowChange() {
    this.workflowNode = null;
  }

  get activitys() {
    const key = this.workflow || undefined;
    if (!key) {
      return [];
    }
    return this.workflows[key].activitys;
  }

  workflows: any = {}; // 存在的流程模板

  firstData: any = {};

  init() {
    // @ts-ignore
    const bodyHeigth: number = document.querySelector('body').offsetHeight;

    if (bodyHeigth < bodyHeigth * 0.08 + 630) {
      this.top = 0;
      this.popHeigth = bodyHeigth + 'px';
    }

    this.onDataChange(this.data);
  }

  modalShow: boolean = false;

  @Watch('value', {
    immediate: true,
  })
  onVisibleChange(value: boolean) {
    this.modalShow = value;
    if (value) {
      this.init();
    }
  }

  @Watch('modalShow')
  onModalShowChange(val: boolean) {
    this.$emit('input', val);
  }

  close() {
    this.$emit('input', false);
  }

  get completed() {
    return [
      workflowEnum.WorkflowState.APPROVED,
      workflowEnum.WorkflowState.TOPPING_OFF,
    ].includes(this.workflowState as any);
  }

  showNoParticipantsList() {
    (this.$refs['noParticipantTable'] as any).showNoParticipantsList = true;
  }

  /**
   * 激活节点是预估处理人
   */
  async predictProcesser(activityCode) {
    return workflowApi.getActivityPreprocessors({
      workflowInstanceId: this.$route.params.workflowInstanceId,
      activityCode: activityCode,
    });
  }

  /**
   * 激活其他节点选择节点
   */
  onActivityChange(activityCode) {
    this.activationActivityCode = activityCode;
    this.predictProcesser(activityCode).then((res) => {
      if (res.errcode === 0) {
        this.activityParticipants = res.data.map((item) => {
          return Object.assign(item, { type: 3, unitType: 3 });
        });
      } else {
        this.$message.error(res.errmsg);
      }
    });
  }
}
</script>
<style lang="less" scoped>
@import '~cloudpivot-list/application/src/components/pc/style/custom-themes';
.pagination-wrapper {
  padding-top: 10px;
  display: flex;
  justify-content: flex-end;
  border-top: 1px solid #eee;
  margin-top: 24px;
  padding-bottom: 16px;
  width: 100%;
  & > div {
    float: right;
  }
  // 分页器高度调整
  /deep/.ant-pagination-options {
    height: 26px;
    .ant-pagination-options-size-changer {
      margin-top: 1px;
    }
  }
  /deep/.ant-pagination > li {
    border-radius: 2px;
    a,
    div,
    input {
      border-radius: 2px;
    }
    .ant-select-arrow {
      right: 7px;
    }
  }
}
.to-work-handover {
  display: flex;
  flex-direction: column;
  cursor: pointer;
  small {
    margin-left: 0;
    color: rgba(0, 0, 0, 0.45);
    max-width: 186px;
    // text-align: justify;
    word-break: break-all;
    margin-top: 10px;
  }
  p {
    color: #2970ff;
    display: flex;
    align-items: center;
    i {
      margin-left: 5px;
      font-size: 14px;
    }
    &:hover {
      color: #5291ff;
    }
  }
}

.current-node {
  background-color: #f7f7f9;
  border-radius: 4px;
  padding: 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 36px;
  height: 83px;
  .left {
    display: flex;
    flex: 1;
    .list {
      margin-bottom: 0;
      display: flex;
      flex-direction: column;
      &:first-child {
        margin-right: 48px;
      }
      label {
        width: 80px;
        color: rgba(17, 18, 24, 0.5);
        line-height: 20px;
        font-size: 12px;
        margin-bottom: 8px;
      }
      p {
        color: #111218;
      }
    }
  }
}
.show-other {
  color: @highlightColor;
  cursor: pointer;
}
small {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  margin-left: 5px;
  .font-ele {
    color: @highlightColor;
  }
}

.list {
  display: flex;
  justify-content: space-between;
  // align-items: center;
  margin-bottom: 16px;
  label {
    width: 98px;
    margin-right: 16px;
    white-space: nowrap;
  }
  & > div {
    flex: 1;
  }
}
.tips {
  width: 100%;
  height: 32px;
  line-height: 30px;
  background: #fffbe6;
  border-radius: 4px;
  border: 1px solid #ffe58f;
  padding: 0 16px;
  color: rgba(0, 0, 0, 0.65);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-bottom: 22px;
  span {
    margin-right: 10px;
    color: #faad14;
  }
}

.workflow-operation {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10px;
  flex-direction: column;
  & > label {
    margin-right: 16px;
    font-weight: 600;
    color: @primaryColor;
    line-height: 22px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
    &::before {
      content: '';
      display: inline-block;
      width: 3px;
      height: 12px;
      background-color: @primaryColor;
      margin-right: 5px;
    }
  }
  .operations {
    flex: 1;
    overflow: hidden;
  }
}
.row {
  margin-bottom: 8px;
  label {
    margin-right: 20px;
    width: 120px;
  }
}
.row-item {
  display: flex;
  justify-content: space-between;
  flex-direction: column;
  margin-bottom: 16px;
  & > label {
    margin-right: 16px;
    color: #111218;
    line-height: 22px;
    margin-bottom: 6px;
    display: flex;
    align-items: center;
    &.label-required {
      &::after {
        content: '*';
        color: #f0353f;
        line-height: 22px;
        vertical-align: sub;
        height: 22px;
        padding: 2px 0 2px 2px;
      }
    }
  }
  & > div {
    flex: 1;
  }
  .select-node {
    margin-bottom: 16px;
  }
}
.to-template {
  display: flex;
  align-items: center;

  span {
    color: @highlightColor;
    font-size: 14px;
    cursor: pointer;
    &.icon {
      padding-top: 3px;
    }
  }
  &:hover {
    span {
      color: @highlightColor;
    }
  }

  .to-modify-workflow-template {
    height: 32px;
    line-height: 32px;
    display: inline-block;
    color: @highlightColor;
    text-decoration: underline;
    text-underline-offset: 2px;
    margin-right: 6px;
  }
}
.radio-wrapper {
  display: flex;
  flex-wrap: wrap;
  label {
    margin-bottom: 8px;
    width: 25%;
    overflow: hidden;
    margin-right: 0;
    display: flex;
    align-items: center;
    /deep/span.ant-radio {
      display: flex;
      align-items: center;
      + span {
        text-overflow: ellipsis;
        width: 100%;
        overflow: hidden;
        white-space: nowrap;
      }
    }
  }
}
.multiplexing {
  display: flex;
  align-items: center;
  line-height: 21px;
  & > span {
    margin-left: 5px;
  }
}
.comment-input {
  position: relative;
  textarea {
    height: 96px;
  }
  .str-len {
    position: absolute;
    right: 10px;
    top: 70px;
    color: rgba(0, 0, 0, 0.25);
    pointer-events: none;
  }
}

.handler {
  position: relative;
  max-height: 88px;
  text-align: justify;
  padding-right: 10px;
}
.icon-up {
  position: absolute;
  left: 58%;
  transform: translateX(-50%);
  color: rgba(0, 0, 0, 0.45);
  cursor: pointer;
  top: 100%;
  margin-top: -10px;
  font-size: 12px;
}
</style>

<style lang="less">
.black-tooltip {
  .ant-popover-inner {
    background-color: rgba(0, 0, 0, 0.8);
  }
  .ant-popover-arrow {
    border-right-color: rgba(0, 0, 0, 0.8) !important;
    border-bottom-color: rgba(0, 0, 0, 0.8) !important;
  }
  p {
    color: #fff;
    font-size: 14px;
  }
}

.operations .ant-radio-group {
  width: 100%;
}

// 作废与删除确认弹窗样式重写
.cus-confirm-modal {
  .anticon-question-circle {
    display: none;
  }
  .ant-modal-confirm-content {
    font-size: 14px;
    color: #111218;
    line-height: 22px;
    p {
      font-size: 16px;
      font-weight: 600;
      color: #111218;
      line-height: 24px;
      margin-bottom: 12px;
    }
    .h-icon-all-question-circle {
      font-weight: 400;
      color: #faad14;
      font-size: 24px;
      position: absolute;
      left: 31px;
      top: 42px;
    }
  }
  .ant-modal-confirm-btns {
    .ant-btn {
      height: 32px;
    }
  }
}

.no-participants-table {
  .ant-table-thead {
    background: #f8f8fb;
    th {
      border-right: 1px solid #e8eaed;
      color: #111218;
      font-size: 13px;
      font-weight: 600;
      &:last-child {
        border-right: none;
      }
    }
  }
  .ant-table-tbody {
    td {
      border-right: 1px solid #e8eaed;
      color: #111218;
      font-size: 13px;
      &:last-child {
        border-right: none;
      }
    }
  }
}

.nodeParticipants-tooltip {
  .node-tips {
    width: 291px;
  }
}
</style>
