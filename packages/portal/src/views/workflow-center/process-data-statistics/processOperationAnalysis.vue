<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <div class="process-operation-analysiss">
    <ReportAnalysis :objectId="objectId" />
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import ReportAnalysis from 'cloudpivot/common/src/components/pc/report-analysis/report-analysis.vue';
import ReportInfos from 'cloudpivot/common/src/components/pc/report-analysis/report-info';

@Component({
  name: 'process-operation-analysiss',
  components: {
    ReportAnalysis,
  },
})
export default class processOperationAnalysis extends Vue {
  objectId: string = '';

  created() {
    this.objectId = ReportInfos.ProcessOperationAnalysis.objectId;
  }
}
</script>

<style lang="less" scoped>
.process-operation-analysiss {
  width: calc(100% + 24px);
  height: calc(100% + 40px);
  margin: -16px 0 -24px -24px;
  overflow: auto;
}
</style>
