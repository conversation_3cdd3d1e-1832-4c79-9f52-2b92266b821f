/*
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
*/
/*
 * @Author: <PERSON>
 * @Date: 2022-01-04 18:06:33
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-03-10 12:12:57
 * @FilePath: \yunshu6.0\entries\portal\src\locale\common.en-US.ts
 * @Description:
 */
export default {
  ok: 'OK',
  continue: 'Continue',
  okAndDelete: 'OK And Delete',
  delete: 'Delete',
  selectAll: 'Select All',
  cancel: 'Cancel',
  workflowCenter: 'Workflow Center',
  initiationProcess: 'Initiation Process',
  applicationList: 'Apps',
  notificationCenter: 'Notification Center',
  back: 'Back',
  workbench: 'Dashboard',
  applicationPortal: 'Application Portal',
  notification: 'Notification',
  openInBrowser: 'Open In Browser',
  workflowTrack: 'Workflow Track',
  personalInfo: 'Personal Info',
  personalSetting: 'Personal Settings',
  switch: 'Switch',
  search: 'Search',
  changeTheme: 'Skin',
  backStageManager: 'Management',
  exitSys: 'Log out',
  noData: 'No Data',
  errTips: 'The server made a slip',
  goBack: 'Manual return',
  closePage: 'Close the current page',
  close: 'Close',
  changePwd: 'Change Password',
  passwordSetting: 'Password Setting',
  oldPwd: 'oldPwd',
  oldPwdRequied: 'oldPwd cannot be empty',
  newPwd: 'newPwd',
  newPwdRequied: 'newPwd cannot be empty',
  newPwdNotSame: 'Inconsistent password input',
  surePwd: 'surePwd',
  placeholder: 'Please input',
  placeholder2: 'Please select',
  reset: 'Reset',
  changePwdSuccess: 'Password modification succeeded!',
  helpCenter: 'Help Center',
  approval: 'Approval logs',
  preEdit: 'before Edit',
  postEdit: 'after Edit',
  noHistoryRec: 'no history records',
  mainOrg: 'Main Organization',
  loginErr:
    'The system is not configured with organization information, please contact the administrator for processing.',
  noPermission: 'No permission to view the current page.',
  LoadMore: 'More',
  passwordSafeTips:
    'The system detects that the account security is low, please change the password.',
  passwordErrorTips1: 'Original password cannot be blank',
  passwordErrorTips2: 'New password cannot be blank',
  passwordErrorTips3:
    'The length of the new password should be 12-14 characters, and the new password must contain upper and lower case letters and numbers',
  passwordErrorTips4: 'The new password cannot be the same as the original',
  passwordErrorTips5: 'Confirm password cannot be empty',
  passwordErrorTips6: 'The passwords entered twice are inconsistent',
  passwordErrorTips7:
    'The length of the new password should be 8-14 characters, and the new password must contain upper and lower case letters and numbers',
  lastLoginTime: 'Last login time: ',
  helpList: {
    userManual: 'User manual',
    video: 'Video tutorial',
    developer: 'Developer’s Manual',
    customerService: 'Customer service',
  },
  userInfo: {
    name: 'Name',
    id: 'Username',
    departmentName: 'Department',
    position: 'Position',
    email: 'Email',
    employeeNo: 'Employee NO.',
    mobile: 'Mobile',
    officePhone: 'Telephone',
    roleName: 'Role',
    remark: 'Remarks',
  },
  tip: {
    saveSuccess: 'Save Success',
    operationFail: 'Operation failed, please try again',
    paramsError: 'Params Error',
    operationSucceed: 'Operation Succeed',
    operationFailed: 'Operation Failed',
    sureToLogOut: 'Are you sure you want to exit the current login?',
    confirm: 'Ok',
    cancel: 'Cancel',
  },
  ide: {
    appAdmin: 'Subadministrator',
    sysAdmin: 'System administrator',
  },
  appMenu: {
    all: 'All Applications',
    history: 'Recently Used',
    favorite: 'My Collection',
    noApplications: 'No Applications',
    noFavorite: 'No Collection',
    belongApp: 'Application',
    applications: 'Applications',
    noResult: 'No Result',
  },
  searchContent: 'Enter Search Content',

  formTitle: {
    createWorkflow: 'Start Workflow',
    createForm: 'Create Form',
    editWorkflow: 'Edit Workflow',
    editForm: 'Edit Form',
    viewWorkflow: 'View Workflow',
    viewForm: 'View Form',
  },

  ProcessOperationAndMaintenance: {
    title: 'Process Operation And Maintenance',
    NoParticipantTips1:
      'Reuse the current operation to the following no-participant workflow of the same type',
    NoParticipantTips2:
      '"No Participant" means that the current node handler leaves or is deleted',
    NoParticipantTips3:
      '"Same type" refers to the process of the same model, the same node, the same state, and the same handler',
    NoParticipantTips4: '[{name}] No Participant Process has {num}',
    ActivityTips1: 'Activation node cannot be empty!',
    ActivityTips2: 'Are you sure to activate this node "{node}"?',
    AdjustTips1: 'The current handler cannot be empty!',
    AdjustTips2: 'Are you sure you want to adjust the current handler?',
    EndTips1: 'Are you sure to end the workflow?',
    ModifyTips1: 'Owner cannot be null!',
    ModifyTips2: 'The owner department cannot be empty!',
    ModifyTips3: 'Are you sure to modify the owner?',
    CancelAllTaskTips1: 'Are you sure to cancel all tasks of the current node?',
    NoParticipant: 'No participant workflow',
  },
  WorkflowActions: {
    Nullify: 'Cancel workflow',
    Delete: 'Delete workflow',
    ActivateNode: 'activate other nodes',
    AdjustHandler: 'Adjust current handler',
    FinishInstance: 'End-workflow',
    EditOwner: 'Modify owner',
    EditTemplate: 'Modify workflow template',
    CancelAllTask: 'Cancel all task of the activity',
    SelectHandler: 'Select current handler',
    CancelTips1:
      'After canceling all tasks of the current node, the process will stay at the current node',
    SelectNode: 'Select Node',
    pleaseSelect: 'Please select',
    chooseOwner: 'Choose owner',
    nodeParticipants: 'Node participants',
    nodeParticipantsTips:
      '展示节点设置的参与者，未获取到参与者时需手动添加，根据新参与者激活节点生成任务',
  },
  placeInputSearch: 'Please input',
  NoSearchResult: 'No resulet',
  Notification: {
    Notifications: 'Notifications',
    AltAndReply: '@ & Reply',
    ReadAll: 'Read all',
    unread: 'Unread',
    read: 'Read',
    NoNewsYet: 'No news yet',
    NoticeMsg: '【{what}】 process initiated by {who} needs you to-do',
    NoticeMsg2:
      '【{org}】 {num} employee is in the state of leaving and waiting for handover: 【{who}】 please handover his duties in "Yunshu Manage-Organization-WorkTransfer',
    CheckTheDetail: 'Details',
  },
  SingleApp: {
    FrontPage: 'Front Page',
    ToDoTask: 'To-do Task',
    readingTask: 'Reading Task',
    ToReadTask: 'To-read Task',
    FinishedTask: 'Finished Task',
    FinishedRead: 'Finished Read',
    myFlow: 'My Workflow',
  },
  ApplicationPortal: {
    ApplicationPortal: 'Application Protal',
    EnterAppName: 'Please enter an app name',
    AddPortal: 'Add',
    NullTips1: 'No data yet, click ',
    NullTips2: '"Add"',
    NullTips3: ' to add an environment',
    Modify: 'Modify',
    Delete: 'Delete',
    Check: 'Check',
    Cancel: 'Cancel',
    DeleteTips1: 'Are you sure you want to delete?',
  },
  Applications: {
    Enter: 'Enter text',
    Select: 'Choose an option',
    Oneself: 'Myself',
    ThisDepartment: 'My Department',
    Custom: 'Custom',
  },
  externalLink: {
    SubmitSuccess: 'Submitted data successfully',
    ExternalLinkTips1:
      'You can save the QR code above and scan to view the data information at any time! This data can be viewed by everyone, please save it carefully and do not pass it on to other people to avoid information leakage!',
    DownloadQRCode: 'Download QR code',
  },
  form: {
    Logs: 'Operate Log',
    qrcode: 'Qrcode',
    dingtalk: 'Dingtalk',
    enterpriseWechet: 'Enterprise wechat',
    scanCodeView: 'Scan code to view data by {equip}',
    scanCodeFill: 'Scan code to fill in data by {equip}',
    CopyLink: 'Copy link',
    CopySuccess: 'Copy successed',
    CopyFailed: 'Copy failed',
    OpenInNewPage: 'Open in new page',
    Back: 'Back',
    ownerDepartment: 'Owner department',
    curActivity: 'Current Activities',
    resolver: 'Approver',
    WorkHandover: 'Work handover',
    organization: 'Organization',
    organizationTips1:
      'Incumbents and ex-employees conduct job handovers through personnel and department search',
    organizationRole: 'Organization Role',
    organizationTips2: 'Job handover through role search',
    SelectAction: 'Select Process Action',
    Total: 'Total {num}',
    NullifyTips1: 'Sure to void?',
    NullifyTips2: 'The process will stop circulating after it is invalidated',
    DeleteTips1:
      'All workflow data will be delete after workflow instance be deleted, ',
    DeleteTips2: 'and it can not restore after delete',
    DeleteTips3: 'Are you sure to delete?',
    workflowApproval: 'Process approval',
    FormTrack: 'Form track',
    Comment: 'Comment',
    invalidForm:
      'This form has not been published. Please contact the administrator for processing',
    cacheFormData: 'Keep this submission when continuing to create',
    adminTips1: 'admin cannot start workflow',
    adminTips2: 'admin cannot create data',
    adminTips3: 'admin cannot start workflow or create data',
  },
  WorkflowActionStatus: {
    Submit: 'Submit',
    Submits: 'Submit',
    Cancel: 'Cancel',
    Agree: 'Agree',
    DisAgree: 'Disagree',
    AdjustParticipant: 'Add-approver',
    Assist: 'Assist',
    Forward: 'Transfer',
    Reject: 'Reject',
    FinishInstance: 'End-workflow',
    UnRead: 'Unread',
    Read: 'Read',
    Circularize: 'Circularize',
    Empty: '',
    Revoke: 'Revoke',
    BeCanceled: 'Be Canceled',
    AutoCanceled: 'Auto Cancel',
    ActivateActivity: 'Activate Other Nodes',
    ADJUST_PARTICIPANT: 'Adjust Current Processor',
    CANCELED_ACTIVITY_WORKITEM: 'Cancel All Tasks Of The Node',
    MODIFY_OWNER: 'Modify Owner',
    TIMEOUT_CANCEL: 'Timeout Cancel',
    TIMEOUT_PASS: 'Timeout Pass',
    consult: 'Consult',
    preAddSign: 'Pre-add-approver',
  },
  WorkItemStatus: {
    NotStarted: 'Not Started',
    Processing: 'Processing',
    Completed: 'Completed',
    Canceled: 'Canceled',
    Draft: 'Draft',
    BeCanceled: 'Be Canceled',
    Invalid: 'Abandoned',
    Exception: 'Exception',
  },
  workBenchInfo: {
    FavoriteApps: 'Favorite Apps',
    ToDoTask: 'To-do Task',
    ToReadTask: 'To-read Task',
    FinishedTask2: 'Finished Task',
    FinishedRead: 'Finished Read',
    MyInitiated: 'My Workflow',
    createWorkflow: 'Start Workflow',
    MyDraft: 'My Draft',
    Previous: 'Prev',
    Next: 'Next',
    Shortcut: 'Shortcut',
    Belong: 'Application: ',
    ProcessStatistics: 'Process Statistics',
    Today: 'Today',
    ThisWeek: 'Week',
    ThisMonth: 'Month',
    Items: '{num}',
    FinishedTask: 'Finished Task',
    TimeoutTask: 'Timeout Task',
    MyInitiated2: 'My New Workflow',
    Status: 'Status',
    Template: 'Template',
    noData: 'No Data',
    Urge: 'Urge',
    Timeout: 'Timeout',
    Duration: 'Duration of stay: ',
    total: 'Total',
    item1: ', ',
    currentlyShow: 'Currently showing',
    item2: '',
    createTime: 'Creation time: ',
    loadedAll: 'Loaded full content shown',
    deleteTips1: 'Are you sure you want to delete this draft?',
    deleteTips2: 'Are you sure you want to delete the selected {num} drafts?',
    myApplications: 'My Apps',
    manage: 'Manage',
    more: 'More',
    appManagement: 'All Apps',
    tips1: 'Users manage their own authorized applications in the foreground',
    tips2: 'Please add your favorite here',
    addApp: 'Add favorite',
    placeholder1: 'Please enter an app name to search',
    searchByName: 'Please enter an name to search',
    tips3: 'clear',
    tips4: 'Click the left checkbox to select an application',
    tips5:
      'Business components have business logic and can only be customized in styles',
    workflow: 'Workflow',
  },
  login: {
    verificationCode: 'Verification code',
    verificationCodePlaceholder: 'Please enter the verification code',
    sendVerificationCodeBtn: 'Send',
    verificationCodeTips:
      'The verification code has been sent to the mobile phone {phone}',
    verificationCodeErr1: 'The verification code cannot be empty',
    verificationCodeErr2: 'Send frequently, only once a minute',
    verificationCodeErr3: 'Verification code input error or invalid',
    verificationCodeErr4: 'Failed to send verification code',
    verificationCodeErr5:
      'The mobile phone number bound by the user is invalid',
    verificationCodeErr6: 'The verification code mode is not enabled',
  },
  StartTime: 'Start time',
  EndTime: 'End time',
  PlzSelect: 'Please select',
  queryBox: {
    fixed: 'Specify date',
    dataItem: 'Current model',
    dynamic: 'Dynamic range',
    today: 'Today',
    yesterday: 'Yesterday',
    tomorrow: 'Tomorrow',
    LastWeek: 'Recent week',
    LastMonth: 'Recent mounth',
    LastYear: 'Recent year',
    ThisWeek: 'This week',
    ThisMonth: 'This month',
    ThisYear: 'This year',
    LastQuarter: 'Recent quarter',
    ThisQuarter: 'This quarter',
    last: 'Recent',
    future: 'Future',
    day: 'Day',
    week: 'Week',
    month: 'Month',
    quarter: 'Quarter',
    year: 'Year',
    days: '{symbol} {num} day',
  },
};
