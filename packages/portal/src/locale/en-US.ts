/*
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
*/
import common from './common.en-US';

import formDetail from '../views/form/locale/en-US';

import flowCenter from 'cloudpivot-flow/flow-center/locales/en-US';

import list from 'cloudpivot-list/list/locales/en-US';

import flow from 'cloudpivot-flow/flow/locales/en-US';

import formComment from 'cloudpivot-form/form-comment/locales/en-US';

import form from 'cloudpivot-form/form/locales/en-US';

import application from 'cloudpivot-list/application/locales/en-US';

import cloudpivotCommon from 'cloudpivot/common/locales/en-US';

export default {
  cloudpivot: {
    flowCenter,
    list,
    flow,
    formComment,
    form,
    application,
    cloudpivotCommon,
  },
  languages: {
    common,
    form: formDetail,
  },
};
