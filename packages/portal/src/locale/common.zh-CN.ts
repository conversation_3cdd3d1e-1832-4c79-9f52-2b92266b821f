/*
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
*/
/*
 * @Descripttion:
 * @version: v1.0
 * @Author: baidongsheng
 * @Date: 2021-12-15 16:04:48
 * @LastEditors: <PERSON>
 * @LastEditTime: 2022-02-28 17:15:26
 */

export default {
  ok: '确定',
  continue: '继续',
  okAndDelete: '确定并删除',
  delete: '删除',
  selectAll: '全选',
  cancel: '取消',
  workflowCenter: '流程中心',
  workbench: '工作台',
  applicationPortal: '应用门户',
  notification: '消息通知',
  openInBrowser: '在浏览器中打开',
  initiationProcess: '发起流程',
  applicationList: '应用列表',
  notificationCenter: '消息中心',
  back: '返回',
  workflowTrack: '流程跟踪',
  personalInfo: '个人信息',
  personalSetting: '个人设置',
  switch: '中英切换',
  search: '搜索',
  changeTheme: '更换主题',
  backStageManager: '后台管理',
  exitSys: '退出',
  noData: '暂无内容',
  errTips: '服务器开了个小差',
  goBack: '手动返回',
  closePage: '关闭当前页面',
  close: '关闭',
  changePwd: '修改密码',
  passwordSetting: '密码设置',
  oldPwd: '原密码',
  oldPwdRequied: '原密码不能为空',
  newPwd: '新密码',
  newPwdRequied: '新密码不能为空',
  newPwdNotSame: '密码输入不一致',
  surePwd: '确认新密码',
  placeholder: '请输入',
  placeholder2: '请选择',
  reset: '重置',
  changePwdSuccess: '修改密码成功！',
  helpCenter: '帮助中心',
  approval: '审批记录',
  preEdit: '编辑前',
  postEdit: '编辑后',
  noHistoryRec: '没有历史记录',
  mainOrg: '主组织',
  loginErr: '系统未配置组织信息，请联系管理员处理',
  noPermission: '无权限',
  LoadMore: '查看更多',
  passwordSafeTips: '系统检测账号安全性低，请修改密码。',
  passwordErrorTips1: '原密码不能为空',
  passwordErrorTips2: '新密码不能为空',
  passwordErrorTips3:
    '新密码长度应为12-14位，且新密码中必须包含大、小写字母和数字',
  passwordErrorTips4: '新密码不能与原密码相同',
  passwordErrorTips5: '确认密码不能为空',
  passwordErrorTips6: '两次输入密码不一致',
  passwordErrorTips7:
    '新密码长度应为8-14位，且新密码中必须包含大、小写字母和数字',
  lastLoginTime: '上次登录时间：',
  helpList: {
    userManual: '用户手册',
    video: '视频教程',
    developer: '开发者手册',
    customerService: '客服平台',
  },
  userInfo: {
    name: '姓名',
    id: '用户名',
    departmentName: '所属部门',
    position: '职位',
    email: '邮箱',
    employeeNo: '工号',
    mobile: '手机号',
    officePhone: '办公电话',
    roleName: '角色',
    remark: '备注',
  },

  tip: {
    saveSuccess: '保存成功',
    operationFail: '操作失败，请重新尝试',
    paramsError: '参数错误',
    operationSucceed: '操作成功',
    operationFailed: '操作失败',
    sureToLogOut: '确定要退出当前登录？',
    confirm: '确定',
    cancel: '取消',
  },
  ide: {
    appAdmin: '子管理员',
    sysAdmin: '系统管理员',
  },
  appMenu: {
    all: '全部应用',
    history: '最近使用',
    favorite: '我的收藏',
    noApplications: '暂无应用',
    noFavorite: '暂无收藏',
    belongApp: '所属应用',
    applications: '应用列表',
    noResult: '暂无搜索结果',
  },
  searchContent: '请输入搜索内容',

  formTitle: {
    createWorkflow: '发起流程',
    createForm: '新增表单',
    editWorkflow: '编辑流程',
    editForm: '编辑表单',
    viewWorkflow: '查看流程',
    viewForm: '查看表单',
  },

  ProcessOperationAndMaintenance: {
    title: '流程运维',
    NoParticipantTips1: '将当前操作复用于以下同类型的无参与者流程',
    NoParticipantTips2: '“无参与者”指当前节点处理人离职或被删除',
    NoParticipantTips3:
      '“同类型”指同一模型、同一节点、同一状态、同一处理人的流程',
    NoParticipantTips4: '【{name}】无参与者流程{num}条',
    ActivityTips1: '激活节点不能为空！',
    ActivityTips2: '确定激活该节点“{node}”？',
    AdjustTips1: '当前处理人不能为空！',
    AdjustTips2: '确定调整当前处理人？',
    EndTips1: '确定结束流程？',
    ModifyTips1: '拥有者不能为空！',
    ModifyTips2: '拥有者部门不能为空！',
    ModifyTips3: '确定修改拥有者？',
    CancelAllTaskTips1: '确定取消当前节点所有任务？',
    NoParticipant: '无参与者流程',
  },
  WorkflowActions: {
    Nullify: '作废流程',
    Delete: '删除流程',
    ActivateNode: '激活其他节点',
    AdjustHandler: '调整当前处理人',
    FinishInstance: '结束流程',
    EditOwner: '修改拥有者',
    EditTemplate: '修改流程模板',
    CancelAllTask: '取消当前节点所有任务',
    SelectHandler: '选择当前处理人',
    CancelTips1: '取消当前节点的所有任务后，流程会停留在当前节点',
    SelectNode: '选择节点',
    pleaseSelect: '请选择',
    chooseOwner: '选择拥有者',
    nodeParticipants: '节点参与者',
    nodeParticipantsTips:
      '展示节点设置的参与者，未获取到参与者时需手动添加，根据新参与者激活节点生成任务',
  },
  placeInputSearch: '请输入搜索内容',
  NoSearchResult: '暂无搜索结果',
  Notification: {
    Notifications: '站内消息',
    AltAndReply: '@和回复我的',
    ReadAll: '全部已读',
    unread: '未读消息',
    read: '已读消息',
    NoNewsYet: '暂无消息',
    NoticeMsg: '{who}发起的【{what}】流程需要您处理',
    NoticeMsg2:
      '【{org}】的{num}个员工为离职待交接状态：【{who}】，请在“云枢后台-组织机构-工作交接”中交接他的任务',
    CheckTheDetail: '查看详情',
  },
  SingleApp: {
    FrontPage: '首页',
    ToDoTask: '我的待办',
    readingTask: '传阅我的',
    ToReadTask: '我的待阅',
    FinishedTask: '我的已办',
    FinishedRead: '我的已阅',
    myFlow: '我发起的',
  },
  ApplicationPortal: {
    ApplicationPortal: '应用门户',
    EnterAppName: '请输入应用名称',
    AddPortal: '新 增',
    NullTips1: '暂无数据，点击',
    NullTips2: '新增',
    NullTips3: '添加环境',
    Modify: '修 改',
    Delete: '删 除',
    Check: '查 看',
    Cancel: '取 消',
    DeleteTips1: '确定要删除？',
  },
  Applications: {
    Enter: '输入文本',
    Select: '选择选项',
    Oneself: '本人',
    ThisDepartment: '本部门',
    Custom: '自定义',
  },
  externalLink: {
    SubmitSuccess: '提交数据成功',
    ExternalLinkTips1:
      '您可以保存上方二维码，随时扫描查看数据信息！此数据所有人可查看，请您谨慎保存，切勿传给其它人员，以免信息泄露！',
    DownloadQRCode: '下载二维码',
  },
  form: {
    Logs: '操作日志',
    qrcode: '二维码',
    dingtalk: '钉钉',
    enterpriseWechet: '企业微信',
    scanCodeView: '{equip}扫码查看数据',
    scanCodeFill: '{equip}扫码填写数据',
    CopyLink: '复制链接',
    CopySuccess: '复制成功',
    CopyFailed: '复制失败',
    OpenInNewPage: '新页面打开',
    Back: '返回',
    ownerDepartment: '拥有者部门',
    curActivity: '当前节点',
    resolver: '处理人',
    WorkHandover: '工作交接',
    organization: '组织机构',
    organizationTips1: '在职人员和离职人员通过人员、部门查找，进行工作交接',
    organizationRole: '组织角色',
    organizationTips2: '通过角色查找，进行工作交接',
    SelectAction: '选择流程操作',
    Total: '共{num}条',
    NullifyTips1: '确定要作废吗？',
    NullifyTips2: '作废后该流程将停止流转',
    DeleteTips1: '流程删除后该流程所有数据将删除，',
    DeleteTips2: '且删除后不可恢复',
    DeleteTips3: '确定要删除？',
    workflowApproval: '流程审批',
    FormTrack: '表单留痕',
    Comment: '评论',
    invalidForm: '该表单未发布，请联系管理员处理',
    cacheFormData: '继续创建时，保留本次提交内容',
    adminTips1: 'admin 不能发起流程',
    adminTips2: 'admin 不能新增数据',
    adminTips3: 'admin 不能发起流程或新增数据',
  },
  WorkflowActionStatus: {
    Submit: '提交',
    Submits: '提交',
    Cancel: '作废',
    Agree: '同意',
    DisAgree: '不同意',
    AdjustParticipant: '加签',
    Assist: '协办',
    Forward: '转办',
    Reject: '驳回',
    FinishInstance: '结束流程',
    UnRead: '待阅',
    Read: '已阅',
    Circularize: '传阅',
    Empty: '',
    Revoke: '撤回',
    BeCanceled: '被取消',
    AutoCanceled: '自动取消',
    ActivateActivity: '激活其他节点',
    ADJUST_PARTICIPANT: '调整当前处理人',
    CANCELED_ACTIVITY_WORKITEM: '取消节点所有任务',
    MODIFY_OWNER: '修改拥有者',
    TIMEOUT_CANCEL: '超时作废',
    TIMEOUT_PASS: '超时通过',
    consult: '征询',
    preAddSign: '前置加签',
  },
  WorkItemStatus: {
    NotStarted: '未启动',
    Processing: '进行中',
    Completed: '已完成',
    Canceled: '被取消',
    Draft: '草稿',
    BeCanceled: '被取消',
    Invalid: '作废',
    Exception: '异常',
  },
  workBenchInfo: {
    FavoriteApps: '收藏应用',
    ToDoTask: '我的待办',
    ToReadTask: '我的待阅',
    FinishedTask2: '我的已办',
    FinishedRead: '我的已阅',
    MyInitiated: '我发起的',
    createWorkflow: '发起流程',
    MyDraft: '我的草稿',
    Previous: '上一条',
    Next: '下一条',
    Shortcut: '最近使用',
    Belong: '所属应用：',
    ProcessStatistics: '流程统计',
    Today: '今天',
    ThisWeek: '本周',
    ThisMonth: '本月',
    Items: '{num}条',
    FinishedTask: '我处理的任务',
    TimeoutTask: '我的待办超时',
    MyInitiated2: '我发起的流程',
    Status: '状态',
    Template: '流程模板',
    noData: '暂无数据',
    Urge: '催办',
    Timeout: '超时',
    Duration: '停留时间：',
    total: '共',
    item1: '条，',
    currentlyShow: '当前展示',
    item2: '条',
    createTime: '创建时间：',
    loadedAll: '已展示加载完全部内容',
    deleteTips1: '确定删除此草稿？',
    deleteTips2: '确定删除所选{num}条草稿？',
    myApplications: '我的常用',
    manage: '管理',
    more: '更多',
    appManagement: '全部应用',
    tips1: '由用户在前台对自己有权限的应用进行管理',
    tips2: '请将你的常用添加到此处',
    addApp: '添加常用',
    placeholder1: '请输入应用名称进行搜索',
    searchByName: '请输入名称进行搜索',
    tips3: '清除',
    tips4: '点击左侧选框选择应用',
    tips5: '业务组件带有业务逻辑，仅可以自定义样式',
    workflow: '流程',
  },
  login: {
    verificationCode: '验证码',
    verificationCodePlaceholder: '请输入验证码',
    sendVerificationCodeBtn: '发送验证码',
    verificationCodeTips: '验证码已发送至手机{phone}',
    verificationCodeErr1: '验证码不能为空',
    verificationCodeErr2: '频繁发送，1分钟只能发送一次',
    verificationCodeErr3: '验证码输入错误或已失效',
    verificationCodeErr4: '发送验证码失败',
    verificationCodeErr5: '用户绑定手机号无效',
    verificationCodeErr6: '验证码模式未开启',
  },
  StartTime: '开始时间',
  EndTime: '结束时间',
  PlzSelect: '请选择',
  queryBox: {
    fixed: '指定日期',
    dataItem: '当前模型',
    dynamic: '动态范围',
    today: '今天',
    yesterday: '昨天',
    tomorrow: '明天',
    LastWeek: '最近一周',
    LastMonth: '最近一月',
    LastYear: '最近一年',
    ThisWeek: '本周',
    ThisMonth: '本月',
    ThisYear: '本年',
    LastQuarter: '最近一个季度',
    ThisQuarter: '本季度',
    last: '最近',
    future: '未来',
    day: '天',
    week: '周',
    month: '月',
    quarter: '季度',
    year: '年',
    days: '{symbol} {num} 天',
  },
};
