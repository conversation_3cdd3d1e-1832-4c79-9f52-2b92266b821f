/*
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
*/
import Vues from 'vue';
import 'babel-polyfill';
import * as platform from 'cloudpivot-platform/platform';
import env from '@/config/env';
import './config/axios';
import { formApi } from 'cloudpivot/api';
import { startup } from './startup';
import common from 'cloudpivot/common/mobile';
import infiniteScroll from 'vue-infinite-scroll';
import {
  initMapSecret,
  initFileListTypes,
} from 'cloudpivot/common/src/config/common/common-config';
import initFormComponent from 'cloudpivot-form/form/registerComponent';
// Vues.config.devtools = true;

//引入svg
import 'cloudpivot-icons/src/iconfont.js';
import setDevToken from '../../../config/dev-token';
import './tools/hight-light';

// 二开组件
// import extendComponents from 'cloudpivot-form-extend';
// initFormComponent(extendComponents);

// 甘特图插件的注册代码
// import GanttExport from 'cloudpivot-gantt';
// import initViewComponent from 'cloudpivot-list/list/registerView';
/**
 * 初始化甘特图视图
 */
// initViewComponent({
//   Gantt: GanttExport,
// });

import ReportOptions from '@h3/report/dist/options';

ReportOptions.charts.list.dimension = 90;
ReportOptions.charts.table.dimension = 90;
ReportOptions.charts.table.groupDimension = 90;
ReportOptions.charts.table.metric = 90;

ReportOptions.charts.maxCount = 90;

ReportOptions.charts.crosstable.metric = 90;
ReportOptions.charts.crosstable.dimension = 90;
ReportOptions.charts.crosstable.groupDimension = 90;
console.log('%cwelcome to use', 'color:red; font-size: 30px;');

setDevToken();
Vues.use(infiniteScroll);

initFormComponent();

platform.start(env.client_id, env.scope).then((result: any) => {
  // 绑定地图密钥
  initMapSecret();
  // 初始化文件上传后缀约束集合
  initFileListTypes();

  if (!result) {
    return;
  }
  const { query } = result;
  if (query.messageId && common.utils.Common.isPC) {
    openMessage(query.messageId, query);
  } else if (query.messageId && !common.utils.Common.isPC) {
    window.location.href = `${env.portalHost}/mobile/?messageId=${query.messageId}`;
  } else {
    startup(query);
    // import(/* webpackChunkName: "startup" */'./startup').then( obj => {
    //   obj.startup(query);
    // });
  }
});

Vues.prototype.getPopupContainer = (triggerNode: any) => {
  return triggerNode.parentNode.parentNode;
};
/**
 * 根据消息打开页面
 * @param messageId
 */
async function openMessage(messageId: string, query: any) {
  const $app = document.getElementById('app');
  if (!$app) {
    throw new Error(`can't find #app`);
  }

  const params: OAuth.FormUrlParams = {
    messageId,
  };

  const token = localStorage.getItem('token');

  if (token) {
    const res = await formApi.getMessageFormUrl(params);
    if (res.errcode === 0 && res.data) {
      // 跳转到消息地址或者第三方浏览器直接打开地址
      const {
        bizObjectId,
        workItemId,
        workflowInstanceId,
        schemaCode,
        sheetCode,
        url,
      } = res.data;
      let theUrl = '';

      // @ts-ignore
      if (window.config.ddMessageOpenIn === 'openSlidePanel') {
        localStorage.setItem('token', token);
        theUrl = `${env.portalHost}/mobile/#/form/detail?`;
        if (workflowInstanceId) {
          theUrl += `workflowInstanceId=${workflowInstanceId}&workitemId=${
            workItemId || ''
          }&T=${token}`;
        } else {
          theUrl += `objectId=${bizObjectId}&schemaCode=${schemaCode}&sheetCode=${sheetCode}&T=${token}`;
        }

        theUrl += '&formPcdd=true';
        location.href = theUrl;
        return;
      }

      if (url) {
        if (url.indexOf('?') > -1) {
          theUrl = `${url}&T=${token}`;
        } else {
          theUrl = `${url}?T=${token}`;
        }
      } else {
        theUrl = `${env.portalHost}/form/detail?`;
        if (workflowInstanceId) {
          theUrl += `workflowInstanceId=${workflowInstanceId}&workitemId=${
            workItemId || ''
          }&T=${token}`;
        } else {
          theUrl += `objectId=${bizObjectId}&schemaCode=${schemaCode}&sheetCode=${sheetCode}&T=${token}`;
        }
      }

      $app.innerText = '';
      $app.style.textAlign = 'center';
      $app.style.paddingTop = '20px';
      $app.style.color = '#666';
      $app.style.fontSize = '18px';

      const $a = document.createElement('a');
      $a.style.textDecoration = 'underline';
      $a.href = theUrl;
      $a.target = '_blank';
      $a.innerText = '浏览器打开应用';

      $app.appendChild($a);
      platform.service.openLink(theUrl, query);
    }
  } else {
    localStorage.setItem(
      'isShowEmailResquest',
      `${env.portalHost}?messageId=${messageId}`,
    );
    const theUrl = `${env.portalHost}/login`;
    window.location.href = theUrl;
  }
}
