// import { shallowMount, createLocalVue } from '@vue/test-utils';
// import Login from '@/views/login/login-new.vue';
// import Vuex from 'vuex';

// const localVue = createLocalVue();
// localVue.use(Vuex);

// const store = new Vuex.Store({
//   state: {
//     username: 'alice',
//     themsConfig: {
//       info: 'blue'
//     }
//   }
// });

// describe('HelloWorld.vue', () => {
//   it('renders props.msg when passed', () => {
//     const wrapper = shallowMount(Login, {
//       store,
//       localVue
//     });
//     expect(wrapper.vm).toBeTruthy();
//   });
// });
