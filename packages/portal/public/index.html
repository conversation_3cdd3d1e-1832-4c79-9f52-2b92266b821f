<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1.0">
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <!-- <script type="text/javascript" src="<%= BASE_URL %>js/vue.min.2.7.14.js"></script> -->
    <!-- <link rel="stylesheet" href="https://at.alicdn.com/t/font_817620_g5q83gbq5h.css"> -->
    <script src="<%= BASE_URL %>config.js?v=1.7"></script>
    <style scoped>
      @keyframes loading {
        from {
          transform: rotate(0deg);
        }
        to {
          transform: rotate(360deg);
        }
      }
      .app-loading{
        color: rgba(0,0,0,0.45);
        position: absolute;
        top:49%;
        left: 46%;
      }
      .la-ball-beat,
      .la-ball-beat > div {
        position: relative;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
        box-sizing: border-box;
      }
      .la-ball-beat {
        display: block;
        font-size: 0;
        color: #2970ff;
      }
      .la-ball-beat.la-dark {
        color: #333;
      }
      .la-ball-beat > div {
        display: inline-block;
        float: none;
        background-color: currentColor;
        border: 0 solid currentColor;
      }
      .la-ball-beat {
        width: 54px;
        height: 18px;
      }
      .la-ball-beat > div {
        width: 10px;
        height: 10px;
        margin: 4px;
        border-radius: 100%;
        -webkit-animation: ball-beat 0.7s -0.15s infinite linear;
        -moz-animation: ball-beat 0.7s -0.15s infinite linear;
        -o-animation: ball-beat 0.7s -0.15s infinite linear;
        animation: ball-beat 0.7s -0.15s infinite linear;
      }
      .la-ball-beat > div:nth-child(2n-1) {
        -webkit-animation-delay: -0.5s;
        -moz-animation-delay: -0.5s;
        -o-animation-delay: -0.5s;
        animation-delay: -0.5s;
      }
      .la-ball-beat.la-sm {
        width: 26px;
        height: 8px;
      }
      .la-ball-beat.la-sm > div {
        width: 4px;
        height: 4px;
        margin: 2px;
      }
      .la-ball-beat.la-2x {
        width: 108px;
        height: 36px;
      }
      .la-ball-beat.la-2x > div {
        width: 20px;
        height: 20px;
        margin: 8px;
      }
      .la-ball-beat.la-3x {
        width: 162px;
        height: 54px;
      }
      .la-ball-beat.la-3x > div {
        width: 30px;
        height: 30px;
        margin: 12px;
      }
      /*
      * Animation
      */
      @-webkit-keyframes ball-beat {
        50% {
          opacity: 0.2;
          -webkit-transform: scale(0.75);
          transform: scale(0.75);
        }
        100% {
          opacity: 1;
          -webkit-transform: scale(1);
          transform: scale(1);
        }
      }
      @-moz-keyframes ball-beat {
        50% {
          opacity: 0.2;
          -moz-transform: scale(0.75);
          transform: scale(0.75);
        }
        100% {
          opacity: 1;
          -moz-transform: scale(1);
          transform: scale(1);
        }
      }
      @-o-keyframes ball-beat {
        50% {
          opacity: 0.2;
          -o-transform: scale(0.75);
          transform: scale(0.75);
        }
        100% {
          opacity: 1;
          -o-transform: scale(1);
          transform: scale(1);
        }
      }
      @keyframes ball-beat {
        50% {
          opacity: 0.2;
          -webkit-transform: scale(0.75);
          -moz-transform: scale(0.75);
          -o-transform: scale(0.75);
          transform: scale(0.75);
        }
        100% {
          opacity: 1;
          -webkit-transform: scale(1);
          -moz-transform: scale(1);
          -o-transform: scale(1);
          transform: scale(1);
        }
      }
    </style>
    <title></title>
  </head>
  <body style="overflow: hidden;">
    <noscript>
      <strong>We're sorry but portal doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app">
      <div class="app-loading">
        <div class="la-ball-beat">
          <div></div>
          <div></div>
          <div></div>
        </div>
      </div>
    </div>
    <!-- built files will be auto injected -->
  </body>
</html>
