var host = window.location.protocol + '//' + window.location.host;
var oauthHost = window.location.protocol + '//' + window.location.host;

window.config = {
  oauthHost: oauthHost + '/api',
  redirectHost: host,
  client_id: 'api',
  scope: 'read',
  secret: '',
  apiHost: host + '/api',
  portalHost: host,
  openApplicationPortal: false, // 是否开启应用门户
  openSheetViewPattern: true, // 是否开启子表查看模式
  ddMessageOpenIn: 'tab', // 钉钉的消息通知打开位置配置，tab:工作台tab打开， openLink：外部浏览器打开，openModal：模态窗口，openSlidePanel：侧边栏，invokeWorkbench：新窗口打开
  pageMourningColor: false, // 页面开启哀悼色
};
