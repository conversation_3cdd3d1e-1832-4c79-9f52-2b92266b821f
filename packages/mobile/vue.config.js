const path = require('path');
const defaultCssVars = require('cloudpivot/common/styles/variable').mobile;
const extendLessVars = require('./extends/theme');
const modifyVars = Object.assign({}, defaultCssVars, extendLessVars);
const proxy = require('../../config/proxy');
const pages = {
  main: {
    entry: 'src/main.ts',
    template: 'public/index.html',
    filename: 'index.html',
    chunks: ['chunk-vendors', 'chunk-common', 'chunk-echarts', 'main'],
  },
  externalLink: {
    entry: 'src/views/externalLink/apps.ts',
    template: 'src/views/externalLink/el.html',
    filename: 'el.html',
    chunks: ['chunk-vendors', 'chunk-common', 'chunk-echarts', 'externalLink'],
  },
};

const TerserWebpackPlugin = require('terser-webpack-plugin');
module.exports = {
  publicPath: '/mobile/',
  pages,
  filenameHashing: true,
  lintOnSave: false,
  // 处理IE兼容————vuex持久化脚本语法转译
  transpileDependencies: ['vuex-persist', 'flatted', 'ansi-regex'], // /cloudpivot\/[\w-]+/],
  productionSourceMap: false,
  devServer: {
    port: 8089,
    open: true,
    proxy: proxy,
    client: {
      overlay: false,
    },
  },
  css: {
    loaderOptions: {
      less: {
        modifyVars: modifyVars,
        javascriptEnabled: true,
      },
    },
    sourceMap: true,
    extract:
      process.env.NODE_ENV === 'production'
        ? {
            ignoreOrder: true,
          }
        : false,
  },
  chainWebpack: (config) => {
    // 配置包分析器
    // config.plugin('webpack-bundle-analyzer').use(require('webpack-bundle-analyzer').BundleAnalyzerPlugin);

    config.resolve.alias
      .set('@', path.resolve(__dirname, './src'))
      .set('assets', path.resolve(__dirname, './src/assets'))
      .set('styles', path.resolve(__dirname, './src/styles'));
    // 删除预加载, preload删除方式一样
    ['main', 'externalLink'].forEach((name) =>
      config.plugins.delete(`prefetch-${name}`),
    );

    config.optimization.usedExports = true;
    config.optimization.minimize = true;
    config.optimization.concatenateModules = true;
    config.optimization.minimizer = [
      new TerserWebpackPlugin({
        extractComments: false, // 移除注释
        terserOptions: {
          compress: {
            drop_console: true, // 移除 console 语句
          },
        },
      }),
    ];

    config.optimization.sideEffects = true; //打开移除未使用的模块

    // 优化打包
    config.optimization.splitChunks({
      chunks: 'all',
      minSize: 300000,
      cacheGroups: {
        echarts: {
          name: 'chunk-echarts',
          test: /[\\/]node_modules[\\/]echarts[\\/]/,
          chunks: 'all',
          priority: 4,
          reuseExistingChunk: true,
          enforce: true,
        },
        '@ant-design': {
          name: 'chunk-ant-design',
          test: /[\\/]node_modules[\\/]@ant-design[\\/]/,
          minSize: 0,
          minChunks: 1,
          reuseExistingChunk: true,
          chunks: 'all',
        },
        '@h3/antd-vue': {
          name: '@h3/antd-vue',
          test: /[\\/]node_modules[\\/]@h3[\\/]antd-vue[\\/]/,
          minSize: 0,
          minChunks: 1,
          reuseExistingChunk: true,
          chunks: 'all',
        },
        '@h3/report-mobile': {
          name: '@h3/report-mobile',
          test: /[\\/]node_modules[\\/]@h3[\\/]report-mobile[\\/]/,
          minSize: 0,
          minChunks: 1,
          reuseExistingChunk: true,
          chunks: 'all',
        },
        '@h3/thinking-ui': {
          name: '@h3/thinking-ui',
          test: /[\\/]node_modules[\\/]@h3[\\/]thinking-ui[\\/]/,
          minSize: 0,
          minChunks: 1,
          reuseExistingChunk: true,
          chunks: 'all',
        },
        'h3-mobile-vue': {
          name: 'h3-mobile-vue',
          test: /[\\/]node_modules[\\/]h3-mobile-vue[\\/]/,
          minSize: 0,
          minChunks: 1,
          reuseExistingChunk: true,
          chunks: 'all',
        },
        'cloudpivot-flow': {
          name: 'cloudpivot-flow',
          test: /[\\/]cloudpivot-flow[\\/]/,
          minSize: 0,
          minChunks: 1,
          reuseExistingChunk: true,
          chunks: 'all',
        },
        'cloudpivot-list': {
          name: 'cloudpivot-list',
          test: /[\\/]cloudpivot-list[\\/]/,
          minSize: 0,
          minChunks: 1,
          reuseExistingChunk: true,
          chunks: 'all',
        },
        vendors: {
          name: 'chunk-vendors',
          test: /[\\/]node_modules[\\/]/,
          priority: -10,
          chunks: 'initial',
        },
        common: {
          name: 'chunk-common',
          minChunks: 2,
          priority: -20,
          chunks: 'initial',
          reuseExistingChunk: true,
        },
        moment: {
          name: 'chunk-moment',
          test: /[\\/]node_modules[\\/]moment[\\/]/,
          chunks: 'all',
          priority: 3,
          reuseExistingChunk: true,
          enforce: true,
        },
        'dingtalk-jsapi': {
          name: 'chunk-dingtalk-jsapi',
          test: /[\\/]node_modules[\\/]dingtalk-jsapi[\\/]/,
          chunks: 'all',
          priority: 3,
          reuseExistingChunk: true,
          enforce: true,
        },
        lodash: {
          name: 'chunk-lodash',
          test: /[\\/]node_modules[\\/]lodash[\\/]/,
          chunks: 'all',
          priority: 3,
          reuseExistingChunk: true,
          enforce: true,
        },
        zrender: {
          name: 'chunk-zrender',
          test: /[\\/]node_modules[\\/]zrender[\\/]/,
          chunks: 'all',
          priority: 3,
          reuseExistingChunk: true,
          enforce: true,
        },
      },
    });
    config.optimization.concatenateModules = true;
  },
  configureWebpack: (config) => {},
};
