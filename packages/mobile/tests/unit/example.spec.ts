/*
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
*/
// import { shallowMount } from '@vue/test-utils';
// import Login from '../../src/views/login/login-input.vue';

// describe('HelloWorld.vue', () => {
//   it('renders props.msg when passed', () => {
//     const wrapper = shallowMount(Login, {
//       attachTo: document.body
//     });
//     expect(wrapper.vm as any).toBeTruthy();
//   });
// });
