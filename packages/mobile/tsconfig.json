{"compilerOptions": {"target": "esnext", "module": "esnext", "strict": true, "jsx": "preserve", "importHelpers": true, "moduleResolution": "node", "experimentalDecorators": true, "strictFunctionTypes": false, "esModuleInterop": true, "allowSyntheticDefaultImports": true, "noImplicitAny": false, "sourceMap": false, "baseUrl": ".", "types": ["webpack-env", "jest"], "paths": {"@/*": ["./src/*"]}, "lib": ["esnext", "dom", "dom.iterable", "scripthost"]}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "tests/**/*.ts", "tests/**/*.tsx", "extends/**/*.ts", "extends/**/*.tsx", "extends/**/*.vue"], "exclude": ["node_modules"]}