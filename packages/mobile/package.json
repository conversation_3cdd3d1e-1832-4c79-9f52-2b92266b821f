{"name": "cloudpivot-mobile", "version": "6.15.15", "private": true, "description": "云枢mobile端项目", "scripts": {"serve": "npm run install-fix && vue-cli-service serve", "build": "npm run install-fix && npm run build-prod", "test": "vue-cli-service test:unit", "lint": "vue-cli-service lint", "analyze": "vue-cli-service build --report", "build-dev": "vue-cli-service build --mode development", "build-prod": "rimraf node_modules/.cache && vue-cli-service build --max_old_space_size=4096", "dev": "rimraf node_modules/.cache && vue-cli-service serve --mode debug", "dll": "webpack -p --progress --config ./webpack.dll.conf.js", "install-fix": "cd ../../bin && node ./install-fix.js", "analyz": "cross-env NODE_ENV=production vue-cli-service build --mode test"}, "dependencies": {"cloudpivot": "6.15.11", "cloudpivot-flow": "6.15.8", "cloudpivot-form": "6.15.15", "cloudpivot-forms": "6.15.7", "cloudpivot-icons": "6.15.1", "cloudpivot-list": "6.15.13", "cloudpivot-mobile-vue": "6.15.4", "cloudpivot-platform": "6.15.0", "fastclick": "1.0.6"}, "devDependencies": {"@typescript-eslint/eslint-plugin": "^4.18.0", "@typescript-eslint/parser": "^4.18.0", "@vue/cli-plugin-babel": "~5.0.0-alpha.7", "@vue/cli-plugin-eslint": "~5.0.0-alpha.7", "@vue/cli-plugin-router": "~5.0.0-alpha.7", "@vue/cli-plugin-typescript": "~5.0.0-alpha.7", "@vue/cli-plugin-vuex": "~5.0.0-alpha.7", "@vue/cli-service": "~5.0.0-alpha.7", "@vue/eslint-config-airbnb": "3.0.3", "@vue/eslint-config-typescript": "^7.0.0", "babel-core": "7.0.0-bridge.0", "babel-eslint": "^10.0.3", "babel-plugin-import": "^1.13.1", "babel-plugin-syntax-dynamic-import": "^6.18.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "eslint": "^6.7.2", "eslint-plugin-html": "^4.0.6", "eslint-plugin-typescript": "^0.13.0", "eslint-plugin-vue": "^6.2.2", "less": "^3.0.4", "less-loader": "^5.0.0", "typescript": "~4.1.5", "vconsole": "^3.3.0"}}