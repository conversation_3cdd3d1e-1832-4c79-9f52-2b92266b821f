
export default {
    announcement: {
      path: '/announcement',
      name: 'announcement',
      meta: { hideFootbar: true, title: '通知公告' },
      component: () => import('./pages/announcement/index.vue')
    },
    announcementMore: {
      path: '/announcementMore',
      name: 'announcementMore',
      meta: { hideFootbar: true, title: '通知公告' },
      component: () => import('./pages/announcement/moreItem/index.vue')
    },
    announcementDetail: {
      path: '/announcementDetail',
      name: 'announcementDetail',
      meta: { hideFootbar: true, title: '公告详情' },
      component: () => import('./pages/announcement/detail/index.vue')
    },
    bannerOne:{
      path: '/bannerOne',
      name: 'bannerOne',
      meta: { hideFootbar: true, },
      component: () => import('./pages/bannerOne/index.vue')
    },
    bannerTwo:{
      path: '/bannerTwo',
      name: 'bannerTwo',
      meta: { hideFootbar: true, },
      component: () => import('./pages/bannerTwo/index.vue')
    },
    bannerThree:{
      path: '/bannerThree',
      name: 'bannerThree',
      meta: { hideFootbar: true, },
      component: () => import('./pages/bannerThree/index.vue')
    },
    learning: {
      path: '/learning',
      name: 'learning',
      meta: { hideFootbar: true, title: '学习园地' },
      component: () => import('./pages/learning/index.vue')
    },
    learningMore: {
      path: '/learningMore',
      name: 'learningMore',
      meta: { hideFootbar: true, title: '学习园地' },
      component: () => import('./pages/learning/moreItem/index.vue')
    },
    learningDetail:{
      path:'/learningDetail',
      name:'learningDetail',
      meta: { hideFootbar: true, title: '内容详情' },
      component: () => import('./pages/learning/detail/index.vue')
    }
  } as any;
  