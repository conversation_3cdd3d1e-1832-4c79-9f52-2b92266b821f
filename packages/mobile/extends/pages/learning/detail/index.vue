<template>
  <div class="news-detail">
    <div class="container">
      <div
        style="
          font-size: 16px;
          color: #000;
          font-weight: bold;
          margin-bottom: 5px;
        "
      >
        {{ newItem.data.headerwz }}
      </div>
      <div style="font-weight: bold">{{ newItem.data.yw }}</div>
    </div>
    <div class="blue">
      <div style="margin-left: 10px">
        {{ newItem.data.blue }}
      </div>
    </div>
    <div class="title">
      {{ newItem.data.bt }}
    </div>
    <div class="timestamp">
      <div>日期：{{ newItem.data.rq.slice(0, 11) }}</div>
    </div>
    <div class="content">
      <div
        class="content"
        v-if="newItem.data.news && newItem.data.news.trim() !== ''"
        v-html="newItem.data.news"
      ></div>
      <div class="content" v-else>暂无详情内容...</div>
    </div>
    <div v-if="newItem.data.fj && newItem.data.fj.length > 0">
      <div v-for="item in newItem.data.fj" style="line-height: 1.5">
        <a :href="`http://gjlfrp.dyjkyl.com:18081/api/api/aliyun/download?refId=${item.refId}`">附件：⬇{{ item.name }}</a>
      </div>
    </div>
  </div>
</template>

<script>
import { Component, Vue } from 'vue-property-decorator';
import Api from '../../../ly-api';
@Component({
  components: {},
})
export default class detailList extends Vue {
  newItem = [];
  created() {
    this.exportDetail();
  }
  async exportDetail() {
    const data = await Api.exportDetail({
      params: {
        sheetCode: 'xxyd1',
        schemaCode: 'xxyd1',
        _viewCode: 'xxyd1',
        isWorkFlow: false,
        objectId: this.$route.query.id,
      },
    });
    this.newItem = data.data.bizObject;
  }
}
</script>

<style scoped>
.container {
  margin-left: 0;
  display: flex;
  flex-direction: column;
  text-align: left;
  margin-bottom: 10px;
}

.news-detail {
  font-family: Arial, sans-serif;
  margin: 0 16px;
  padding: 16px;
  background-color: #ffffff;
  border-radius: 8px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
  overflow-y: scroll;
  height: 100%;
}
.blue {
  background: rgb(84, 172, 235);
  text-align: left;
  font-size: 16px;
  font-weight: bold;
  color: #000;
  margin-bottom: 10px;
  padding: 5px;
}
.title {
  font-size: 20px;
  font-weight: bold;
  color: #333333;
  margin-bottom: 16px;
}

.timestamp {
  text-align: center;
  font-size: 14px;
  color: #777777;
  margin-bottom: 24px;
}

.content {
  font-size: 16px;
  line-height: 1.6;
  color: #333333;
  white-space: pre-line;
}
/deep/ img {
  height: 100%;
  width: 100%;
}
</style>
