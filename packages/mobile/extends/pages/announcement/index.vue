<template>
  <div class="home-item-box my-custiom">
    <div class="header">
      <span>通知公告</span>
    </div>
    <div>
      <div
        v-for="(item, index) in newsList.slice(0, 5)"
        :key="item.id"
      >
      <router-link :to="{ path: '/announcementDetail', query:item }" class="box-content">
        <div class="placeholder">
          {{ item.data.bt}}
        </div>
        <div class="text">
          {{ item.data.rq.slice(0,11) }}
        </div>

      </router-link>
      </div>
      <H3Button class="btn" type="default" @click="goMore"> 查看更多 </H3Button>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import Api from '../../ly-api';
import { H3Button } from '@h3/thinking-ui';
import { Button } from '@h3/antd-vue';
@Component({
  components: {
    H3Button,
    aButton: Button,
  },
})
export default class CustomNews extends Vue {
  newsList: any = [];
  created() {
    this.fetchNewsList();
  }

  async fetchNewsList() {
    const data = await Api.getList({
      filters: [],
      mobile: false,
      page: 0,
      queryCode: 'tzgg',
      schemaCode: 'tzgg',
      size: 200,
      queryVersion: 1,
      queryCondition: [],
      showTotal: false,
    });
    this.newsList = data.data.content;
  }
  goMore() {
    this.$router.push({
      path: 'announcementMore',
    });
  }
}
</script>

<style lang="less" scoped>
@import '~@/styles/mixins.less';
.my-custiom {
  margin-top: 12px;
  .px2remSmall(padding-left, 12px);
  .px2remSmall(padding-right, 12px);
  .px2remSmall(padding-top, 16px);
  .px2remSmall(padding-bottom, 16px);
  > .header {
    .px2remSmall(padding-left, 8px);
    .px2remSmall(padding-right, 8px);
  }
}
.box-content {
  display: flex;
  align-items: center; /* 垂直居中对齐 */
  margin-bottom: 1em; /* 可以调整间距 */
  margin-top: 3px;
  border-bottom: 1px solid #f7f7f7;
  color: #000;
  border-radius: 10px;
  justify-content: space-between;
  .placeholder{
    padding-left: 20px;
    color: #000;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    position: relative;
    width:150px;
    text-align: left;
  }
  .placeholder::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-60%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: black;
}
  div:last-child {
    color: #bab6b6;
    text-align: right;
    margin-left: auto; /* 使时间靠右 */
  }
}

</style>
