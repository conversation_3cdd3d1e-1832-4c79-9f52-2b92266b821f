<template>
  <div class="my-custiom">
    <div class="header">
      <span>通知公告</span>
    </div>
    <div>
      <div v-for="(item, index) in newsList" :key="item.id">
        <router-link
    :to="{ path: '/announcementDetail', query:item }"
          class="box-content"
        >
        <div class="placeholder">
          {{ item.data.bt}}
        </div>
        <div class="text">
          {{ item.data.rq.slice(0,11) }}
        </div>
        </router-link>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import Api from '../../../ly-api';

@Component({
  components: {},
})
export default class newsList extends Vue {
  newsList: any = [];
  currentPage: number = 0; // 初始化当前页为0
  created() {
    this.fetchNewsList();
  }

  async fetchNewsList() {
    const data = await Api.getList({
      filters: [],
      mobile: false,
      page: this.currentPage,
      queryCode: 'tzgg',
      schemaCode: 'tzgg',
      size: 200,
      queryVersion: 1,
      queryCondition: [],
      showTotal: false,
    });
    this.newsList = data.data.content;
  }
  goMore() {
    this.$router.push({
      path: 'newsList',
    });
  }
}
</script>

<style lang="less" scoped>
@import '~@/styles/mixins.less';
.my-custiom {
  overflow-y: scroll;
  height: calc(100% - 50px); /* 减去头部高度 */
  background-color: #f9f9f9;
  padding: 16px;
  .header {
    height: 50px;
    margin-bottom: 20px;
    font-size: 28px;
    font-family: 'Arial', sans-serif;
    color: #333;
    text-align: center;
    border-bottom: 1px solid #ddd;
  }
}
.box-content {
  display: flex;
  padding: 10px 0;
  border-bottom: 1px solid #eee;
  .placeholder{
    color: #000;
    line-height: 1.5;
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    position: relative;
    padding-left: 20px;
  }
  .placeholder::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 0;
  transform: translateY(-60%);
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background-color: black;
}
  div:last-child {
    color: #999;
    font-size: 12px;
    margin-left: auto;
  }
}
</style>
