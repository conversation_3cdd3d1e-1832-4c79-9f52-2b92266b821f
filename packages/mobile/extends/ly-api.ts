import Axios from 'axios';
import env from '@/config/env';

const api = `${env.oauthHost}/api/runtime/query/list`;

const exportApi = `${env.oauthHost}/api/runtime/query/export_data/async`;

const exportFile = `${env.oauthHost}/api/runtime/query/export_data/async/export_file`;

const exportDetail = `${env.oauthHost}/api/runtime/form/load?`;

const exportGetCode = `${env.oauthHost}/api/portal/page/getByCode`;
interface ListParams {
  filters: Array<any>;
  mobile: Boolean;
  page: Number;
  queryCode: String;
  schemaCode: String;
  size: Number;
  queryVersion: Number;
  queryCondition: Array<any>;
  showTotal: Boolean;
  view: any;
}

export default {
  // 获取列表信息
  getList(params: any): Promise<any> {
    return Axios.post(`${api}`, params);
  },

  exportData(params: any): Promise<any> {
    return Axios.post(`${exportApi}`, params);
  },

  exportFile(params: any): Promise<any> {
    return Axios.post(`${exportFile}`, params);
  },
  exportDetail(params: any): Promise<any> {
    return Axios.get(`${exportDetail}`, params);
  },
  exportGetCode(params: any): Promise<any> {
    return Axios.get(`${exportGetCode}`, params);
  },
};
