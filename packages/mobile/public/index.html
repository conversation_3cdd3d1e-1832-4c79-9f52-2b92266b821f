<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no">
    <meta name="format-detection" content="telephone=no" />
    <link rel="icon" href="<%= BASE_URL %>favicon.ico">
    <title></title>
    <style>
      *, *::before, *::after {
          box-sizing: border-box;
      }
      .loading-block {
        width: 100%;
        height: 96vh;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        overflow: hidden;
        display: none;
      }
      .loading-block img {
        display: inline-block;
        width: 60px;
        height: 60px;
        animation: load 1s infinite linear;
        -webkit-animation: load 1s infinite linear;
        -moz-animation: load 1s infinite linear;
        -ms-animation: load 1s infinite linear;
      }
      .loading-block span {
        display: inline-block;
        margin-top: 30px;
      }
      @keyframes load {
        from {
          transform: rotate(0deg);
        } to {
          transform: rotate(360deg);
        }
      }
      @-webkit-keyframes load{
        from {
          transform: rotate(0deg);
        } to {
          transform: rotate(360deg);
        }
      }
    </style>
  </head>
  <body>
    <noscript>
      <strong>We're sorry but portal doesn't work properly without JavaScript enabled. Please enable it to continue.</strong>
    </noscript>
    <div id="app">
      <div class="loading-block">
        <img src="data:image/png;base64,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">
        <span>努力加载中...</span>
      </div>
    </div>

    <script>
      var _userAgent = navigator.userAgent;
      if (!/DingTalk/gi.test(_userAgent)) {//判断是否是钉钉客户端
        document.querySelector('.loading-block').style.display = 'flex'
      }
    </script>
    <!-- <script type="text/javascript" src="<%= BASE_URL %>js/vue.min.2.7.14.js"></script> -->
    <script src="<%= BASE_URL %>config.js?v=1.7"></script>
    <!-- built files will be auto injected -->
  </body>
</html>
