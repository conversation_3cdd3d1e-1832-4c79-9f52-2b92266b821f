<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <div id="app">
    <transition name="slide-fade">
      <router-view v-if="hideFootbar || isSingleApp" />
      <nav-viewer v-else />
    </transition>
  </div>
</template>
<style lang="less">
@import './styles/index.less';
#app {
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  height: 100%;
  text-align: center;
  font-family: 'Avenir', Helvetica, Arial, sans-serif;
}
</style>

<script lang="ts">
import NavViewer from '@/views/nav-viewer.vue';
import { Component, Provide, Vue, Watch } from 'vue-property-decorator';
import { OAuthApi } from 'cloudpivot/api';
import { setPageMourningColor } from 'cloudpivot/common/src/utils/toolFn';

@Component({
  name: 'App',
  components: {
    NavViewer,
  },
  // NOTE: 每次进入路由前判定是否展示底部导航栏，在路由加载完毕后再展示
  // beforeRouteUpdate(to, from, next) {
  //   const hide = to.meta && to.meta.hideFootbar === true;
  //   const vm = this as any;
  //   vm.hideFootbar = hide;
  //   // next((vm: any) => {
  //   //   vm.hideFootbar = hide;
  //   // });
  // }
})
export default class App extends Vue {
  hideFootbar: boolean = true;

  /**
   * 以下提供的所有Provide都是用于
   * 解决移动端控制台的找不到Inject
   * 报错引入的，没有实际价值
   */
  @Provide()
  getProcessNodeDialogDisable() {}

  @Provide()
  valueChange() {}

  @Provide()
  showTimeRanges() {}

  @Provide()
  formObjSource() {}

  @Provide()
  get $message() {
    return {
      error: (msg: string) => {
        this.showError(msg);
      },
      success: (msg: string) => {
        this.showSuccess(msg);
      },
    } as any;
  }

  showError(text: string) {
    (this as any).$h3.toast.show({
      text,
      autoHide: true,
      iconType: text.length < 8 ? 'close' : '',
    });
  }

  showSuccess(text: string) {
    (this as any).$h3.toast.show({
      text,
      autoHide: true,
      iconType: 'check',
      duration: 1000,
    });
  }

  get isSingleApp() {
    if (window.Environment.appCode) {
      return true;
    }
    return false;
  }

  beforeMount() {
    const vm = this as any;
    this.$router.afterEach((to, from) => {
      const hide = to.meta && to.meta.hideFootbar === true;
      vm.hideFootbar = hide;
    });
  }

  async mounted() {
    const config = await OAuthApi.getSystemConfig();
    if (config) {
      this.$store.commit('setConfig', config);
    }
    setPageMourningColor();
  }

  @Watch('$route')
  routeChange(val) {
    if (val && val.name !== 'login') {
      const login_jump = localStorage.getItem('login_jump');
      if (!login_jump || login_jump === 'false') {
        OAuthApi.getUserInfo().then((res: any) => {
          if (res.errcode === 0) {
            const info = res.data;
            if (info.lastLoginTime) {
              localStorage.setItem('login_jump', 'true');
              const vm = this;
              this.$h3.toast.show({
                text: `${this.$t('languages.common.lastLoginTime').toString()}${
                  info.lastLoginTime
                }`,
              });
            }
            sessionStorage.setItem('user', JSON.stringify(res.data));
          }
        });
      }
    }
  }
}
</script>

<style lang="less">
@import url(./../extends/global-style.less);
</style>
