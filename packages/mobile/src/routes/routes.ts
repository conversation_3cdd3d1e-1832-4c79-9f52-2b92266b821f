/*
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
*/
export default {
  home: {
    path: '/',
    redirect: 'home',
  },
  login: {
    path: '/login',
    name: 'login',
    meta: {
      hideFootbar: true,
    },
    component: () => import('@/views/login/login.vue'),
  },
  signature: {
    path: '/signature',
    name: 'signature',
    meta: {
      hideFootbar: true,
    },
    component: () => import('@/views/signature.vue'),
  },
  apps: {
    path: '/apps',
    name: 'apps',
    meta: { title: '应用列表', titleKey: 'apps', hideFootbar: false },
    component: () => import('@/views/apps/index.vue'),
  },
  'app-item': {
    path: '/app-item/:appCode',
    name: 'app-item',
    meta: { title: '应用详情', titleKey: 'app-item', hideFootbar: true },
    component: () => import('@/views/apps/app-item.vue'),
  },
  'initial-item': {
    path: '/initial-item/:appCode',
    name: 'initial-item',
    meta: { title: '应用详情', titleKey: 'initial-item', hideFootbar: true },
    component: () => import('@/views/initial-instance/initial-item.vue'),
  },
  'my-instances': {
    root: {
      path: '/my-instances',
      name: 'my-instances',
      redirect: { name: 'workitems' },
      component: () => import('@/views/my-instances/index.vue'),
    },
    workitems: {
      path: '/my-instances/workitems',
      name: 'workitems',
      meta: {
        title: '待办',
        titleKey: 'workflows',
      },
      component: () => import('@/views/my-instances/workitems.vue'),
    },
    circulates: {
      path: '/my-instances/circulates',
      name: 'circulates',
      meta: {
        title: '待阅',
        titleKey: 'workflows',
      },
      component: () => import('@/views/my-instances/circulates.vue'),
    },
    'my-workflow': {
      path: '/my-instances/my-workflow',
      name: 'my-workflow',
      meta: {
        title: '我发起的',
        titleKey: 'workflows',
      },
      component: () => import('@/views/my-instances/my-workflow.vue'),
    },
    'finished-workitems': {
      path: '/my-instances/finished-workitems',
      name: 'finished-workitems',
      meta: {
        title: '已办',
        titleKey: 'workflows',
      },
      component: () => import('@/views/my-instances/finished-workitems.vue'),
    },
  },
  'form-detail': {
    path: '/form/detail',
    name: 'form-detail',
    props: true,
    meta: {
      hideFootbar: true,
    },
    component: () =>
      import(
        /* webpackChunkName: "form-detail" */ '@/views/form/form-detail.vue'
      ),
  },
  'form-empty': {
    path: '/form/empty',
    name: 'form-empty',
    props: true,
    meta: {
      hideFootbar: true,
    },
    component: () => import('@/views/form/empty.vue'),
  },
  'custom-page': {
    path: '/custom-page',
    name: 'custom-page',
    props: true,
    meta: {
      hideFootbar: true,
    },
    component: () => import('@/views/customPage/index.vue'),
  },
  formUnpublished: {
    path: '/form-unpublished',
    name: 'formUnpublished',
    props: true,
    meta: {
      hideFootbar: true,
    },
    component: () =>
      import(
        'cloudpivot/common/src/components/mobile/form-unpublished/form-unpublished.vue'
      ),
  },
  permission: {
    path: '/permission',
    name: 'permission',
    props: true,
    meta: {
      hideFootbar: true,
      title: '无权限',
    },
    component: () => import('@/components/global/permission/permission.vue'),
  },
  flowTrack: {
    path: '/form/workflow-track/:workflowInstanceId/:workItemId/',
    name: 'flowTrack',
    meta: {
      hideFootbar: true,
      title: '流程跟踪',
      titleKey: 'workflowTrack',
    },
    component: () => import('@/views/form/workflow-track.vue'),
  },
  FormApproval: {
    path: '/form/approval/:workflowInstanceId',
    name: 'FormApproval',
    meta: {
      hideFootbar: true,
      title: '流程跟踪',
      titleKey: 'workflowTrack',
    },
    component: () => import('@/views/form/workflow-track.vue'),
  },
  WorkflowUpdateTenedor: {
    path: '/form/workflowUpdateTenedor/:workflowInstanceId',
    name: 'WorkflowUpdateTenedor',
    meta: {
      hideFootbar: true,
      title: '修改拥有者',
      titleKey: 'WorkflowUpdateTenedor',
    },
    component: () => import('@/views/form/workflow-update-tenedor.vue'),
  },
  WorkflowAdjustment: {
    path: '/form/WorkflowAdjustment/:workflowInstanceId',
    name: 'WorkflowAdjustment',
    meta: {
      hideFootbar: true,
      title: ' ',
      titleKey: 'WorkflowAdjustment',
    },
    component: () => import('@/views/form/workflow-adjustment.vue'),
  },
  devOps: {
    path: '/form/devOps/:workflowInstanceId',
    name: 'devOps',
    meta: {
      hideFootbar: true,
      title: ' ',
      titleKey: 'devOps',
    },
    component: () => import('@/views/form/workflow-devOps.vue'),
  },

  FormApprovalDetails: {
    path: '/form/approval-details',
    name: 'FormApprovalDetails',
    meta: {
      hideFootbar: true,
      title: '流程跟踪',
      titleKey: 'workflowTrack',
    },
    component: () => import('@/views/form/form-approval-details.vue'),
  },
  FormReadDetails: {
    path: '/form/read-details',
    name: 'FormReadDetails',
    meta: {
      hideFootbar: true,
      title: '流程跟踪',
      titleKey: 'workflowTrack',
    },
    component: () => import('@/views/form/form-read-details.vue'),
  },
  approvalInfo: {
    path: '/form/approval-info',
    name: 'approvalInfo',
    meta: {
      hideFootbar: true,
      title: '运维调整',
      titleKey: 'approvalInfo',
    },
    component: () => import('@/views/form/approval-info.vue'),
  },
  'initial-instance': {
    path: '/my-instances/initial-instance',
    name: 'initial-instance',
    meta: {
      title: '发起流程',
      titleKey: 'startWorkflow',
    },
    component: () => import('@/views/initial-instance/index.vue'),
  },

  'apps-form-list': {
    path: '/apps/apps-form-list/:schemaCode',
    name: 'apps-form-list',
    meta: { hideFootbar: true, title: '应用列表', titleKey: 'apps' },
    component: () => import('@/views/apps/app-form-list.vue'),
  },
  'apps-report': {
    path: '/apps/:appCode/report/:reportCode',
    name: 'apps-report',
    meta: { hideFootbar: true, title: '应用报表', titleKey: 'apps' },
    component: () => import('@/views/apps/app-report.vue'),
  },
  custom: {
    path: '/apps/custom',
    name: 'custom',
    meta: { hideFootbar: true, title: 'test' },
    component: () => import('@/views/apps/custom-router-page/test.vue'),
  },

  singleApp: {
    path: '/singleApp',
    name: 'singleApp',
    meta: { hideFootbar: true, title: '应用列表', titleKey: 'apps' },
    component: () => import('@/views/single-app/index.vue'),
  },

  homepage: {
    path: '/home',
    name: 'home',
    meta: { hideFootbar: false, title: '首页', titleKey: 'homePage' },
    component: () => import('@/views/home/<USER>'),
  },

  // meeting: {
  //     meetingsQRCode: {

  //     }
  // }

  // 设置页面
  setting: {
    path: '/setting',
    name: 'setting',
    meta: {
      hideFootbar: false,
      title: '设置',
      titleKey: 'settings',
    },
    component: () => import('@/views/setting/setting.vue'),
  },
};
