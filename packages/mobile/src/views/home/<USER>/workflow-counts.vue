<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <div class="workflow-counts">
    <div class="info-item" @click="goto('workitems')">
      <h3-badge :text="infoData.workItemCount">
        <span class="badge-block">
          <img src="../assets/workitems.svg" />
        </span>
      </h3-badge>
      <p>{{ $t('languages.common.todo') }}</p>
    </div>

    <div class="info-item" @click="goto('finished-workitems')">
      <h3-badge>
        <span class="badge-block">
          <img src="../assets/finished-workitems.svg" />
        </span>
      </h3-badge>
      <p>{{ $t('languages.common.done') }}</p>
    </div>

    <div class="info-item" @click="goto('circulates')">
      <h3-badge :text="infoData.circulateItemCount">
        <span class="badge-block">
          <img src="../assets/circulates.svg" />
        </span>
      </h3-badge>
      <p>{{ $t('languages.common.circulate') }}</p>
    </div>

    <div class="info-item" @click="goto('my-workflow')">
      <h3-badge>
        <span class="badge-block">
          <img src="../assets/my-workflow.svg" />
        </span>
      </h3-badge>
      <p>{{ $t('languages.common.myWorkflow') }}</p>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { workbenchApi } from 'cloudpivot/api';
import { H3Badge } from '@h3/thinking-ui';

@Component({
  name: 'workflowCounts',
  components: {
    H3Badge,
  },
})
export default class workflowCounts extends Vue {
  infoData: any = {
    circulateItemCount: 0,
    toAdminItemCount: 0,
    workItemCount: 0,
    workflowCount: 0,
    finishWorkItemCount: 0,
    finishCirculateItemCount: 0,
  };

  created() {
    this.getWorkCount();
  }

  /**
   * 请求流程统计数据
   */
  async getWorkCount() {
    const res = await workbenchApi.getWorkCount();
    if (res.errcode === 0) {
      this.infoData = res.data;
    }
  }

  goto(type: string) {
    this.$router.push('/my-instances/' + type);
  }
}
</script>
<style lang="less" scoped>
.workflow-counts {
  padding-top: 14px;
  padding-bottom: 12px;
  border-radius: 6px;
  display: flex;
  background: #fff;
  box-shadow: 0px 0px 8px 0px rgba(18, 25, 51, 0.06);
  border: 1px solid rgba(209, 211, 228, 0.48);
  .info-item {
    flex: 1;
    .badge-block {
      width: 42px;
      height: 42px;
      line-height: 42px;
      border-radius: 50%;
      display: inline-block;
      background: #f6f7fb;

      img {
        width: 24px;
        height: 24px;
        font-size: 20px;
        color: #111218;
      }
    }
    p {
      margin-bottom: 0;
      margin-top: 3px;
      height: 17px;
      font-size: 12px;
      color: #111218;
      line-height: 17px;
    }
  }

  /deep/.h3think-badge .h3think-badge__content--text {
    height: 16px;
    line-height: 17px;
    border-radius: 9px;
    background: #f0353f;
    align-items: normal;
  }
}
</style>
