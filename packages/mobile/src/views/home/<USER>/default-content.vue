<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <div class="default-content">
    <img :src="defaultImg" alt="" />
    <p>
      <!-- 暂无{{ title }}，点击<span class="add" @click="openManageModal">添加</span> -->
      {{ $t('languages.common.workbanch.addTips3', { type: title })
      }}<span class="add" @click="openManageModal">{{
        $t('languages.common.workbanch.add')
      }}</span>
    </p>
  </div>
</template>

<script lang="ts">
import { Component, Prop, Vue } from 'vue-property-decorator';
const icon = require('cloudpivot/common/src/components/mobile/assets/no-loading-data.png');

@Component({
  components: {},
})
export default class DefaultContent extends Vue {
  defaultImg: string = icon;

  @Prop()
  title!: string;

  openManageModal() {
    this.$emit('openManageModal');
  }
}
</script>
<style lang="less" scoped>
.default-content {
  min-height: 158px;
  img {
    width: 120px;
    height: 120px;
  }
  p {
    height: 22px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(17, 18, 24, 0.5);
    line-height: 22px;
    .add {
      color: #2970ff;
      margin-left: 4px;
    }
  }
}
</style>
