<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <div class="home">
    <div style="margin-bottom:10px ;">
      <img src="../../../extends/pages/img/log.png" style="height: 100%; width: 100%;">
    </div>
    <bannerTwo style="margin-bottom:10px ;" />
    <bannerOne style="margin-bottom:10px ;" />
    <workflow-counts />
    <my-application />
    <bannerThree style="margin-bottom:10px ;" />
    <announcement style="margin-bottom:10px ;"></announcement>
    <learning style="margin-bottom:10px ;"></learning>
    <my-charts />
  </div>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import workflowCounts from './components/workflow-counts.vue';
import myApplication from './components/my-application.vue';
import myCharts from './components/my-charts.vue';
// 首页引入通知公告
import announcement from '../../../extends/pages/announcement/index.vue';
//引入第一个轮播
import bannerOne from '../../../extends/pages/bannerOne/index.vue';
//引入第二个轮播
import bannerTwo from '../../../extends/pages/bannerTwo/index.vue';
//引入第三个轮播
import bannerThree from '../../../extends/pages/bannerThree/index.vue';
//引入学习园地
import learning from '../../../extends/pages/learning/index.vue';
@Component({
  name: 'home',
  components: {
    workflowCounts,
    myApplication,
    myCharts,
    announcement,
    bannerOne,
    bannerTwo,
    bannerThree,
    learning,
  },
})
export default class Home extends Vue {}
</script>
<style lang="less" scoped>
.home {
  width: 100%;
  padding: 12px;
  height: 100%;
  /deep/.home-item-box {
    width: 100%;
    background: #fff;
    box-shadow: 0px 0px 8px 0px rgba(18, 25, 51, 0.06);
    border: 1px solid rgba(209, 211, 228, 0.48);
    border-radius: 6px;
    > .header {
      height: 24px;
      line-height: 24px;
      font-size: 14px;
      flex: 0;
      text-align: left;
      font-weight: 600;
      color: #111218;
      .manage-opt {
        float: right;
        color: #111218;
        font-size: 13px;
        font-weight: 400;
      }
    }
  }
}
</style>

<style lang="less">
@import '~cloudpivot-mobile-vue/src/style/common-style.less';
@import '~@/styles/mixins.less';
.manage-edit-box {
  display: flex;
  flex-flow: column;
  background: #f1f2f3;
  height: 100%;
  .manage-edit-header {
    display: flex;
    justify-content: space-between;
    .px2remSmall(height, 57px);
    background: #ffffff;
    .px2remSmall(padding-top, 0);
    .px2remSmall(padding-right, 16px);
    .px2remSmall(padding-bottom, 0);
    .px2remSmall(padding-left, 16px);
    border-bottom: 1px solid rgba(209, 211, 228, 0.48);
    .manage-edit-name {
      margin: 0 auto;
      .px2remSmall(height, 56px);
      .px2remSmall(font-size, 17px);
      font-weight: 600;
      color: #111218;
      .px2remSmall(line-height, 56px);
    }
    .close-modal {
      position: absolute;
      right: 16px;
      .px2remSmall(width, 20px);
      .px2remSmall(height, 56px);
      .px2remSmall(line-height, 20px);
      .px2remSmall(padding-top, 18px);
      .px2remSmall(padding-right, 0);
      .px2remSmall(padding-bottom, 18px);
      .px2remSmall(padding-left, 0);
      .h-icon-all-close {
        display: inline-block;
        .px2remSmall(width, 20px);
        .px2remSmall(height, 20px);
        .px2remSmall(font-size, 20px);
        color: #111218;
      }
    }
  }
  .manage-edit-body {
    flex: 1;
    height: calc(100% - 117px); // height: calc(100% - 117px);
    overflow: auto;
    .body-top,
    .body-bottom {
      background: #ffffff;
      padding: 12px;
    }
    .body-top {
      margin-bottom: 8px;
      min-height: 164px;
    }
    // 我的常用
    .sub-title {
      padding-left: 4px;
      height: 20px;
      font-size: 13px;
      font-weight: 600;
      color: #111218;
      line-height: 20px;
      margin-bottom: 10px;
      .count {
        color: rgba(17, 18, 24, 0.5);
      }
      .selected {
        color: #2970ff;
      }
    }
    .sub-content {
      .app-group-name {
        margin-top: 10px;
        padding-left: 4px;
        height: 20px;
        font-size: 13px;
        color: rgba(17, 18, 24, 0.5);
        line-height: 20px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
        // i {
        //   color: rgba(17, 18, 24, 0.25);
        //   font-size: 12px;
        //   scale: 0.75;
        //   width: 20px;
        //   display: inline-block;
        //   height: 20px;
        //   text-align: center;
        // }
      }
    }
    .empty-content {
      text-align: center;
      height: 110px;
      line-height: 110px;
      font-size: 14px;
      color: rgba(17, 18, 24, 0.5);
    }
    // 我的图表
  }
  .manage-edit-foot {
    display: flex;
    .px2remSmall(height, 60px);
    background: #ffffff;
    box-shadow: inset 0px 0.5px 0px 0px rgba(209, 211, 228, 0.65); // box-shadow: inset 0px 1px 0px 0px rgba(209, 211, 228, 0.65);
    .px2remSmall(padding-top, 10px);
    .px2remSmall(padding-right, 16px);
    .px2remSmall(padding-bottom, 10px);
    .px2remSmall(padding-left, 16px);
    .btn {
      background: #ffffff;
      border: 0.5px solid rgba(17, 18, 24, 0.5);
      .px2remSmall(font-size, 15px);
      color: #111218;
      &.h3think-button--primary {
        border: unset;
        background: #2970ff;
        .px2remSmall(border-radius, 6px);
        color: #ffffff;
      }
      & + .btn {
        .px2remSmall(margin-left, 16px);
      }
    }
  }
}

.h3-toast-mask {
  z-index: 9999;
}
</style>
