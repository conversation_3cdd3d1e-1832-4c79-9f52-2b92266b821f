<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <h3-single-chart
    :key="apprep.uuid"
    :corpId="apprep.corpId"
    :reportId="apprep.objectId"
    :chartId="apprep.uuid"
    :config="config"
  >
    <div slot="title">
      <div class="header">{{ apprep.title }}</div>
    </div>
  </h3-single-chart>
</template>

<script lang="ts">
import { Component, Vue, Prop } from 'vue-property-decorator';
import { MDashboard } from '@h3/report-mobile';
import '@/views/apps/report-service';
@Component({
  name: 'SingleChartRender',
  components: {
    H3SingleChart: MDashboard.SingleChart,
  },
})
export default class SingleChartRender extends Vue {
  @Prop() apprep!: any;

  @Prop() config!: any;

  mounted() {}
}
</script>

<style lang="less" scoped></style>
