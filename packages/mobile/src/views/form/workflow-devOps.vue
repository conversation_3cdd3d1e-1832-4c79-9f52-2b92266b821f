<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <!-- 流程运维 -->
  <div class="devOps-wrapper">
    <h3-nav-bar
      v-if="!(isDingTalk || isWeChat)"
      class="devOps-wrapper-header"
      title="流程运维"
    >
      <template slot="left">
        <div class="go-back-box" @click="goBackPage">
          <span class="go-back">
            <span class="aufontAll h-icon-all-bevel-bottom-stroke"></span>
            <span class="text">返回</span>
          </span>
        </div>
      </template>
    </h3-nav-bar>
    <div class="devOps-wrapper-body" :class="{ noBar: isDingTalk || isWeChat }">
      <div v-if="currentOptions.length" class="choice-node">
        <h3-radio-list
          class="cur-other"
          showMode="0"
          :defaultValue="activityCode"
          :options="currentOptions"
          layout="vertical"
          title="当前节点"
          :notFoundText="$t('cloudpivot.form.renderer.noOptions')"
          :clearText="$t('cloudpivot.form.renderer.clear')"
          :confirmText="$t('cloudpivot.form.renderer.ok')"
          @onChange="currentActivityCodeChange"
        />
        <H3Field
          label="当前处理人"
          :showIcon="false"
          layout="vertical"
          class="cur-other choice-node-inner no-icon"
        >
          <div class="field-value-box">
            <div
              ref="cur-processor"
              class="value-text"
              :class="{ expend: showAll }"
            >
              <span>
                {{ getShowText(participants.map((el) => el.name).join('、')) }}
              </span>
            </div>
            <div v-if="showExportBtn" class="value-opts">
              <span v-if="!showAll" @click="toggleShowAll">展开</span>
              <span v-else @click="toggleShowAll">收起</span>
            </div>
          </div>
        </H3Field>
      </div>

      <div class="inner">
        <h3-radio-list
          class="cur-other"
          showMode="0"
          :defaultValue="autoSelect"
          :options="displayOptions"
          layout="vertical"
          title="流程操作"
          :notFoundText="$t('cloudpivot.form.renderer.noOptions')"
          :clearText="$t('cloudpivot.form.renderer.clear')"
          :confirmText="$t('cloudpivot.form.renderer.ok')"
          @onChange="onChange"
        />

        <template v-if="manage === 'ADJUST_PARTICIPANT'">
          <!-- 调整当前处理人 -->
          <mobile-staff-selector
            v-model="userValue"
            :required="true"
            :options="staffSelectorOpts"
            layout="vertical"
            :params="{ sourceType: 'portal' }"
            title="处理人"
            class="cur-other"
          />
        </template>

        <template v-if="manage === 'ACTIVATE_ACTIVITY'">
          <!-- 激活其他节点 -->
          <h3-radio-list
            class="cur-other"
            showMode="0"
            :defaultValue="autoSelect"
            :options="nodeOptions"
            :required="true"
            layout="vertical"
            :title="$t('cloudpivot.form.renderer.ChooseNode')"
            :notFoundText="$t('cloudpivot.form.renderer.noOptions')"
            :clearText="$t('cloudpivot.form.renderer.clear')"
            :confirmText="$t('cloudpivot.form.renderer.ok')"
            @onChange="nodeChange"
          />

          <mobile-staff-selector
            v-model="nodeParticipants"
            :required="true"
            :options="staffSelectorOpts"
            layout="vertical"
            :params="{ sourceType: 'portal' }"
            title="节点参与者"
            class="cur-other"
          />
        </template>

        <template v-if="manage === 'MODIFY_OWNER'">
          <!-- 修改拥有者 -->
          <mobile-staff-selector
            v-model="owner"
            :required="true"
            :options="taffSelectorOpts"
            layout="vertical"
            :params="{ sourceType: 'portal' }"
            title="拥有者"
            class="cur-other"
            @change="onValueChange"
          />

          <h3-radio-list
            v-if="owner.length"
            v-model="departments"
            class="cur-other"
            showMode="0"
            :defaultValue="departments"
            :options="departmentsList"
            title="拥有者部门"
            layout="vertical"
            :notFoundText="$t('cloudpivot.form.renderer.noOptions')"
            :clearText="$t('cloudpivot.form.renderer.clear')"
            :confirmText="$t('cloudpivot.form.renderer.ok')"
            @onChange="departmentsChange"
          />
        </template>

        <template v-if="!['DELETE_WORKFLOW'].includes(manage)">
          <H3Field
            label="处理意见"
            :showIcon="false"
            layout="vertical"
            class="cur-other cur-approver-option no-icon"
          >
            <ApprovalTextOpinion
              v-model="comment"
              :maxLength="200"
              :placeholder="'请输入审批意见'"
              :rowSize="{ minRows: 3, maxRows: 3 }"
            />
          </H3Field>

          <H3Field
            :label="$t('cloudpivot.form.renderer.attachment')"
            :showIcon="false"
            layout="vertical"
            class="cur-other cur-file-upload no-icon"
          >
            <ApprovalUpload
              :DefaultFileService="DefaultFileService"
              @fileListChange="onFileChange"
            />
          </H3Field>

          <H3Field
            :label="$t('cloudpivot.form.renderer.sign')"
            :showIcon="false"
            layout="vertical"
            class="cur-other cur-file-upload no-icon"
          >
            <ApprovalSignature
              :DefaultFileService="DefaultFileService"
              @signatureChange="upLoad"
            />
          </H3Field>
        </template>
      </div>

      <div
        v-if="
          workItemSource === 1 &&
          total &&
          !['CANCELED_ACTIVITY_WORKITEM', 'ACTIVATE_ACTIVITY'].includes(manage)
        "
        class="is-batch"
      >
        <H3Field
          label="批量处理"
          :showIcon="false"
          layout="vertical"
          class="cur-other batch-opt no-icon"
        >
          <div class="batch-opt-box">
            <div class="batch-opt-switch">
              <h3-switch
                slot="extra"
                v-model="isMultiplexing"
                activeColor="#2970ff"
              />
            </div>
            <div class="batch-opt-tips">
              <span class="tips-title">开启后当前操作将复用于以下同类型的无参与者流程</span>
              <span class="tips-body">1. “无参与者”指当前节点处理人离职或被删除</span>
              <span class="tips-body">2.“同类型”指同一流程模版、同一版本、同一节点、同一状态的流程</span>
            </div>
            <div class="batch-opt-data">
              <span @click="toNoParticipantsList">无参与者流程({{ total }})</span>
            </div>
          </div>
        </H3Field>
      </div>

      <div class="border-top">
        <h3-button class="border-top-btn" type="primary" @click="ok">
          {{ $t('cloudpivot.form.runtime.biz.ok') }}
        </h3-button>
      </div>
    </div>

    <div v-if="noParticipantsListVisible" class="noParticipantsList">
      <h3-nav-bar
        v-if="!(isDingTalk || isWeChat)"
        class="header"
        title="无参与者流程"
      >
        <template slot="left">
          <div class="go-back-box" @click="goBack">
            <span class="go-back">
              <span class="aufontAll h-icon-all-bevel-bottom-stroke"></span>
            </span>
          </div>
        </template>
      </h3-nav-bar>
      <div class="list-wrapper" :class="{ noBar: isDingTalk || isWeChat }">
        <WorkitemCard
          v-for="(workitem, index) in noParticipantsList"
          :key="index"
          class="list-item"
          :originalData="workitem"
          :baseAttrValueMapping="workflowBaseAttrValueMapping"
          :bottomTimeConfig="workflowBottomTimeConfig"
          :attrConfigs="workflowAttrConfigs"
        />
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import { isiOS } from 'cloudpivot/common/src/utils/common';
import dd from 'dingtalk-jsapi';
import { H3RadioList } from 'cloudpivot-mobile-vue';
import { H3Field, H3Switch, H3NavBar, H3Button } from '@h3/thinking-ui';
import mobile from 'cloudpivot-form/form/src/renderer/components/mobile';
import common from 'cloudpivot/common/mobile';
import MobileStaffSelector from 'cloudpivot-form/form/src/common/components/form-staff-selector/mobile/mobile-staff-selector.vue';
import { workflowApi, userApi } from 'cloudpivot/api';
import { DefaultFileService } from '@/config/h3-form/file-service';
import signatrue from 'cloudpivot-form/form/src/renderer/components/shared/signatrue.vue';
import OAuthApi from '@/views/login/oauth-api';
import ApprovalTextOpinion from 'cloudpivot/common/src/components/mobile/approval-tools/approval-text-opinion.vue';
import ApprovalUpload from 'cloudpivot/common/src/components/mobile/approval-tools/approval-upload.vue';
import ApprovalSignature from 'cloudpivot/common/src/components/mobile/approval-tools/approval-signature.vue';
import WorkitemCard from 'cloudpivot-flow/flow-center/src/components/mobile/components/list-content/workitem-card.vue';
import { getAllWorkflowFieldConfig } from 'cloudpivot-flow/flow-center/src/typings/workflow-list-config';
import WorkflowMixin from 'cloudpivot-flow/flow-center/src/components/mobile/mixins/workitem';
import * as platform from 'cloudpivot-platform/platform';
import * as Back from 'cloudpivot/common/src/config/mobile/back';
import { getUrlStroage } from 'cloudpivot/common/src/utils/url';

@Component({
  name: 'devOps',
  components: {
    H3RadioList,
    H3Button,
    CheckBox: common.components.Checkbox,
    MobileStaffSelector,
    signatrue,
    H3Switch,
    H3Scroll: mobile.H3Scroll,
    H3Field,
    ApprovalTextOpinion,
    ApprovalUpload,
    ApprovalSignature,
    WorkitemCard,
    H3NavBar,
  },
})
export default class devOps extends WorkflowMixin {
  autoSelect: any = '';

  manage: string = ''; // 操作类型

  userValue: any[] = []; // 当前处理人

  nodeParticipants: any[] = []; // 节点参与者

  activationActivityCode: string = ''; // 激活节点

  owner: Array<any> = []; // 拥有者

  departments: string = ''; // 拥有者部门

  comment: string = ''; // 审批意见

  basaImg: string = '';

  signatrue: any = null; // 签名

  fileList: any[] = []; // 附件

  isMultiplexing: boolean = false; // 是否批量处理

  total: any = 0; // 无参与者总数

  activityCode: string = ''; //当前节点

  noParticipantsListVisible: boolean = false;

  showExportBtn: boolean = false;

  get workflowBottomTimeConfig() {
    return {
      color: (item) => {
        if (item.workItemTimeoutStatus !== 'TIMEOUT') {
          return '#111218';
        } else {
          return '#F0353F';
        }
      },
      valueMap: (item) => {
        if (item.workItemTimeoutStatus !== 'TIMEOUT') {
          return '已停留 ' + item['stayTimeStr'];
        } else {
          return '已超时 ' + item['timeoutTimeStr'];
        }
      },
    };
  }

  get workflowBaseAttrValueMapping() {
    return {
      instanceName: 'instanceName',
      status: '',
      curNode: 'activityName',
      initiator: '',
    };
  }

  get workflowAttrConfigs() {
    const { originatorName, receiveTime } = getAllWorkflowFieldConfig(this);

    return [originatorName, receiveTime];
  }

  get isDingTalk() {
    return platform.IS_DINGTALK;
  }

  get isWeChat() {
    return platform.IS_WECHAT;
  }

  @Watch('noParticipantsListVisible')
  onShowModalChange(noParticipantsListVisible) {
    if (noParticipantsListVisible) {
      Back.subscribeBack({
        callback: (event: Event) => {
          if (event) {
            event.preventDefault();
          }
          this.noParticipantsListVisible = false;
          return;
        },
      });
      Back.useScribeBack();
    } else {
    }
  }

  mounted() {
    const vm = this;
    this.$nextTick(() => {
      const node = vm.$refs['cur-processor'] as HTMLElement;
      vm.showExportBtn = node.scrollHeight > node.offsetHeight + 3;
    });
  }

  departmentsChange(e) {
    this.departments = typeof e === 'object' ? e.value : e;
  }

  toNoParticipantsList() {
    this.noParticipantsListVisible = true;
    this.getListInstances(0, 9999);
  }

  showAll: boolean = false;

  toggleShowAll() {
    this.showAll = !this.showAll;
  }

  getShowText(text: string) {
    if (!text) {
      return '';
    }
    // if (this.showAll) {
    return text;
    // } else {
    //   return text.slice(0, 40);
    // }
  }

  tips: string = '';

  ok() {
    let activityName = '';
    const errorList: any[] = [];
    switch (this.manage) {
      case 'ACTIVATE_ACTIVITY':
        if (this.activationActivityCode === '') {
          errorList.push('激活节点不能为空！');
        } else if (this.nodeParticipants.length === 0) {
          errorList.push('节点参与者不能为空');
        } else {
          // @ts-ignore
          activityName = this.nodeOptions.find(
            (el) => el.value === this.activationActivityCode,
          ).label;
        }
        break;
      case 'ADJUST_PARTICIPANT':
        if (!this.userValue.length) {
          errorList.push('当前处理人不能为空！');
        }
        break;
      case 'MODIFY_OWNER':
        // 修改拥有者
        if (this.owner.length === 0) {
          errorList.push('拥有者不能为空！');
        }
        if (this.departments.length === 0) {
          errorList.push('拥有者部门不能为空！');
        }
        break;
      case '':
        errorList.push('请选择流程操作！');
        break;
      default:
        break;
    }

    if (errorList.length) {
      this.$h3.toast.show({
        text: errorList[0],
        iconType: '',
      });
      return;
    }
    const Obj = {
      DELETE_WORKFLOW:
        '流程删除后该流程所有数据将删除，且删除后不可恢复，确定要删除？',
      COMPLETED_WORKFLOW: '是否提前结束流程？',
      ADJUST_PARTICIPANT: '是否调整当前处理人？',
      MODIFY_OWNER: '是否修改拥有者？',
      ACTIVATE_ACTIVITY: `是否激活${activityName}节点？`,
      CANCELED_ACTIVITY_WORKITEM:
        '取消当前节点所有任务后流程会停滞，确定要取消？',
      CANCELED_WORKFLOW: '作废后该流程将停止流转，确定要作废？',
    };
    this.tips = Obj[this.manage] || '';

    this.$h3.modal.show({
      type: 'alert',
      content: this.tips,
      actions: [
        {
          text: this.$t('languages.common.cancel'),
          onPress: () => {
            console.log('取消');
          },
        },
        {
          text: '确定',
          onPress: () => {
            this.tipsOk();
          },
        },
      ],
    });
  }

  tipsOk() {
    const params: any = {
      activityCode: this.activityCode,
      approval: this.getApproval(),
      batchOperation: this.isMultiplexing,
      operationType: this.manage,
      workflowInstanceId: this.workflowInstanceId,
    };

    switch (this.manage) {
      case 'ADJUST_PARTICIPANT': // 调整当前处理人
        params.participantors = this.userValue.map((el) => el.id);
        break;
      case 'MODIFY_OWNER': // 修改拥有者
        params.ownerDeptId = this.departments;
        params.ownerId = this.owner[0].id;
        break;
      case 'ACTIVATE_ACTIVITY': // 激活其他节点
        params.activityCode = this.activationActivityCode;
        params.participantors = this.nodeParticipants.map((el) => el.id);
        break;
      default:
        break;
    }
    this.workflowOperation(params);
  }

  // 提交运维数据
  async workflowOperation(params) {
    await workflowApi.workflowOperation(params).then((res: any) => {
      if (res.errcode === 0) {
        this.isReturn();
      } else {
        this.$h3.toast.show({
          text: res.errmsg,
          iconType: '',
        });
      }
    });
  }

  isReturn() {
    this.$h3.toast.show({
      text: '操作成功',
      iconType: 'check',
    });
    setTimeout(() => {
      const url: any =
        getUrlStroage(this.$route.query.return) || this.$route.query.return;
      this.$router.push({ path: url + '?' + new Date().getTime() });
    }, 1000);
  }

  noParticipantsList: any[] = [];

  async getListInstances(page: number, size: number) {
    const params: any = {
      instanceState: 'PROCESSING',
      workflowCode: this.$route.params.workflowCode,
      workflowVersion: this.$route.params.workflowVersion,
      activityCode: this.activityCode,
      filterWorkflowInstanceId: this.workflowInstanceId,
      workItemSource: 1,
      page: page,
      size: size,
    };
    await workflowApi.searchWorkitems(params).then((res: any) => {
      if (res.errcode === 0) {
        (this as any).processResult(res, '');
        this.total = res.data.totalElements;
        this.noParticipantsList = res.data.content;
      } else {
        this.$message.error(res.errmsg);
      }
    });
  }

  // 获取审批意见参数：意见、附件和签名
  getApproval() {
    let resources: any[] = [];
    if (this.signatrue) {
      resources = [...this.fileList, this.signatrue];
    } else {
      resources = [...this.fileList];
    }

    return {
      activityCode: this.activityCode,
      commonSet: false,
      deleted: false,
      resources: resources,
      content: this.comment || null,
    };
  }

  upLoad(file: any) {
    this.signatrue = file;
  }

  DefaultFileService: any = new DefaultFileService();

  onFileChange(info: { value: any }, files: any[]): void {
    this.fileList = files
      .filter((f) => f.status === 2)
      .map((f) => f.response.data);
  }

  staffSelectorOpts = {
    selectOrg: false,
    selectUser: true,
    mulpitle: true,
    showModel: true,
    showSelect: true,
    placeholder: '',
  };

  taffSelectorOpts = {
    selectOrg: false,
    selectUser: true,
    mulpitle: false,
    showModel: true,
    showSelect: true,
    placeholder: '请选择',
  };

  nodeChange(e) {
    this.activationActivityCode = typeof e === 'object' ? e.value : e;
    this.onActivityChange(this.activationActivityCode);
  }

  @Watch('activityCode')
  onActivityCodeChange(val) {
    if (val) {
      this.getListInstances(0, 1);
    }
  }

  onChange(e) {
    this.manage = typeof e === 'object' ? e.value : e;
  }

  options: any[] = [];

  nodeOptions: any[] = [];

  //当前流程实例下所有的任务节点，不含子流程节点
  workflowNodes: any[] = [];

  async getListWorkflow() {
    const res: any = await workflowApi.getWorkflowNodes({
      workflowInstanceId: this.workflowInstanceId,
    });
    if (res.errcode === 0) {
      let optList = {};
      this.workflowNodes = res.data;
      res.data.forEach((r: any) => {
        if (r.status === 0) {
          optList = {
            key: r.activityCode,
            value: r.activityCode,
            label: r.activityName,
          };
          this.nodeOptions.push(optList);
        }
      });
    }
  }

  departmentsList: any[] = [];

  onValueChange(value: any[]) {
    if (value.length > 0) {
      this.getUserDepartments(value[0].id);
    } else {
      this.departmentsList = [];
      this.departments = '';
    }
  }

  async getUserDepartments(userId: string) {
    const res: any = await userApi.getUserDepartments(userId);
    if (res && res.errcode === 0) {
      const list: any[] = [];
      res.data.forEach((r: any) => {
        list.push({
          key: r.deptId,
          value: r.deptId,
          label: r.deptName,
        });
      });
      this.departmentsList = list;
      const resoure = res.data.find((d: any) => d.isMain);
      this.departments = resoure.deptId;
    } else {
      this.$message.error(res.errmsg);
    }
  }

  currentOptions: any[] = [];

  created() {
    this.getUserInfo();
    this.getListWorkflow();
    // @ts-ignore
    this.currentOptions =
      Array.isArray(this.$route.params.participants) &&
      this.$route.params.participants.map((el) => {
        return {
          key: el.activityCode,
          value: el.activityCode,
          label: el.activityName,
        };
      });
    // @ts-ignore
    this.activityCode =
      (Array.isArray(this.$route.params.participants) &&
        this.$route.params.participants[0] &&
        this.$route.params.participants[0].activityCode) ||
      '';
    // @ts-ignore
    this.participants =
      (Array.isArray(this.$route.params.participants) &&
        this.$route.params.participants[0] &&
        this.$route.params.participants[0].participants) ||
      '';
    // @ts-ignore
    this.workItemSource =
      (Array.isArray(this.$route.params.participants) &&
        this.$route.params.participants[0] &&
        this.$route.params.participants[0].workItemSource) ||
      0;

    this.initScope(); // 初始化流程操作范围
  }

  participants: any[] = [];

  workItemSource: number = 0;

  currentActivityCodeChange(e) {
    this.activityCode = typeof e === 'object' ? e.value : e;
    // @ts-ignore
    this.participants =
      Array.isArray(this.$route.params.participants) &&
      this.$route.params.participants.find(
        (el) => el.activityCode === this.activityCode,
      ).participants;
    // @ts-ignore
    this.workItemSource =
      Array.isArray(this.$route.params.participants) &&
      this.$route.params.participants.find(
        (el) => el.activityCode === this.activityCode,
      ).workItemSource;
  }

  isWORKFLOW_ADMIN: boolean = false;

  async getUserInfo() {
    const res = await OAuthApi.getUserInfo();
    if (res.errcode === 0) {
      const info: any = res.data;
      // 判断当前用户角色
      const isAppAdmin: boolean = info.permissions.includes('APP_MNG');
      const isSysAdmin: boolean = info.permissions.includes('SYS_MNG');
      const isRootAdmin: boolean = info.permissions.includes('ADMIN');
      const isAdmin: boolean = isAppAdmin || isSysAdmin || isRootAdmin;

      const isWORKFLOW_ADMIN: boolean =
        info.permissions.includes('WORKFLOW_ADMIN'); // 特权人
      if (!isRootAdmin && !isSysAdmin) {
        // 不是管理员，只是特权人
        this.isWORKFLOW_ADMIN = true;
        this.getWorkflowAdminManageScopes();
      } else {
        //Else Empty block statement
      }
    }
  }

  workflowAdminManageScopes: any[] = [];

  async getWorkflowAdminManageScopes() {
    await workflowApi
      .getWorkflowAdminManageScopesByWorkflowInstanceId({
        workflowInstanceId: this.workflowInstanceId || '',
      })
      .then((res: any) => {
        if (res.errcode === 0) {
          this.workflowAdminManageScopes = res.data || [];
        }
      });
  }

  /**
   * 更新流程操作
   */
  get displayOptions() {
    return this.options.filter((el) => {
      //子流程不允许显示调整处理人操作与取消所有任务操作
      if (
        ['ADJUST_PARTICIPANT', 'CANCELED_ACTIVITY_WORKITEM'].includes(el.value)
      ) {
        if (
          this.workflowNodes.every(
            (item: any) => item.activityCode !== this.activityCode,
          )
        ) {
          return false;
        }
      }

      if (!this.isWORKFLOW_ADMIN) {
        return true;
      }

      return this.workflowAdminManageScopes.includes(el.value);
    });
  }

  initScope() {
    let res: any = [
      {
        value: 'CANCELED_WORKFLOW',
        label: '作废流程',
      },
      {
        value: 'ADJUST_PARTICIPANT',
        label: '调整当前处理人',
      },
      {
        value: 'COMPLETED_WORKFLOW',
        label: '结束流程',
      },
      {
        value: 'CANCELED_ACTIVITY_WORKITEM',
        label: '取消当前节点所有任务',
      },
      {
        value: 'ACTIVATE_ACTIVITY',
        label: '激活其他节点',
      },
      {
        value: 'DELETE_WORKFLOW',
        label: '删除流程',
      },
      {
        value: 'MODIFY_OWNER',
        label: '修改拥有者',
      },
    ];
    const headerAction: any = this.$route.params.headerAction;
    if (this.$route.params.status === 'COMPLETED') {
      // 已完成: 调整当前处理人、取消当前节点所有任务、结束流程  操作不存在
      res = res.filter(
        (el) =>
          ![
            'ADJUST_PARTICIPANT',
            'CANCELED_ACTIVITY_WORKITEM',
            'COMPLETED_WORKFLOW',
          ].includes(el.value),
      );
    }
    if (this.$route.params.status === 'CANCELED') {
      // 已作废: 结束流程 不存在
      res = res.filter((el) => !['COMPLETED_WORKFLOW'].includes(el.value));
    }

    if (this.$route.params.status === 'EXCEPTION') {
      // 已作废: 结束流程 不存在
      res = res.filter(
        (el) => !['ADJUST_PARTICIPANT', 'MODIFY_OWNER'].includes(el.value),
      );
    }
    if (headerAction && !headerAction.showAdjust) {
      // 不允许调整节点
      res = res.filter(
        (el) =>
          ![
            'ADJUST_PARTICIPANT',
            'CANCELED_ACTIVITY_WORKITEM',
            'COMPLETED_WORKFLOW',
            'ACTIVATE_ACTIVITY',
          ].includes(el.value),
      );
    }

    if (
      (headerAction && !headerAction.showCancel) ||
      this.$route.params.status === 'CANCELED'
    ) {
      // 不允许作废/流程已作废 已作废不显示 取消节点任务、调整当前处理人
      res = res.filter(
        (el) =>
          ![
            'CANCELED_WORKFLOW',
            'ADJUST_PARTICIPANT',
            'CANCELED_ACTIVITY_WORKITEM',
          ].includes(el.value),
      );
    }

    if (headerAction && !headerAction.showRemove) {
      // 不允许删除
      res = res.filter((el) => !['DELETE_WORKFLOW'].includes(el.value));
    }

    if (headerAction && !headerAction.showEditOwner) {
      // 不允许修改拥有者
      res = res.filter((el) => !['MODIFY_OWNER'].includes(el.value));
    }

    this.options = res;
  }

  /**
   * 激活节点是预估处理人
   */
  async predictProcesser(activityCode) {
    return workflowApi.getActivityPreprocessors({
      workflowInstanceId: this.workflowInstanceId,
      activityCode: activityCode,
    });
  }

  /**
   * 激活其他节点选择节点
   */
  onActivityChange(activityCode) {
    this.predictProcesser(activityCode).then((res: any) => {
      if (res.errcode === 0) {
        this.nodeParticipants = res.data.map((item) => {
          return Object.assign(item, { type: 3, unitType: 3 });
        });
      } else {
        this.$message.error(res.errmsg);
      }
    });
  }

  processItem(item: any) {
    //计算超时时间
    if (item.workItemTimeoutStatus === 'TIMEOUT') {
      item.timeoutTimeStr = this.getTimerNumber(
        new Date().valueOf() - new Date(item.allowedTime).valueOf(),
      );
    }

    item.checked = false;

    return this.SetTimeType(item);
  }

  goBack() {
    this.noParticipantsListVisible = false;
  }

  goBackPage() {
    window.history.go(-1);
  }

  //表单内打开是query上，单独页面打开是params上
  get workflowInstanceId() {
    return (this.$route.query.workflowInstanceId ||
      this.$route.params.workflowInstanceId) as any;
  }
}
</script>
<style lang="less" scoped>
@import '~@/styles/mixins.less';
.devOps-wrapper {
  width: 100vw;
  height: 100vh;
  .px2rem(padding-bottom, 122px);
  background: #f7f7f7;
  .devOps-wrapper-header {
    .px2rem(padding-top, 8px);
    .px2rem(padding-bottom, 8px);
    .px2rem(padding-left, 24px);
    .px2rem(padding-right, 24px);
    position: relative;
    /deep/.h3think-nav-bar__left {
      .go-back-box .go-back {
        width: unset;
        .h-icon-all-bevel-bottom-stroke {
          color: #111218;
          display: inline-block;
          transform: rotate(180deg);
        }
      }
      .text {
        .px2rem(margin-left, 4px);
        .px2rem(height, 64px);
        .px2rem(font-size, 32px);
        .px2rem(line-height, 64px);
        font-weight: 400;
        color: #111218;
      }
    }
    /deep/.h3think-nav-bar__title {
      .px2rem(height, 72px);
      .px2rem(font-size, 36px);
      .px2rem(line-height, 72px);
      font-weight: 600;
      color: #111218;
    }
  }
  .devOps-wrapper-body {
    height: calc(100% - 44px);
    overflow: auto;
    &.noBar {
      height: 100%;
    }
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  }
  .cur-other {
    box-shadow: inset 0px -0.5px 0px 0px rgba(209, 211, 228, 0.65); // box-shadow: inset 0px -0.5px 0px 0px rgba(209,211,228,0.65);
  }
  .cur-approver-option {
    .px2rem(padding-bottom, 44px);
    /deep/.approval-text-opinion .h3think-textarea {
      .px2rem(height, 136px);
    }
  }
  .cur-file-upload {
    .px2rem(padding-right, 0);
    .px2rem(padding-bottom, 0);
    .px2rem(padding-left, 0);
    /deep/.h3think-field__label {
      .px2rem(padding-right, 32px);
      .px2rem(padding-bottom, 32px);
      .px2rem(padding-left, 32px);
    }
    /deep/.approval-upload {
      .h3think-upload__btn {
        .px2rem(top, -74px);
      }
      &.show-files {
        .px2rem(margin-top, -32px);
        .h3think-upload__btn {
          .px2rem(top, -42px);
        }
      }
    }
  }
  .batch-opt {
    /deep/.h3think-field__label {
      .px2rem(padding-bottom, 20px);
    }
    /deep/.batch-opt-box {
      position: relative;
      text-align: left;
      .batch-opt-switch {
        position: absolute;
        right: 0;
        .px2rem(height, 48px);
        .px2rem(top, -64px);
      }
      .batch-opt-tips {
        .px2rem(padding-right, 38px);
        .px2rem(line-height, 34px);
        .px2rem(font-size, 24px);
        font-weight: 400;
        .tips-title {
          display: block;
          color: #111218;
        }
        .tips-body {
          display: block;
          color: rgba(17, 18, 24, 0.5);
          .px2rem(margin-top, 12px);
        }
      }
      .batch-opt-data {
        .px2rem(margin-top, 54px);
        .px2rem(height, 40px);
        .px2rem(font-size, 28px);
        font-weight: 400;
        color: #315efb;
        .px2rem(line-height, 40px);
      }
    }
  }
}
.inner {
  background-color: #fff;
}

.choice-node {
  background-color: #fff;
  .choice-node-inner {
    .field-value-box {
      display: flex;
      text-align: left;
      .value-text {
        flex: 1;
        word-break: break-all;
        .px2rem(font-size, 30px);
        font-weight: 400;
        color: #111218;
        .px2rem(line-height, 44px);
        .px2rem(padding-right, 24px);
        .px2rem(height, 44px);
        overflow: hidden;
        &.expend {
          height: auto;
        }
      }
      .value-opts {
        .px2rem(height, 44px);
        .px2rem(font-size, 30px);
        font-weight: 400;
        color: #315efb;
        .px2rem(line-height, 44px);
      }
    }
  }
}

.border-top {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  .px2rem(height, 120px);
  .px2rem(padding-top, 16px);
  .px2rem(padding-bottom, 16px);
  .px2rem(padding-left, 32px);
  .px2rem(padding-right, 32px);
  background: #fff;
  box-shadow: 0px -1px 0px 0px #f0f0f0; // box-shadow: 0px -1px 0px 0px #F0F0F0;
  .border-top-btn {
    background-color: #315efb;
    border-color: #315efb;
    .px2rem(font-size, 32px);
  }
}
.is-batch {
  // .px2rem(margin-top, 24px);
  background: #fff;
}
.tips {
  .px2rem(font-size, 24px);
  color: #666;
  text-align: left;
  .px2rem(padding-top, 20px);
  .px2rem(padding-bottom, 20px);
  .px2rem(padding-left, 30px);
  .px2rem(padding-right, 30px);
  text-align: justify;
  p,
  h1 {
    padding: 0;
    margin: 0;
    .px2rem(line-height, 46px);
  }
}
.noParticipantsList {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  z-index: 999;
  background-color: #fff;
  display: flex;
  flex-direction: column;
  .header.h3think-nav-bar {
    .px2rem(padding-top, 0);
    .px2rem(padding-bottom, 0);
    .px2rem(padding-left, 32px);
    .px2rem(padding-right, 32px);
    /deep/.h3think-nav-bar__left {
      .px2rem(padding-top, 24px);
      .px2rem(padding-bottom, 24px);
    }
    /deep/.h3think-nav-bar__title {
      .px2rem(padding-top, 20px);
      .px2rem(padding-bottom, 16px);
      .px2rem(height, 88px);
      .px2rem(font-size, 34px);
      font-weight: 600;
      color: #121933;
      .px2rem(line-height, 52px);
    }
  }
  .list-wrapper {
    .px2rem(padding-top, 18px);
    .px2rem(padding-bottom, 18px);
    .px2rem(padding-left, 32px);
    .px2rem(padding-right, 32px);
    background: #f1f2f3;
    .list-item + .list-item {
      .px2rem(margin-top, 24px);
    }
    &.noBar {
      top: 0;
    }
    &::-webkit-scrollbar {
      width: 0;
      height: 0;
    }
  }
}
</style>
<style lang="less">
@import '~@/styles/mixins.less';
.devOps-wrapper .mescroll {
  background-color: #f7f8fc;
  .px2rem(top, 98px);
  bottom: 0;
}

.list-wrapper {
  position: absolute;
  background-color: #f7f8fc;
  width: 100%;
  .px2rem(top, 88px);
  bottom: 0;
  overflow-y: auto;
}

.devOps-wrapper .signatrue--row .row__file {
  padding: 14px 15px;
}
.to-batch .h3-list-line .h3-list-extra {
  flex: 0;
  height: 44px;
  min-width: 32px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
}
</style>
