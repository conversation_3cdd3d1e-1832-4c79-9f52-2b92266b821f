<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <div class="form-nodes-switch">
    <div class="form-nodes-switch-header" @click="swithShow">
      <span>{{ activeNodes }}</span>
      <em
        :class="[
          'icon aufontAll h-icon-all-bevel-bottom-stroke',
          showPanel ? 'up-icon' : 'down-icon',
        ]"
      ></em>
    </div>
    <div v-show="showPanel" v-transfer-dom class="form-nodes-switch-content">
      <ul class="form-nodes-switch-content-panel">
        <li
          v-for="(node, index) in nodes"
          :key="node.activityCode"
          :class="{ selected: node.selected }"
          @click="nodesSwitch(index)"
        >
          <span>{{ node.activityName }}</span>
          <em
            v-show="node.selected"
            class="icon aufontAll h-icon-all-check"
          ></em>
        </li>
      </ul>
      <div class="form-nodes-switch-content-mask"></div>
    </div>
  </div>
</template>

<script lang="ts">
import common from 'cloudpivot/common/mobile';
import { H3Avatar } from 'h3-mobile-vue';
import { Component, Prop, Vue } from 'vue-property-decorator';

@Component({
  name: 'form-nodes-switch',
  components: {
    H3Avatar,
  },
  directives: {
    TransferDom: common.directives.transferDom,
  },
})
export default class FormNodesSwitch extends Vue {
  @Prop() nodes!: any;

  showPanel = false;

  swithShow() {
    this.showPanel = !this.showPanel;
  }

  get activeNodes() {
    const theNode = this.nodes.find((res) => res.selected);
    if (theNode) {
      return theNode.activityName;
    }
    return '';
  }

  nodesSwitch(index: number) {
    if (this.nodes[index].selected) {
      return;
    }
    let theNode = '';
    this.nodes.forEach((res, idx) => {
      if (idx === index) {
        res.selected = true;
        theNode = res.activityCode;
      } else {
        res.selected = false;
      }
    });
    this.nodes = this.nodes.slice();
    this.swithShow();
    this.$emit('nodesSwitch', theNode);
  }
}
</script>
<style lang="less" scoped>
@import '~@/styles/mixins.less';
@import '~@/styles/mixins/hairline.less';
.form-nodes-switch {
  &-header {
    position: relative;
    text-align: left;
    background: #fff;
    display: flex;
    align-items: center;
    color: #111218;
    padding-left: 16px;
    padding-right: 16px;
    font-size: 14px;
    height: 36px;
    span {
      padding-right: 7px;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    }
    em {
      font-size: 14px;
      color: rgba(17, 18, 24, 0.5);
      &.up-icon {
        transform: rotate(-90deg);
      }
      &.down-icon {
        transform: rotate(90deg);
      }
    }
  }
  &-content {
    text-align: left;
    &-mask {
      position: fixed;
      z-index: 1001;
      background-color: rgba(0, 0, 0, 0.4);
      .px2rem(top, 90px);
      bottom: 0;
      width: 100%;
    }
    &-panel {
      position: fixed;
      z-index: 1002;
      .px2rem(top, 72px);
      padding-bottom: 8px;
      margin-bottom: 0;
      width: 100%;
      background: #fff;
      border-radius: 0 0 8px 8px;
      & > li {
        margin: 2px 8px;
        padding-left: 8px;
        padding-right: 8px;
        height: 36px;
        font-size: 14px;
        border-radius: 4px;
        color: #111218;
        display: flex;
        align-items: center;
        justify-content: space-between;
        span {
          white-space: nowrap;
          overflow: hidden;
          text-overflow: ellipsis;
        }
      }
      & > li.selected {
        background: #f2f6ff;
        font-weight: 600;
        color: #2970ff;
      }
    }
  }
}
</style>
