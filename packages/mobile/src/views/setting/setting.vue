<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <div class="setting">
    <div class="setting-item">
      <ul>
        <li>
          <div>
            <span class="text">{{ $t('languages.common.version') }}</span>
            <span class="text">V{{ version }}</span>
          </div>
          <div class="icon"></div>
        </li>
        <li @click="showToggleLang">
          <div>
            <span class="text">{{
              $t('languages.common.toggleLanguage')
            }}</span>
            <span class="text">{{ curLanguage }}</span>
          </div>
          <div class="icon">
            <i class="icon aufontAll h-icon-all-right-o"></i>
          </div>
        </li>
      </ul>
    </div>
    <H3Actionsheet
      v-model="showActionSheet"
      class="sheet-adjust"
      :menus="actionSheetMenus"
      showCancel
      :cancelText="cancelText"
      @on-click-menu="clickActionSheetMenu"
    />
  </div>
</template>
<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';

import { H3Actionsheet } from 'h3-mobile-vue';

import { utils } from 'cloudpivot/common';

import * as platform from 'cloudpivot-platform/platform';

@Component({
  name: 'Setting',
  components: {
    H3Actionsheet,
  },
})
export default class Setting extends Vue {
  showActionSheet: boolean = false;

  actionSheetMenus: Array<any> = [];

  cancelText: string = '取消';

  get curLanguage() {
    (window as any).pageTitle = `${this.$t('languages.common.settings')}`;
    platform.service.setTitle(`${this.$t('languages.common.settings')}`);
    switch (this.$i18n.locale) {
      case 'zh':
        return '中文';
      case 'en':
        return 'English';
      default:
        return '';
    }
  }

  get version() {
    return this.$store.state.config.systemVersion;
  }

  mounted() {
    this.setSheetMenus();
  }

  /*
   * 设置语言菜单国际化
   */
  setSheetMenus() {
    if (this.$i18n.locale === 'en') {
      this.cancelText = 'Cancel';
      this.actionSheetMenus = [
        {
          label: '中文',
          type: 'Default',
          value: 'zh',
        },
        {
          label: 'English',
          type: 'Default',
          value: 'en',
        },
      ];
    } else {
      this.cancelText = '取消';
      this.actionSheetMenus = [
        {
          label: '中文',
          type: 'Default',
          value: 'zh',
        },
        {
          label: 'English',
          type: 'Default',
          value: 'en',
        },
      ];
    }
  }

  /**
   * 切换语言
   */
  toggleLanguage(lang: string) {
    this.$i18n.locale = lang;
    localStorage.setItem('locale', this.$i18n.locale);
    this.setSheetMenus();
    this.$forceUpdate();
  }

  showToggleLang() {
    this.showActionSheet = true;
  }

  clickActionSheetMenu(value: any) {
    if (value === 'cancel') {
      return;
    }
    this.toggleLanguage(value);
  }

  @Watch('showActionSheet')
  onShowActionSheetChange(v: boolean) {
    if (!v) {
      setTimeout(() => {
        utils.Bus.$emit('toggleNavbar', !v);
      }, 100);
    } else {
      utils.Bus.$emit('toggleNavbar', !v);
    }
  }
}
</script>
<style lang="less">
@import '~cloudpivot/common/styles/mixins.less';
.setting {
  .setting-item {
    .px2rem(margin-top, 16px);
    ul {
      li {
        background: white;
        display: flex;
        justify-content: space-between;
        align-items: center;
        .px2rem(padding, 32px);
        div {
          span {
            .px2rem(font-size, 30px);
            display: inline-block;
            line-height: 1;
            &:first-of-type {
              .px2rem(width, 168px);
              text-align: left;
              .px2rem(font-size, 26px);
              .px2rem(line-height, 40px);
              font-weight: 400;
              color: rgba(17, 18, 24, 0.5);
            }
            &:last-of-type {
              .px2rem(margin-left, 24px);
              .px2rem(font-size, 30px);
              .px2rem(height, 44px);
              .px2rem(line-height, 44px);
              color: #111218;
            }
          }
        }
        .icon {
          color: #707481;
          .px2rem(font-size, 26px);
        }
      }
      li:first-child {
        border-bottom: 1px solid #f1f2f3;
      }
    }
  }
  .sheet-adjust {
    .h3ui-actionsheet__menu,
    .h3ui-actionsheet__action,
    .h3ui-actionsheet__cell {
      color: #111218;
      .px2rem(font-size, 32px);
      border-radius: 0;
    }
    .h3ui-actionsheet,
    .h3ui-actionsheet__menu {
      border-radius: 12px 12px 0px 0px;
    }
    .h3ui-actionsheet__cell {
      .px2rem(height, 112px);
      .px2rem(line-height, 112px);
    }
    .h3ui-actionsheet__cell:nth-child(2):before {
      border-top: 1px solid rgba(209, 211, 228, 0.65);
    }
    .h3ui-actionsheet__cell:before {
      height: 0 !important;
    }
    .h3ui-actionsheet__action {
      .px2rem(margin-top, 16px);
    }
    .h3ui-actionsheet-cancel-button-mask {
      background: #f1f2f3;
      border-top: none;
      top: -8px;
      .px2rem(height, 16px);
    }
  }
}
</style>
