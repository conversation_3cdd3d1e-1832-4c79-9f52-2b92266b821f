<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <MyFinishedWorkItem
    ref="workitem"
    :appCode="appCode"
    @showSearchBarChange="showSearchBarChange"
    @openForm="openForm"
  />
</template>
<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
import { State } from 'vuex-class';

import flowCenter from 'cloudpivot-flow/flow-center/mobile';
import OrgApi from 'cloudpivot/common/src/components/mobile/api/organization';
import { getUrlCode } from 'cloudpivot/common/src/utils/url';

@Component({
  name: 'workitems',
  components: {
    MyFinishedWorkItem: flowCenter.components.MyFinishedWorkItem,
  },
})
export default class FinishedWorkitems extends Vue {
  @State('appCode') appCode!: any;

  // @ts-ignore
  openApplicationPortal: boolean = window.config.openApplicationPortal || false;

  get token() {
    return window.localStorage.getItem('token');
  }

  async openForm(workitem: any) {
    let url = '';
    let host: string = '';
    if (this.openApplicationPortal && workitem.containerName) {
      let workflowInstanceId = workitem.instanceId;

      const res = await OrgApi.getContainerUrl({
        workflowInstanceId: workflowInstanceId,
      });
      if (res.errcode === 0) {
        host = res.data;
      }
    }

    url = `/form/detail?workitemId=${workitem.id}&workflowInstanceId=${
      workitem.instanceId
    }&return=${getUrlCode(this.$route.fullPath)}&workitemType=finishedWorkitem`;

    if (this.openApplicationPortal) {
      url = `${host}/mobile#` + url + '&T=' + this.token;
      window.open(url, '_self');
      return;
    }
    this.$router
      .push({
        path: url,
      })
      .catch((err: any) => {
        console.log(err);
      });
    // this.$router
    //   .push({
    //     name: 'form-detail',
    //     query: {
    //       workitemId: workitem.id,
    //       workflowInstanceId: workitem.instanceId,
    //       return: this.$route.fullPath,
    //       workitemType: 'finishedWorkitem',
    //     },
    //   })
    //   .catch((err: any) => {
    //     console.log(err);
    //   });
  }

  showSearchBarChange(value) {
    this.$emit('changeWrapShow', value);
  }

  modalWrapClick() {
    (this.$refs['workitem'] as any).modalWrapClick();
  }
}
</script>
<style lang="less" scoped></style>
