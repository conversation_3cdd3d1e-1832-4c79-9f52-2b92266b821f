<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <my-unfinished-workItem
    ref="workitem"
    :appCode="appCode"
    @showSearchBarChange="showSearchBarChange"
    @toggle="toggle"
    @openForm="openForm"
  />
</template>
<script lang="ts">
import flowCenter from 'cloudpivot-flow/flow-center/mobile';
import { Component, Vue } from 'vue-property-decorator';
import { State } from 'vuex-class';
import OrgApi from 'cloudpivot/common/src/components/mobile/api/organization';
import { getUrlCode } from 'cloudpivot/common/src/utils/url';

@Component({
  name: 'workitems',
  components: {
    MyUnfinishedWorkItem: flowCenter.components.MyUnFinishedWorkItem,
  },
})
export default class Workitems extends Vue {
  @State('appCode') appCode!: any;

  // @ts-ignore
  openApplicationPortal: boolean = window.config.openApplicationPortal || false;

  get token() {
    return window.localStorage.getItem('token');
  }

  async openForm(workitem: any) {
    let url = '';
    let host: string = '';
    if (this.openApplicationPortal && workitem.containerName) {
      let workflowInstanceId = workitem.instanceId;

      const res = await OrgApi.getContainerUrl({
        workflowInstanceId: workflowInstanceId,
      });
      if (res.errcode === 0) {
        host = res.data;
      }
    }

    url = `/form/detail?workitemId=${workitem.id}&workflowInstanceId=${
      workitem.instanceId
    }&return=${getUrlCode(
      this.$route.fullPath,
    )}&workitemType=unfinishedWorkitem`;

    if (this.openApplicationPortal) {
      url = `${host}/mobile#` + url + '&T=' + this.token;
      window.open(url, '_self');
      return;
    }
    this.$router
      .push({
        path: url,
      })
      .catch((err: any) => {
        console.log(err);
      });
    // this.$router
    //   .push({
    //     name: 'form-detail',
    //     query: {
    //       workitemId: workitem.id,
    //       workflowInstanceId: workitem.instanceId,
    //       return: this.$route.fullPath,
    //       workitemType: 'unfinishedWorkitem',
    //     },
    //   })
    //   .catch((err: any) => {
    //     console.log(err);
    //   });
  }

  toggle(value) {
    this.$emit('toggle', value);
  }

  showSearchBarChange(value) {
    this.$emit('changeWrapShow', value);
  }

  modalWrapClick() {
    (this.$refs['workitem'] as any).modalWrapClick();
  }
}
</script>
<style lang="less" scoped></style>
