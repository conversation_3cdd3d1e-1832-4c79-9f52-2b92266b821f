<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <div
    class="home"
    :class="{
      'modal-wrap': modalWrap,
    }"
  >
    <div class="modal-wrapper" @click.prevent="modalWrapClick"></div>
    <ul class="home-nav">
      <router-link
        v-for="(router, index) in routerList"
        :key="index"
        tag="li"
        :to="{ name: router.code }"
      >
        <span
          class="home-nav-router"
          :class="{ 'home-nav-active': router.code === CurrentTag }"
        >
          {{ router.name }}
        </span>
      </router-link>
    </ul>
    <div class="home-router">
      <router-view
        ref="router-view"
        @toggle="toggleButtonShow"
        @changeWrapShow="changeWrapShow"
      />
    </div>
    <div
      v-show="showAddButton && !appCode"
      class="home-add"
      @click="gotoInstance"
    >
      <img src="@/assets/images/add.svg" />
    </div>
  </div>
</template>

<script lang="ts">
import common from 'cloudpivot/common/mobile';
import { Component, Vue, Watch } from 'vue-property-decorator';
import { namespace, State } from 'vuex-class';

@Component({
  name: 'home',
  components: {
    Empty: common.components.Empty,
  },
})
export default class Home extends Vue {
  @State('appCode') appCode!: any;

  routerList: Array<any> = [];

  showAddButton: boolean = true;

  //是否显示遮罩
  modalWrap: boolean = false;

  get CurrentTag() {
    const { name } = this.$route;
    if (name) {
      return name;
    }
    return this.routerList[0].code;
  }

  @Watch('CurrentTag')
  onCurrentTagChange() {
    this.changeWrapShow(false);
  }

  changeWrapShow(value) {
    this.modalWrap = value;
  }

  /**
   * 切换当前悬浮按钮显隐
   */
  toggleButtonShow(show: boolean) {
    this.showAddButton = !show;
  }

  /**
   * 跳转到发起流程
   */
  gotoInstance() {
    this.$router.push({ name: 'initial-instance' }).catch((err: any) => {
      console.log(err);
    });
  }

  mounted() {
    localStorage.removeItem('isShowEmailResquest');
    this.routerList = [
      {
        name: this.$t('languages.common.todo'),
        code: 'workitems',
      },
      {
        name: this.$t('languages.common.done'),
        code: 'finished-workitems',
      },
      {
        name: this.$t('languages.common.circulate'),
        code: 'circulates',
      },
      {
        name: this.$t('languages.common.myWorkflow'),
        code: 'my-workflow',
      },
    ];
  }

  //点击遮罩时间处理
  modalWrapClick() {
    if (this.modalWrap) {
      this.modalWrap = false;
      (this.$refs['router-view'] as any).modalWrapClick();
    }
  }
}
</script>
<style lang="less" scoped>
@import '~cloudpivot/common/styles/mixins.less';
@import '~cloudpivot/common/styles/mobile/themes/default.less';

@nav-height: 88px;
@add-btn-size: 96px;
@btn-padding-bottom: 40px;
@count-width: 44px;
@count-height: 34px;
@marign-left: 14px;
.home {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
  &.modal-wrap {
    .modal-wrapper {
      &::after {
        content: '';
        width: 100vw;
        position: fixed;
        left: 0;
        background: rgba(0, 0, 0, 0.5);
        z-index: 11;
        .px2rem(top, 176px);
        .calcAttr(height, 88);
      }
    }
    /deep/.workflow-list .content-data {
      overflow-y: hidden !important;
    }
  }
  &-nav {
    display: flex;
    flex: none;
    margin-bottom: 0 !important;
    background: @white-background;
    position: relative;
    z-index: 2;
    box-shadow: inset 0 -0.5px 0 0 rgba(209, 211, 228, 0.48);
    .backgroundline('bottom');
    &::before {
      content: '';
      width: 100%;
      position: absolute;
      top: 0;
      background: rgba(209, 211, 228, 0.65);
      .px2rem(height, 1px);
    }
    li {
      flex: 1;
      display: flex;
      align-items: center;
      justify-content: center;
      .home-nav-router {
        display: inline-block;
        .px2rem(height,@nav-height);
        .px2rem(line-height,@nav-height);
        .px2rem(font-size,28px);
        font-weight: 400;
        color: @first-level-black;
      }
      .home-nav-active {
        position: relative;
        color: @primary-color;
        font-weight: bold;
        &:after {
          content: '';
          position: absolute;
          bottom: 0;
          left: 0;
          width: 100%;
          height: 0;
          border-bottom: 2px solid @primary-color;
          border-radius: 2px;
        }
      }
      .home-nav-count {
        display: flex;
        align-items: center;
        justify-content: center;
        .px2rem(margin-left,10px);
        .px2rem(padding-left, 10px);
        .px2rem(padding-right, 10px);
        .px2rem(min-width,@count-width);
        .px2rem(height,@count-height);
        // border-radius: 42%;
        .px2rem(border-radius, 17px);
        background-color: #f4454e;
        box-sizing: border-box;
        overflow: hidden;
        span {
          display: inline-block;
          vertical-align: middle;
          color: white;
        }
      }
      .radius-tip {
        .px2rem(width,34px);
        .px2rem(height,34px);
        border-radius: 50%;
      }
    }
  }
  &-router {
    flex: 1;
    .calcAttr(height, 44);
  }
  &-add {
    position: fixed;
    z-index: 9;
    .px2rem(right, 48px);
    .px2rem(bottom, 168px);
    img {
      .px2rem(height,@add-btn-size);
      .px2rem(width,@add-btn-size);
    }
  }
}
</style>
