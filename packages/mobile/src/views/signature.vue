<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <h3-signature
    v-show="!uploaded"
    ref="signature"
    :clip="true"
    :maxWidth="1"
    :maxHeight="1"
    :onBegin="onBegin"
    @saveAsPng="saveAsPng"
  />
</template>

<script lang="ts">
import ControlBack from 'cloudpivot-form/form/src/renderer/directives/control-back';
import { H3Signature } from 'cloudpivot-mobile-vue';
import { H3Button } from '@h3/thinking-ui';
import { Component, Vue } from 'vue-property-decorator';
import { DefaultFileService } from '../config/h3-form/file-service';

const IS_WECHAT =
  /WeChat/.test(navigator.userAgent) ||
  /wxwork/.test(navigator.userAgent) ||
  /MicroMessenger/.test(navigator.userAgent);
const IS_DINGTALK = /DingTalk/.test(navigator.userAgent);

@Component({
  name: 'signatrue2',
  components: {
    H3Signature,
    H3Button,
  },
  directives: {
    ControlBack,
  },
})
export default class Signatrue2 extends Vue {
  // signatrueHeight: number = document.body.offsetHeight - 50;

  // signatrueWidth: number = document.body.offsetWidth;

  isRest: boolean = true;

  signature: any = {};

  img: any = '';

  uploaded: boolean = false; // 已经上传成功

  reset() {
    this.signature.clear();
    this.isRest = true;
  }

  onBegin() {
    this.isRest = false;
  }

  ok() {
    this.signature.saveAsPng();
  }

  refId: string = '';

  oldToken: string = '';

  isNotWechatOrDingtalk: boolean = false;

  created() {
    if (!IS_WECHAT && !IS_DINGTALK) {
      this.$message.success('请使用微信、企业微信或者钉钉扫码签名');
      this.isNotWechatOrDingtalk = true;
      return;
    }
    this.oldToken = localStorage.getItem('token') || '';
    const token: string = (this.$route.query.T as string) || '';
    if (this.$route.query.T) {
      localStorage.setItem('token', token);
    }
    this.refId = (this.$route.query.refId as string) || '';
  }

  mounted() {
    this.signature = this.$refs.signature;
  }

  destroyed() {
    if (this.oldToken) {
      localStorage.setItem('token', this.oldToken);
    }
  }

  saveAsPng(data: any) {
    this.img = data;
    this.uploadImg();
  }

  uploadImg() {
    this.upLoad(this.img);
    // this.reset();
  }

  upLoad(file: any) {
    const closeLoading = (this.$message as any).loading();
    new DefaultFileService().upFile(file, this.refId).then((res: any) => {
      if (res.errcode === 0) {
        this.uploaded = true;
        closeLoading();
        this.$message.success('上传签名成功，请返回PC端查看');
      } else {
        closeLoading();
      }
    });
  }

  get $message() {
    return {
      loading: (msg?: string) => {
        this.showLoading(msg || '');
        return this.hideToast;
      },
      success: (msg?: string) => {
        this.showSuccess(msg || '');
      },
    } as any;
  }

  showSuccess(text: string) {
    this.$h3.toast.show({
      text,
      autoHide: false,
      iconType: '',
    });
  }

  showLoading(text: string) {
    this.$h3.toast.show({
      text,
      autoHide: false,
      iconType: 'loading',
    });
  }

  hideToast() {
    this.$h3.toast.hide();
  }
}
</script>

<style lang="less" scoped>
.signatrue-panel {
  display: flex;
  flex-flow: column;
  height: 100%;
  .signatrue-panel__content {
    flex: 1;
    max-height: calc(100% - 60px);
    > canvas {
      height: 100%;
    }
  }
}
</style>
