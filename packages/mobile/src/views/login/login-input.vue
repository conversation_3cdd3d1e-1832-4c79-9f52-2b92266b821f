<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <div class="login-input">
    <p v-show="showTitle" class="login-input-label">
      {{ lable }}
    </p>
    <input
      v-model="val"
      :type="inputType"
      :placeholder="placeholder"
      autocomplete="off"
      @focus="focus"
      @blur="blur"
      @change="chang"
    />
    <span
      v-if="type === 'password'"
      class="icon aufontAll icon-eye"
      :class="{
        'h-icon-all-eye-close': !showPassWord,
        'h-icon-all-eye-o': showPassWord,
      }"
      @click="switchType"
    ></span>
    <span
      v-show="val && showTitle"
      class="clear icon aufontAll icon-clear h-icon-all-close-circle"
      :class="{
        'password-clear': type === 'password',
        'send-clear': canSendMsg,
        'remaining-clear': sendingMessage,
      }"
      @click="clearValue"
    ></span>
    <div v-if="type === 'verification'" class="send-verification">
      <span v-if="!canSendMsg && !sendingMessage" class="disable">发送验证码</span>
      <span
        v-else-if="canSendMsg && !sendingMessage"
        class="enable"
        @click="sendMsg"
        >发送验证码</span>
      <span v-else class="disable">{{ remainingTime }}s</span>
    </div>
  </div>
</template>
<script lang="ts">
enum inputType {
  text = 'text',

  password = 'password',

  verification = 'verification',
}

import { Component, Model, Prop, Vue, Watch } from 'vue-property-decorator';

@Component({
  name: 'login-input',
  components: {},
})
export default class LoginInput extends Vue {
  @Prop({
    default: '',
  })
  placeholder!: boolean;

  @Model('change')
  val!: string;

  @Prop({
    default: '',
  })
  lable!: string;

  @Prop({
    default: inputType.text,
  })
  type!: inputType;

  @Prop({
    default: false,
  })
  canSendMsg?: boolean;

  @Prop({
    default: false,
  })
  sendingMessage?: boolean;

  showTitle = false;

  showPassWord = false;

  inputType = inputType.text;

  remainingTime: number = 0; // 验证码冷却时间

  @Watch('sendingMessage')
  sendingMsg(val) {
    if (val) {
      this.remainingTime = 60;
      const sendMessageInterval = setInterval(() => {
        this.remainingTime--;
        if (!this.remainingTime) {
          this.sendingMessage = false;
          clearInterval(sendMessageInterval);
        }
      }, 1000);
    }
  }

  sendMsg() {
    this.$emit('sendMsg');
  }

  created() {
    this.inputType = this.type;
  }

  focus() {
    this.showTitle = true;
  }

  blur() {
    this.showTitle = false;
  }

  chang(e: any) {
    this.$emit('change', e.target.value);
  }

  switchType() {
    this.showPassWord = !this.showPassWord;
    this.inputType = this.showPassWord ? inputType.text : inputType.password;
  }

  clearValue() {
    this.$emit('change', '');
    // this.val = '';
  }
}
</script>
<style lang="less" scoped>
.login-input {
  position: relative;
  text-align: left;
  .login-input-label {
    position: absolute;
    top: 0;
    height: 18px;
    font-size: 13px;
    font-weight: 400;
    color: #2970ff;
    line-height: 18px;
  }
  input {
    width: 100%;
    border-bottom: 0.5px solid rgba(209, 211, 228, 0.65);
    background-color: inherit;
    padding: 24px 0 6px;
    font-size: 15px;
    font-weight: 400;
    color: #111218;
    line-height: 22px;
    &::-webkit-input-placeholder {
      height: 22px;
      font-size: 15px;
      font-weight: 400;
      color: rgba(17, 18, 24, 0.4);
      line-height: 22px;
    }
  }
  .icon-eye {
    position: absolute;
    right: 0;
    top: 27px;
    color: rgba(17, 18, 24, 0.5);
    display: inline-block;
    width: 16px;
    height: 16px;
    font-size: 16px;
    line-height: 16px;
  }
  .icon-clear {
    position: absolute;
    right: 0;
    top: 27px;
    color: rgba(17, 18, 24, 0.5);
    display: inline-block;
    width: 16px;
    height: 16px;
    font-size: 16px;
    line-height: 16px;
    &.password-clear {
      right: 32px;
    }
    &.send-clear {
      right: 81px;
    }
    &.remaining-clear {
      right: 37px;
    }
  }
  .send-verification {
    position: absolute;
    right: 0;
    top: 27px;
    height: 17px;
    font-size: 13px;
    font-weight: 400;
    color: #2970ff;
    line-height: 17px;
    .disable {
      color: rgba(17, 18, 24, 0.25);
    }
    .enable {
      color: #2970ff;
    }
  }
}
</style>
