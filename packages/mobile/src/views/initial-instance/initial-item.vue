<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <span>
    <form-empty v-if="showEmpty" :isClose="true" />
    <initial-item v-else />
  </span>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';

import flowCenter from 'cloudpivot-flow/flow-center/mobile';

import { State } from 'vuex-class';

import * as platform from 'cloudpivot-platform/platform';

import FormEmpty from '../form/empty.vue';

@Component({
  name: 'initial-items',
  components: {
    InitialItem: flowCenter.components.InitialItem,
    FormEmpty,
  },
})
export default class InitialItems extends Vue {
  @State('appName') appName!: any;

  appCode: string = '';

  showEmpty: boolean = false;

  created() {
    this.appCode = this.$route.params.appCode;
    if (this.appCode === 'ben' && !this.appName) {
      this.showEmpty = true;
    }
    (window as any).pageTitle = this.appName;
    platform.service.setTitle(this.appName);
  }
}
</script>

<style lang="less" scoped></style>
