<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->
<template>
  <div class="nav-viewer" :class="{ 'nav-viewer-ios-wx': isIOSWX }">
    <div :class="['nav-viewer__box', { normal: !showNav }]">
      <router-view class="nav-viewer__wrap" />
    </div>

    <div v-show="showNav" class="nav-viewer__bar">
      <router-link
        v-for="(item, index) in menu_config"
        :key="index"
        :to="item.path"
        tag="div"
      >
        <span>
          <em class="icon aufontAll" :class="item.icon"></em>
        </span>
        <p>{{ item.title }}</p>
      </router-link>
    </div>
  </div>
</template>

<script lang="ts">
import { Component, Vue, Watch } from 'vue-property-decorator';
import { utils } from 'cloudpivot/common';
import { Route } from 'vue-router';

@Component({
  name: 'NavViewer',
})
export default class NavViewer extends Vue {
  // 是否展示底部导航栏
  showNav: boolean = true;

  // ios企业微信
  isIOSWX: boolean = false;

  mounted() {
    utils.Bus.$on('toggleNavbar', (val: boolean) => {
      this.showNav = val;
    });
  }

  beforeDestroy() {
    utils.Bus.$off('toggleNavbar');
  }

  created() {
    const ua: string = navigator.userAgent;
    this.isIOSWX = ua.indexOf('iPhone') !== -1 && ua.indexOf('wxwork') !== -1;
  }

  get menu_config() {
    return [
      {
        path: '/home',
        icon: 'h-icon-all-home-stroke',
        title: this.$t('languages.common.homePage') as string,
      },
      {
        path: '/my-instances',
        icon: 'h-icon-all-team-single-stroke',
        title: this.$t('languages.common.workflows') as string,
      },
      {
        path: '/apps',
        icon: 'h-icon-all-a-app-store-stroke1',
        title: this.$t('languages.common.apps') as string,
      },
      {
        path: '/setting',
        icon: 'h-icon-all-setting-stroke',
        title: this.$t('languages.common.settings') as string,
      },
    ];
  }

  @Watch('$route', {
    immediate: true,
  })
  routeChange(route: Route) {
    //应用分组页面不显示底部tab导航
    if (route && route.name === 'app-item') {
      this.showNav = false;
    }
  }
}
</script>

<style lang="less">
@import '~@/styles/index.less';
@foot-height: 120px;
.nav-viewer {
  display: flex;
  flex-direction: column;
  height: 100%;
  box-sizing: border-box;
  // background: @main-background;
  background: #f1f2f3;
  &.nav-viewer-ios-wx {
    &__wrap {
      position: fixed;
      .px2rem(bottom, @foot-height);
      top: 0;
      left: 0;
      right: 0;
    }
    &__box {
      padding-bottom: 0;
    }
  }
  &__box {
    flex: 1;
    overflow: hidden;
    .calcAttr(max-height, 60);
    &.normal {
      padding-bottom: 0;
      z-index: 11;
      max-height: 100%;
    }
  }
  &__wrap {
    height: inherit;
    overflow-y: auto;
  }
  // todo ios flex布局在iOS上点击透传兼容问题，目前用absolute避免；
  .nav-viewer__bar {
    position: absolute;
    bottom: 0;
    display: flex;
    align-items: center;
    width: 100%;
    .px2rem(height, @foot-height);
    .px2rem(padding-top, 20px);
    .px2rem(padding-bottom, 24px);
    font-weight: 400;
    background: @white-background;
    z-index: 10;
    box-shadow: inset 0px 0.5px 0px 0px rgba(209, 211, 228, 0.65);
    // .backgroundline('top');
    & > div > p {
      margin: 0;
      transform: scale(0.5);
      .px2rem(line-height, 28px);
      .px2rem(font-size, 40px) !important;
    }
    div {
      // .px2rem(padding-top, @vertical-padding-md);
      // .px2rem(padding-bottom, @vertical-padding-md);
      flex: 1;
      display: block;
      text-align: center;
      // color: @second-level-black;
      color: rgba(17, 18, 24, 0.5);
    }
    span {
      display: inline-block;
      .px2rem(min-height, @line-height-xxs);
    }
    em {
      .px2rem(font-size, 48px);
    }
    .router-link-active {
      color: @primary-color;
    }
  }

  // 屏蔽因移动端首页引入单独图表组件引发的before样式影响
  .home:before,
  .setting:before {
    content: '';
  }
}
</style>
