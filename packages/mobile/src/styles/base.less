/*
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
*/
@import './mixins.less';
@import './themes/default.less';
/*css 初始化 */

html,
body,
ul,
li,
ol,
dl,
dd,
dt,
p,
h1,
h2,
h3,
h4,
h5,
h6,
form,
fieldset,
legend,
img {
  margin: 0;
  padding: 0;
}

fieldset,
img,
input,
button {
  /*fieldset组合表单中的相关元素*/
  border: none;
  padding: 0;
  margin: 0;
  outline-style: none;
}

ul, ol {
  list-style: none;               /*清除列表风格*/
}

.editor-htm{
  ul, ol {
      list-style: revert;            /*清除列表风格*/
      padding-left: 1.5em;
      li{
        list-style-type: revert;
      }
  }
  
}

input {
  padding-top: 0;
  padding-bottom: 0;
  font-family: "SimSun", "宋体";
}

select,
input {
  vertical-align: middle;
}

select,
input,
textarea {
  margin: 0;
}

textarea {
  resize: none;
}

/*防止多行文本框拖动*/

img {
  border: 0;
  vertical-align: middle;
}

/*  去掉图片低测默认的3像素空白缝隙*/

table {
  border-collapse: collapse;
  /*合并外边线*/
}

body {
  .px2rem(font-size, @font-size-base);
  font-family: "Helvetica Neue For Number", -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", "Helvetica Neue", Helvetica, Arial, sans-serif;
  background: #f7f8fc!important;
  height: 100%;
  color: rgba(102, 102, 102, 1);
  overflow: hidden;
  line-height: 1.15 !important;
}

.clearfix:before,
.clearfix:after {
  content: "";
  display: table;
}

.clearfix:after {
  clear: both;
}

.clearfix {
  *zoom: 1;
  /*IE/7/6*/
}

a {
  color: rgba(102, 102, 102, 1);
  text-decoration: none;
}

a:active {
  text-decoration: none;
}

h1,
h2,
h3,
h4,
h5,
h6 {
  text-decoration: none;
  font-weight: normal;
  font-size: 100%;
}

i,
em {
  // font-style: normal;
  font-style: italic;
  text-decoration: none;
}
