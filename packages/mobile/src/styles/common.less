/*
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
*/
.flex-center {
  display: flex;
  justify-content: center;
  align-items: center;
}

.flex-justify-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.divider-vertical {
  width: 0;
  height: 100%;
  border-left: 1px solid #fff;
}

.text-ellipsis {
  display: -webkit-box;
  -webkit-line-clamp: 1;
  /* autoprefixer: ignore next */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.cursor-pointer {
  cursor: pointer !important;
}

::-webkit-scrollbar {
  width: 4px;
  height: 4px;
  cursor: pointer !important;
}

::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  border-radius: 5px;
  -webkit-box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.45);
  background: rgba(0, 0, 0, 0.45);
}

::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  // -webkit-box-shadow: inset 0 0 5px rgba(0,0,0,0.2);
  border-radius: 0;
  background: #fff;
}

// 垂直手风琴
/*
    * 使用范例：
    <transition name="collapse">
        <div class="collapse-item" v-show="x">
            {{content}}
        </div>
    </transition>
*/
.collapse-item {
  overflow: hidden;
}

.collapse-enter-active {
  max-height: 375px;
  transition: all .35s ease-in;
}

.collapse-leave-active {
  max-height: 375px;
  transition: all .35s ease-out;
}

.collapse-enter {
  max-height: 0;
}

.collapse-leave-to {
  max-height: 0;
}

// 淡入淡出
.fade-enter-active {
  margin-left: -100%;
  transition: all .25s ease-in;
}

.fade-leave-active {
  margin-right: -100%;
  transition: all .25s ease-in;
}

.h3-input-number {
  border: none !important;
  border-radius: 0 !important;

  &-steps {
    display: none !important;
  }

  &>input {
    padding: 0 !important;
  }
}

.h3-modal-container,
.v-transfer-dom .h3-popup-show{
  z-index: 9999 !important
}

.h3-popup-mask.h3-popup-show{
  z-index: 9998 !important
}

.ant-tooltip{
  font-size: 13px !important;
  line-height: 32px !important;
}

.h3-toast-notice-content{
  .h3-toast-text{
    max-height: 100px;
    .h3-toast-text-info{
      -webkit-line-clamp: 3;
    }
  }
}

.ant-input[disabled]{
  color: rgba(0, 0, 0, 0.45);
}
.ant-btn-disabled, .ant-btn.disabled, .ant-btn[disabled], .ant-btn-disabled:hover, .ant-btn.disabled:hover, .ant-btn[disabled]:hover, .ant-btn-disabled:focus, .ant-btn.disabled:focus, .ant-btn[disabled]:focus, .ant-btn-disabled:active, .ant-btn.disabled:active, .ant-btn[disabled]:active, .ant-btn-disabled.active, .ant-btn.disabled.active, .ant-btn[disabled].active{
  color: rgba(0, 0, 0, 0.45);
}


.h3-modal-transparent {
  width: 280px !important;

  .h3-modal-content.has-no-title {
    padding-top: 22px !important;
    .h3-modal-body {
      padding: 0 24px 18px !important;
      .h3-modal-alert-content {
        color: #111218;
      }
    }
    .h3-modal-button {
      font-size: 16px;
    }
  }

}