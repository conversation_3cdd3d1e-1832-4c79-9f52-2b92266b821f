/*
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
*/
@import "../themes/default";

.scale-hairline-common(@color, @top, @right, @bottom, @left) {
  content: '';
  position: absolute;
  background-color: @color;
  display: block;
  z-index: 1;
  top: @top;
  right: @right;
  bottom: @bottom;
  left: @left;
}

.hairline(@direction, @color: @border-color-base) when (@direction = 'top') {
  border-top: 1PX solid @color;

  html:not([data-scale]) & {
    @media (min-resolution: 2dppx) {
      border-top: none;

      &::before {
        .scale-hairline-common(@color, 0, auto, auto, 0);
        width: 100%;
        height: 1PX;
        transform-origin: 50% 50%;
        transform: scaleY(0.5);

        @media (min-resolution: 3dppx) {
          transform: scaleY(0.33);
        }
      }
    }
  }
}

.hairline(@direction, @color: @border-color-base) when (@direction = 'right') {
  border-right: 1PX solid @color;

  html:not([data-scale]) & {
    @media (min-resolution: 2dppx) {
      border-right: none;

      &::after {
        .scale-hairline-common(@color, 0, 0, auto, auto);
        width: 1PX;
        height: 100%;
        background: @color;
        transform-origin: 100% 50%;
        transform: scaleX(0.5);

        @media (min-resolution: 3dppx) {
          transform: scaleX(0.33);
        }
      }
    }
  }
}

.hairline(@direction, @color: @border-color-base) when (@direction = 'bottom') {
  border-bottom: 1PX solid @color;

  html:not([data-scale]) & {
    @media (min-resolution: 2dppx) {
      border-bottom: none;

      &::after {
        .scale-hairline-common(@color, auto, auto, 0, 0);
        width: 100%;
        height: 1PX;
        transform-origin: 50% 100%;
        transform: scaleY(0.5);

        @media (min-resolution: 3dppx) {
          transform: scaleY(0.33);
        }
      }
    }
  }
}

.hairline(@direction, @color: @border-color-base) when (@direction = 'left') {
  border-left: 1PX solid @color;

  html:not([data-scale]) & {
    @media (min-resolution: 2dppx) {
      border-left: none;

      &::before {
        .scale-hairline-common(@color, 0, auto, auto, 0);
        width: 1PX;
        height: 100%;
        transform-origin: 100% 50%;
        transform: scaleX(0.5);

        @media (min-resolution: 3dppx) {
          transform: scaleX(0.33);
        }
      }
    }
  }
}

.hairline(@direction, @color: @border-color-base, @radius: 0) when (@direction = 'all') {
  border: 1PX solid @color;
  border-radius: @radius;

  html:not([data-scale]) & {
    @media (min-resolution: 2dppx) {
      position: relative;
      border: none;

      &::before {
        content: '';
        position: absolute;
        left: 0;
        top: 0;
        width: 200%;
        height: 200%;
        border: 1PX solid @color;
        border-radius: @radius * 2;
        transform-origin: 0 0;
        transform: scale(0.5);
        box-sizing: border-box;
        pointer-events: none;

        // @media (min-resolution: 3dppx) {
        //   width: 300%;
        //   height: 300%;
        //   border-radius: @radius * 3;
        //   transform: scale(0.33);
        // }
      }
    }
  }
}

.hairline-remove(@position) when (@position = 'left') {
  border-left: 0;
  &:before {
    display: none !important;
  }
}

.hairline-remove(@position) when (@position = 'right') {
  border-right: 0;
  &:after {
    display: none !important;
  }
}

.hairline-remove(@position) when (@position = 'top') {
  border-top: 0;
  &:before {
    display: none !important;
  }
}

.hairline-remove(@position) when (@position = 'bottom') {
  border-bottom: 0;
  &:after {
    display: none !important;
  }
}

.hairline-remove(@position) when (@position = 'all') {
  border: 0;
  &:before {
    display: none !important;
  }
}

.backgroundline(@direction, @color: #E6EBF6) when (@direction = 'top') {
  background-image: linear-gradient(180deg, @color, @color 50%, transparent 50%),
    linear-gradient(270deg, @color, @color 50%, transparent 50%),
    linear-gradient(0deg, @color, @color 50%, transparent 50%),
    linear-gradient(90deg, @color, @color 50%, transparent 50%);
  background-size: 100% 1px, 0px 100%, 100% 0px, 0px 100%;
  background-repeat: no-repeat;
  background-position: top, right top, bottom, left top;
}

.backgroundline(@direction, @color: #E6EBF6) when (@direction = 'bottom') {
  background-image: linear-gradient(180deg, @color, @color 50%, transparent 50%),
    linear-gradient(270deg, @color, @color 50%, transparent 50%),
    linear-gradient(0deg, @color, @color 50%, transparent 50%),
    linear-gradient(90deg, @color, @color 50%, transparent 50%);
  background-size: 100% 0px, 0px 100%, 100% 1px, 0px 100%;
  background-repeat: no-repeat;
  background-position: top, right top, bottom, left top;
}
