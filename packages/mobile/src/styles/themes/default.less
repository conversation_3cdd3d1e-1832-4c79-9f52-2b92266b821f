/*
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
*/
// The prefix to use on all css classes from bpm.
@prefix : bpm;

// -------- Colors -----------
//主题色
@primary-color : #2970FF; 
@primary-color-hover: #1852D9;

//辅助色
@success-color : #00C293;
@error-color : #F0353F;
@warning-color : #FAAD14;

//中性色
@first-level-black: #111218;
@second-level-black: #88888B;
@third-level-black:#C3C3C5;

//背景色
@background-color: #F1F2F3;

//分割线，边框线
@border-color: #E1E2ED;

//标签、选项颜色（底色/边框色）
@info-background: #F0F7FF;
@info-border: #A3CBFF;
@info-error-background: #FFF1F0;
@info-error-border: #FFB6B3;
@info-warning-background: #FFFBE6;
@info-warning-border: #FFE58F;
@info-warning-background: #E6FFED;
@info-warning-border: #7AE69F;
@tag-gray-background: #F5F6F9;
@tag-gray-border: #E8EAF1;

// -------- 阴影，圆角 -----------

//投影
@card-shadow: 0 0 2px 0 #DEDFE5;
@button-shadow-bottom: 0 2px 12px 0 rgba(17,18,24,0.1);
@button-shadow-top: 0 -2px 12px 0 rgba(17,18,24,0.1);

//圆角
@border-radius-base: 8px;
@border-radius-sm: 4px;


@error-bg : #fff5f5;
@highlight-color : @primary-color;
@normal-color : #d9d9d9;

// Background color & border color
@main-background : #f7f8fc;

@white-background : #fff;
@base-border-color : #E6EBF6; // Used on @white-background

// avator color && applaction color
@random-color-1 : #1CC972;
@random-color-2 : #FFA228;
@random-color-3 : #606BFF;
@random-color-4 : #19A7FB;
@random-color-5 : #FF6851;


// font-family desc
@font-family : "Chinese Quote",
-apple-system,
BlinkMacSystemFont,
"Segoe UI",
"PingFang SC",
"Hiragino Sans GB",
"Microsoft YaHei",
"Helvetica Neue",
Helvetica,
Arial,
sans-serif,
"Apple Color Emoji",
"Segoe UI Emoji",
"Segoe UI Symbol";
@code-family : "SFMono-Regular",
Consolas,
"Liberation Mono",
Menlo,
Courier,
monospace;

// font-color 
@text-color-main : #111218;
@text-color-secondary : rgba(17, 18, 24, 0.5);
@text-color-describe : rgba(17, 18, 24, 0.5);
@text-color-disabled : rgba(17, 18, 24, 0.25);
@text-color-white : #fff;

// font-size
@font-size-xxl : 48px;
@font-size-xl : 36px;
@font-size-lg : 34px;
@font-size-md : 32px;
@font-size-base : 30px;
@font-size-sm : 28px;
@font-size-xs : 26px;
@font-size-xxs : 24px;
@font-size-xxxs : 20px;

// line-height
@line-height-xxl : 72px;
@line-height-xl : 56px;
@line-height-lg : 50px;
@line-height-md : 48px;
@line-height-base : 46px;
@line-height-sm : 44px;
@line-height-xs : 42px;
@line-height-xxs : 40px;
@line-height-xxxs : 36px;


// vertical paddings
@vertical-padding-lg : 24px; // containers
@vertical-padding-md : 16px; // small containers and buttons
@vertical-padding-sm : 12px; // Form controls and items
@vertical-padding-xs : 8px; // small items
@vertical-padding-base : 4px; // base 

// horizontal paddings
@horizontal-padding-lg : 30px;
@horizontal-padding-md : 20px;
@horizontal-padding-sm : 10px; // base

//  radius  list
@border-radius-base : 8px;
@border-radius-lg : 16px;

// z-index list