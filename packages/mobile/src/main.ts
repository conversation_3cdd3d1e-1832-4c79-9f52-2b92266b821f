/*
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
*/
import Vue from 'vue';
import Router from 'vue-router';
import App from './App.vue';
import routes from './routes';
import store from './store';
import i18n from './config/i18n';
import infiniteScroll from 'vue-infinite-scroll';

import * as platform from 'cloudpivot-platform/platform';
import { formApi } from 'cloudpivot/api';
import initFormComponent from 'cloudpivot-form/form/registerComponent';
import {
  initMapSecret,
  initFileListTypes,
} from 'cloudpivot/common/src/config/common/common-config';
// 二开组件
// import extendComponents from 'cloudpivot-form-extend';
import './config/h3-mobile';
import './config/axios';
import './config/api';

import 'h3-mobile-vue/lib/theme/h3-mobile-vue.css';
import './lib/rem';

import './directives/hight-light';
import './directives/slider-left';

import { OAuthApi } from 'cloudpivot/api';

// 钉钉设置title
import zh from './locale/common.zh-CN';
import en from './locale/common.en-US';
import setDevToken from '../../../config/dev-token';
setDevToken();
Vue.use(infiniteScroll);

let importReportService = false;
const titleMap: any = {
  zh,
  en,
};

initFormComponent();
// initFormComponent(extendComponents);

// 钉钉设置title

// 钉钉授权导入
import env from '@/config/env';

import OauthApi from '@/apis/oauth';
// 钉钉授权导入__End

// 钉钉全局返回监听
import Back from '@/config/back';

Vue.config.productionTip = false;

Vue.use(Router);

window.Environment = {
  corpId: '',
  agentId: '',
  messageId: '',
  appCode: '',
  messageJson: null,
  isDingTalk: platform.IS_DINGTALK,
};
Vue.prototype.getPopupContainer = (triggerNode: any) => {
  return triggerNode.parentNode.parentNode;
};
/**
 * 设置路由集合
 * @param routeName 目标路由的名称
 */
function setRoute(routeName: string, options: any = {}) {
  let routeIndex = -1;
  // 寻找匹配的单个路由
  const route: any = routes.find((r: any, idx: number) => {
    if (r.name === routeName) {
      routeIndex = idx;
      return r;
    }
  });
  if (route) {
    // 如果是表单详情，则清空路由数组，仅保留表单详情并带入查询参数
    if (routeName === 'form-detail') {
      const formRoutes = require('./routes/form');
      routes.splice(0, routes.length);
      routes.push({
        ...route,
        query: options.query,
      });
      routes.push(...formRoutes.default);
      return;
    }
    // 普通路由设置，则将该路由重置为默认根路径的路由
    routes.splice(routeIndex, 1);
    routes[0] = Object.assign({}, route, { path: '/' }, options);
  }
}

/**
 * 设置移动端默认首页地址
 */
const setDefaultHomeAddress = () => {
  const {
    appCode,
    mode,
    messageId,
    messageJson,
    sheetCode,
    schemaCode,
    id,
    sCode,
  } = window.Environment;
  // 包含单应用的编码，进入单应用
  if (appCode) {
    setRoute('singleApp');
  }
  // 进入通知消息页面——表单详情
  else if (messageId) {
    setRoute('form-detail', {
      query: messageJson,
    });
  }
  // 进入发起流程填写表单页——表单详情
  else if (mode === 'create') {
    try {
      const query = JSON.parse(
        `{"${window.location.search.replace('?', '')}"}`
          .replace(/&/g, '","')
          .replace(/=/g, '":"'),
      );
      setRoute('form-detail', {
        query,
      });
    } catch (error) {
      console.error(error);
    }
  } else {
    //Else Empty block statement
  }
};

/**
 * 启用Vue实例挂载
 */
const startApp = () => {
  setDefaultHomeAddress();
  const router = new Router({
    base: process.env.BASE_URL,
    routes,
  });
  window.Environment.historyLength = 0;
  router.beforeEach((to, form, next) => {
    if (to.path === '/signature' && to.query.T) {
      localStorage.setItem('token', to.query.T as string);
    }
    if (to.name === 'apps-report' && !importReportService) {
      importReportService = true;
      import('@/views/apps/report-service');
    }

    if (to.name === 'singleApp') {
      const appName: string = window.Environment.appName || '';
      if (appName) {
        (window as any).pageTitle = appName;
        platform.service.setTitle(appName);
      }
    } else {
      // @ts-ignore
      const title: string = to.meta.title; // || '云枢';
      // @ts-ignore
      const titleKey: string = to.meta.titleKey; // 通过这个key值去映射国际化标题
      if (titleKey) {
        const locale: string =
          ((window as any).localStorage.getItem('locale') as string) || 'zh';
        const t: string = titleMap[locale][titleKey];

        (window as any).pageTitle = t;
        platform.service.setTitle(t);
        // setTitle(t);
      } else if (title) {
        (window as any).pageTitle = title;
        platform.service.setTitle(title);
        // setTitle(title);
      } else {
        //Else Empty block statement
      }
    }
    window.Environment.historyLength += 1;
    if (!to.name && /unfinishedWorkitem/.test(to.path)) {
      next({ name: 'workitems' });
    } else {
      next();
    }
  });
  new Vue({
    router,
    i18n,
    store,
    created() {
      // 会导致无法跳转到目标路由
      const {
        appCode,
        mode,
        messageId,
        messageJson,
        schemaCode,
        sheetCode,
        id,
        workflowInstanceId,
        workItemId,
        workflowCode,
        sCode,
      } = window.Environment;
      // if (window.history.length <= 1 && !(this.$route.query.goindex || appCode || mode || messageId)) {
      //   this.$router.replace({ path: window.location.pathname.replace(process.env.BASE_URL, '') + window.location.search, query: Object.assign({}, this.$route.query, { goindex: 'true' }) });
      // }
      // 从钉钉通知到达

      if (messageId) {
        if (messageJson.url) {
          window.location.href = messageJson.url;
        } else {
          router.replace({
            name: 'form-detail',
            query: messageJson,
          });
        }
      } else if (mode === 'create') {
        // 从发起表单到达
        const params = window.location.search;
        router.replace({
          path: `/form/detail/${params}`,
        });
      } else if (mode === 'form') {
        if (workflowInstanceId && workItemId) {
          // 流程表单
          const params = {
            workflowInstanceId,
            workitemId: workItemId,
          };
          router.replace({
            name: 'form-detail',
            // path: '/form/detail/',
            query: params,
          });
        } else if (workflowCode) {
          // 发起流程
          router.replace({
            name: 'form-detail',
            // path: '/form/detail/',
            query: {
              startWorkflowCode: workflowCode,
            },
          });
        } else if (schemaCode) {
          // 查看业务表单
          const params: any = {
            schemaCode,
            sheetCode,
          };
          if (id) {
            params.objectId = id;
          }
          router.replace({
            path: '/form/detail/',
            query: params,
          });
        } else {
          //Else Empty block statement
        }
      } else if (sCode) {
        // 批量打印二维码扫码查看表单
        // 通过短码去获取相关参数
        OauthApi.getShortCode(sCode).then((sCodeRes: any) => {
          if (sCodeRes.errcode === 0) {
            const json: any = JSON.parse(sCodeRes.data.pairValue);
            // const { sheetCode, schemaCode, id } = json;
            if (json.sheetCode && json.schemaCode && json.id) {
              const qrcodeParams: OAuth.GetFormUrlParams = {
                formCode: json.sheetCode,
                schemaCode: json.schemaCode,
                bizObjectId: json.id,
              };
              OauthApi.getFormUrl(qrcodeParams).then((res: any) => {
                if (res) {
                  router.replace(res);
                }
              });
            }
          } else {
            console.log(sCodeRes.errmsg);
          }
        });
      } else {
        //Else Empty block statement
      }
    },
    mounted() {
      if (platform.IS_DINGTALK) {
        // const back = require('./config/back');
        this.$nextTick(() => {
          // back.default();
          Back();
        });
      }
    },
    render: (h) => h(App),
  }).$mount('#app');
  Vue.prototype.router = router;
};

/**
 * 获取消息打开地址
 */
const getMessageUrl = async (messageId: any) => {
  const params: OAuth.FormUrlParams = {
    messageId: window.Environment.messageId,
  };

  const token = localStorage.getItem('token');
  // const res = await OauthApi.getFormUrl(params);
  const res = await formApi.getMessageFormUrl(params);
  if (res.errcode === 0 && res.data) {
    // alert('获取消息参数结果：'+ res.data);
    // 跳转到消息地址或者第三方浏览器直接打开地址
    const {
      bizObjectId,
      workItemId,
      workflowInstanceId,
      schemaCode,
      sheetCode,
      url,
    } = res.data;

    window.Environment.messageJson = {
      objectId: bizObjectId,
      workitemId: workItemId,
      workflowInstanceId,
      T: token,
      schemaCode,
      sheetCode,
      url,
    };
    startApp();
  }
};

function setUserInfo(info: any) {
  // 判断当前用户角色
  const isAppAdmin: boolean = info.permissions.includes('APP_MNG');
  const isSysAdmin: boolean = info.permissions.includes('SYS_MNG');
  const isRootAdmin: boolean = info.permissions.includes('ADMIN');

  const isWORKFLOW_ADMIN: boolean = info.permissions.includes('WORKFLOW_ADMIN');

  const isAdmin: boolean = isAppAdmin || isSysAdmin || isRootAdmin;

  // staff-selectot-base.ts 需要使用这个内容
  window.sessionStorage.setItem('user', JSON.stringify(info));

  store.commit('System/User/setIsAdmin', isAdmin);
  store.commit('System/User/setRootAdmin', isRootAdmin);
  store.commit('System/User/setAdmin', isSysAdmin || isRootAdmin);
  store.commit('System/User/setUserInfo', info);
  store.commit('System/User/setIsPrivilegedPerson', isWORKFLOW_ADMIN);
  store.commit('System/User/setIsAppAdmin', isAppAdmin);
}

export function init() {
  platform.start(env.client_id, env.scope).then((result: any) => {
    const { query } = result;
    const token = localStorage.getItem('token');
    if (!token && query.messageId) {
      localStorage.setItem(
        'isShowEmailResquest',
        `${env.portalHost}/mobile/?messageId=${query.messageId}`,
      );
      const theUrl = `${env.portalHost}/mobile/#/login`;
      window.location.href = theUrl;
    }
    window.Environment = query;
    OAuthApi.getUserInfo().then((res: any) => {
      if (res.errcode === 0) {
        setUserInfo(res.data);
      }
    });
    if (query.messageId) {
      getMessageUrl(query.messageId);
    } else {
      // alert('开始请求消息参数:消息id：'+window.Environment.messageId);
      startApp();
    }
    initMapSecret();
    initFileListTypes();
  });
}

init();
