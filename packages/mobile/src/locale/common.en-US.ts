/*
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
*/
/*
 * @Descripttion:
 * @version: v1.0
 * @Author: baidongsheng
 * @Date: 2021-08-18 19:20:43
 * @LastEditors: baidongsheng
 * @LastEditTime: 2021-09-22 15:47:21
 */

export default {
  ok: 'OK',
  continue: 'Continue',
  okAndDelete: 'OK And Delete',
  cancel: 'Cancel',
  clear: 'Clear',
  reset: 'Reset',
  search: 'Search',
  edit: 'Edit',
  adjust: 'Adjust node',
  more: 'More',
  delete: 'Delete',
  void: 'Void',
  updateOwner: 'Modify owner',
  tip: {
    saveSuccess: 'Save success',
    operationFail: 'Operation failed, please try again',
  },
  todo: 'To-do',
  toread: 'To-read',
  done: 'Done',
  read: 'Read',
  circulate: 'Circulate',
  myWorkflow: 'My Workflow',
  process: 'Processing',
  completed: 'Completed',
  canceled: 'Canceled',
  sequenceNo: 'SequenceNo',
  filter: 'Filter',
  workflowName: 'FlowName',
  input: 'Please input',
  inputWorkflowName: 'Please input workflowName',
  startTime: 'StartTime',
  inputStartTime: 'Please input startTime',
  endTime: 'EndTime',
  inputEndTime: 'Please input endTime',
  loadAllWorkflow: 'All processes loaded',
  workflowChart: 'WorkFlow diagram',
  approval: 'Approval logs',
  unknown: 'Unknown',
  homePage: 'Home',
  apps: 'Apps',
  workflows: 'Workflows',
  settings: 'Settings',
  workflowTrack: 'Workflow Track',
  startWorkflow: 'Start Workflow',
  batchOpt: 'Batch Handle',
  version: 'Version',
  toggleLanguage: 'Switch',
  showAll: 'Show All',
  showDetail: 'Show Detail',
  noTodoItems: 'No pending task',
  noToreadItems: 'No pending items to read',
  comment: 'comment {data}',
  trust: '【{name} entrust】',
  closePage: 'Close the current page',
  WorkflowUpdateTenedor: 'Modify owner',
  WorkflowAdjustment: 'Adjust node',
  lastLoginTime: 'Last login time: ',
  devOps: 'Operation',
  singleApp: {
    tips: 'You have {num} to-do tasks',
    tips2: 'You have {num} to-read tasks',
    toList: 'Go check',
  },
  workbanch: {
    myApplications: 'My Applications',
    myCharts: 'My Charts',
    management: 'Management',
    manageApplication: 'Applications Management',
    manageChart: 'Charts Management',
    added: 'Added',
    addable: 'Addable',
    addTips: 'Click on the application below to add',
    addTips2: 'Click on the chart below to add',
    addTips3: 'There is currently no {type}, click to',
    add: 'add',
    applicaion: 'application',
    chart: 'chart',
    loading: 'loading',
  },
  StartTime: 'Start time',
  EndTime: 'End time',
  PlzSelect: 'Please select',
  queryBox: {
    fixed: 'Specify date',
    dataItem: 'Current model',
    dynamic: 'Dynamic range',
    today: 'Today',
    yesterday: 'Yesterday',
    tomorrow: 'Tomorrow',
    LastWeek: 'Recent week',
    LastMonth: 'Recent mounth',
    LastYear: 'Recent year',
    ThisWeek: 'This week',
    ThisMonth: 'This month',
    ThisYear: 'This year',
    LastQuarter: 'Recent quarter',
    ThisQuarter: 'This quarter',
    last: 'Recent',
    future: 'Future',
    day: 'Day',
    week: 'Week',
    month: 'Month',
    quarter: 'Quarter',
    year: 'Year',
    days: '{symbol} {num} day',
  },
};
