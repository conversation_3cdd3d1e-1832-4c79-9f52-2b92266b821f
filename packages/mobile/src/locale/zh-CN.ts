/*
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
*/

import application from 'cloudpivot-list/application/locales/zh-CN';
import Common from 'cloudpivot/common/locales/zh-CN';
import flowCenter from 'cloudpivot-flow/flow-center/locales/zh-CN';
import flow from 'cloudpivot-flow/flow/locales/zh-CN';
import formComment from 'cloudpivot-form/form-comment/locales/zh-CN';
import form from 'cloudpivot-form/form/locales/zh-CN';
import list from 'cloudpivot-list/list/locales/zh-CN';
import common from './common.zh-CN';

export default {
  cloudpivot: {
    form,
    flowCenter,
    formComment,
    list,
    flow,
    application,
    Common,
  },
  languages: {
    common,
    form,
  },
};
