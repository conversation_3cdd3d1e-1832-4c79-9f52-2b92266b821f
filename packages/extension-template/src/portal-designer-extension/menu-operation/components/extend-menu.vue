<template>
  <a-tooltip placement="bottom">
    <template slot="title">
      <span>扩展提示</span>
    </template>
    <span class="icon-wrapper"><em class="icon aufontAll h-icon-all-tablet"></em>移动端</span>
  </a-tooltip>
</template>

<script lang="ts">
import { Component, Vue } from 'vue-property-decorator';
@Component({
  name: 'extendMenu',
  components: {},
})
export default class ExtendMenu extends Vue {
  created() {}
}
</script>

<style lang="less" scoped>
.icon-wrapper {
  display: flex;
  align-items: center;
  > em {
    margin-right: 5px;
  }
}
</style>
