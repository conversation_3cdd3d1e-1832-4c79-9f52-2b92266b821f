/**
 * 门户设计器导航组件左侧菜单配置属性描述说明
 */
interface MenuToolsConfig {
  name: string; //菜单名称
  components: string; //组件名称
  authority: ['admin', 'isAdmin']; //菜单权限控制
}

const changeMenuToolsConfig = (menus: MenuToolsConfig[]) => {
  const extendmenus: any = [
    {
      name: 'ExtendMenu',
      components: 'ExtendMenu',
      authority: [],
    },
  ];
  // menus.splice(3, 0, ...extendmenus);
  return menus;
};
export default changeMenuToolsConfig;
