<template>
  <div class="page">
    <div>
      <ul>
        <li>{{ userId }}</li>
        <li>{{ roleIds }}</li>
        <li>{{ roleName }}</li>
        <li>{{ phone }}</li>
        <li>{{ email }}</li>
        <li>{{ deptName }}</li>
        <li>{{ deptId }}</li>
      </ul>
    </div>
  </div>
</template>

<script lang="ts">
// 定义组件
import { Component, Vue, Prop } from 'vue-property-decorator';

@Component
export default class TestPage extends Vue {
  @Prop({ type: String, default: '' })
  userId!: string;

  @Prop({ type: Array || null, default: null })
  roleIds!: any[] | null;

  @Prop({ type: Array || null, default: null })
  roleName!: any[] | null;

  @Prop({ type: String, default: '' })
  phone!: string;

  @Prop({ type: String, default: '' })
  email!: string;

  @Prop({ type: String, default: '' })
  deptName!: string;

  @Prop({ type: String, default: '' })
  deptId!: string;
}
</script>

<style lang="less" scoped>
.page {
  display: flex;
  width: 100%;
  height: 100%;
  background-color: #1890ff;
  justify-content: center;
  align-items: center;

  div {
    font-size: 16px;
    color: #fff;
  }
}
</style>
