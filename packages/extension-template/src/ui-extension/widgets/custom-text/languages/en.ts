export default {
  title: 'TextList',
  properties: {
    content: 'Content',
    type: 'Text type',
    background: 'Background',
    border: 'Border',
    margin: 'Margin',
    padding: 'Padding',
    rotate: 'Rotate',
    shadow: 'Shadow',
    size: 'text size',
    sizeLimit: 'Size limite',
    visible: 'Visible',
    textIndent: 'Indent',
    letterSpacing: 'Letter spacing',
    wordSpacing: 'Word spacing',
    lineHeight: 'Line spacing',
    userSelect: 'Select',
    lineFeed: 'Line feed',
    textAlign: 'Text align',
    verticalAlign: 'Vertical align',
    wordBreak: 'Word break',
    fontStyle: 'Font style',
    fontFamily: 'Font family',
    fontSize: 'Font size',
    fontColor: 'Color',
    textOverflow: 'Text overflow',
    textValue: '文本内容',
  },
  groups: {
    props: 'Property',
    text: 'Fonts',
    customGroup: '自定义组件',
  },
  enums: {
    normal: 'Normal',
    'break-word': 'Break word',
    inherit: 'Inherit',
    'Microsoft YaHei': 'Microsoft YaHei',
    SimSun: 'SimSun',
    Sim<PERSON>ei: 'SimHei',
    FangSong: 'FangSong',
    ellipsis: 'Ellipsis',
    clip: 'Clip',
    overflow: 'Overflow',
  },
  events: {
    click: 'Click',
  },
};
