export default {
  title: '自定义文本',
  properties: {
    backgroundColor: '背景颜色',
    backgroundImg: '背景图片',
    borderRadius: '圆角',

    borderStyle: '边框样式',
    borderThickness: '边框粗细',
    borderColor: '边框颜色',

    textColor: '文字颜色',
    textThickness: '文字粗细',
    title: '标题名称',
    icon: '组件图标',
    textValue: '文本内容',
  },
  groups: {
    props: '文本属性',
    text: '文字',
    customGroup: '自定义组件',

    backgroudStyle: '背景样式',
    borderStyle: '边框样式',
    textStyle: '文字样式',
    advancedConfig: '基础配置',
  },
  enums: {
    none: '无',
    small: '小',
    medium: '中',
    big: '大',
    dashed: '虚线',
    solid: '实线',
    normal: '正常',
    bold: '加粗',
  },
  events: {
    click: '单击时',
  },
};
