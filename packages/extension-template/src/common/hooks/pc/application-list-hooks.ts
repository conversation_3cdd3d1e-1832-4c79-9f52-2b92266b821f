/**
 * 视图预加载钩子
 */
class PreLoad {
  /**
   * 全局默认钩子
   * @param vm 视图实例
   */
  defaultHook(vm) {
    // console.log('PreLoad.defaultHook', vm);
  }

  /**
   * 示例：针对某个应用的钩子
   * 方法名称格式为：应用编码
   */
  // ['fzh_test_002'](vm) {}

  /**
   * 示例：针对某个模型的钩子
   * 方法名称格式为：应用编码_模型编码
   */
  // ['fzh_test_002_stsxtjyh'](vm) {}

  /**
   * 示例：针对具体视图的钩子
   * 方法名称格式为：应用编码_模型编码_视图编码
   */
  // ['fzh_test_002_stsxtjyh_stsxtjyh'](vm) {}
}

/**
 * 单元格点击钩子
 */
class ColClick {
  /**
   * 全局默认钩子
   * @param vm      视图实例
   * @param data    视图数据
   * @param rowIdx  行索引
   * @param colIdx  列索引
   * @param rowData 行数据
   * @param colData 列数据
   * @returns       返回 false 可阻止默认行为
   */
  defaultHook(vm, data, rowIdx, colIdx, rowData, colData) {
    // console.log('CellClick.defaultHook', vm, data, rowIdx, colIdx, rowData, colData);
    // return false;
  }

  /**
   * 示例：针对某个应用的钩子
   * 方法名称格式为：应用编码
   */
  // ['fzh_test_002'](vm, data, rowIdx, colIdx, rowData, colData) {}

  /**
   * 示例：针对某个模型的钩子
   * 方法名称格式为：应用编码_模型编码
   */
  // ['fzh_test_002_stsxtjyh'](vm, data, rowIdx, colIdx, rowData, colData) {}

  /**
   * 示例：针对具体视图的钩子
   * 方法名称格式为：应用编码_模型编码_视图编码
   */
  // ['fzh_test_002_stsxtjyh_stsxtjyh'](vm, data, rowIdx, colIdx, rowData, colData) {}
}

export const preLoadHooks = new PreLoad();

export const colClickHooks = new ColClick();
