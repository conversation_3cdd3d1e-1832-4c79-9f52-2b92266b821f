interface COLORS {
  name: string;
  primaryColor: string;
  dark_subColor: string;
  light_subColor: string;
  dark_textColor: string;
  light_textColor: string;
  dark_headBGColor: string;
  light_headBGColor: string;
  headMenuActiveColor: string;
  dark_activeBGColor: string;
  light_activeBGColor: string;
  hoverColor: string;
  highlightColor: string;
  borderColor: string;
  shadowColor: string;
  headerMenuHoverColor: string;
  dark_hoveBGColor: string;
  light_hoveBGColor: string;
}

export const customTheme: COLORS[] = [
  // {
  //   name: 'customTheme',
  //   primaryColor: '#ff00ff', // 主色
  //   dark_subColor: '#ff66ff', // 深色辅助色（流程中心、模型列表侧边导航栏背景颜色）
  //   light_subColor: '#ffffff', // 浅色辅助色
  //   dark_textColor: '#ffffff', // 深色文字色
  //   light_textColor: '#ff00ff', // 浅色文字色
  //   dark_headBGColor: '#ff00ff', // 深色头部背景色
  //   light_headBGColor: '#ffffff', // 浅色头部背景色
  //   headMenuActiveColor: '#bf984a', // 深色头部菜单选中颜色
  //   dark_activeBGColor: '#f9f9f9', // 深色侧边菜单选中颜色
  //   light_activeBGColor: '#f1f2f6', // 浅色侧边菜单选中颜色
  //   dark_hoveBGColor: '#f9f9f9', // 深色鼠标经过背景颜色
  //   light_hoveBGColor: '#f1f2f6', // 浅色鼠标经过背景颜色
  //   hoverColor: '#5291FF', // 侧边菜单鼠标悬停背景色
  //   highlightColor: '#ff00ff', // 高亮文字颜色
  //   borderColor: '#5291ff', // 表单控件border颜色
  //   shadowColor: 'rgba(41, 112, 255, 0.2)', // 表单控件投影颜色
  //   headerMenuHoverColor: '#0076F6', //头部文字hover颜色
  // }
];
