@import '~cloudpivot/common/common.less';
@keyframes loading {
  100% {
    transform: rotate(360deg);
  }
}

.app-menu {
  height: 100%;
}

/deep/.ant-confirm-body{
 background: red;
}

.application-box {
  height: 100%;
  position: relative;

  .loading-box {
    display: flex;
    align-items: center;
    justify-content: center;
    position: absolute;
    width: 100%;
    height: calc(100% - 52px);
    background: rgba(255, 255, 255, 0.85);
    z-index: 100;

    .loading {
      text-align: center;
    }

    img {
      animation: loading 1s linear infinite;
    }

    p {
      margin-top: @base4-padding-lg;
      color: @light-color-3;
    }
  }

  .content-top {
    display: flex;
    margin-bottom: @base4-padding-md;
    justify-content: space-between;
    align-items: center;
    height: 34px;
    h2 {
      display: flex;
      align-items: center;
      color: @light-color-1;
      font-size: @font-size-16;
      font-weight: @font-weight-md;
      padding-left: 24px;
    }

    .search-input {
      margin-left: @base4-padding-md;
    }
  }

  .table-box {
    height: calc(100% - 52px);
    // box-shadow: 0px 2px 8px 0px rgba(30, 85, 255, 0.1);
    background-color: @white-background;
    border-radius: @border-radius-lg;
    // padding: 0 16px;
    // background-color: #f0f1f5;

    /deep/.ant-table-thead>tr>th {
      color: @light-color-2;
      font-weight: @font-weight-lg;
      background-color: @main-background;
    }

    /deep/.ant-table-tbody>tr>td {
      color: @light-color-1;
    }

    /deep/.ant-table-thead>tr>th,
    /deep/.ant-table-tbody>tr>td {
      padding: @base4-padding-xs @base4-padding-md;
    }

    /deep/.ant-table-placeholder {
      display: none;
    }

    // /deep/.ant-pagination-item-active{
    //   background: @primary-color;
    //   a {
    //     color: white;
    //   }
    // }
    .no-data {
      position: absolute;
      top: 40%;
      left: 50%;
      transform: translate(-50%, -50%);
      text-align: center;

      p {
        font-size: @font-size-14;
        color: @light-color-3;
      }
    }

    .gray {
      color: @light-color-3;
    }

    .fake-btn {
      cursor: pointer;
      transition: all ease .3s;

      &:hover {
        color: @primary-color;
      }
    }

    .load-all {
      text-align: center;
      color: @light-color-3;
      margin-top: @base10-padding-sm * 4;
    }

    .overtime {
      display: inline-block;
      width: 46px;
      height: 18px;
      background-color: @error-color;
      color: @dark-color-2;
      border-radius: 9px;
      font-size: @font-size-10;
      text-align: center;
      margin-left: @base4-padding-md;
    }

    .overtime-icon {
      margin-left: @base4-padding-md;
    }

    .pagination-box {
      margin-top: @base4-padding-xs;
      text-align: right;
      padding: @base4-padding-xs @base4-padding-md;
      border-top: 1px solid @base-border-color;
    }

    /*ie11 css hack*/
    @media all and (-ms-high-contrast:none) {
      *::-ms-backdrop, .pagination-box { padding-top: 18px; }
    }

    /deep/.ant-select-dropdown-menu-item {
      text-align: center;
    }
  }

  .reload-btn {
    margin-top: @base4-padding-md;
  }

  // 改变列宽指令
  .resize {
    display: inline-block;
    height: 22px;
    width: calc(100% + 32px);
    padding: 0 16px;
    -webkit-transform: translateX(-16px);
    transform: translateX(-16px);
    border-left: 1px solid #e8e8e8;
  }

  .resize-first {
    border: none;
  }

  .text-ellipsis {
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .adjust-text {
    display: inline-block;
    width: 44px;
  }

  @media screen and (max-width: 1366px) {
    .workitem-box .table-box .no-data {
      margin-top: 20px;
    }
  }

  /deep/.sheet {
    border: none;

    .sheet__col {
      border-right: none;
    }

    /deep/.sheet__head {
      &>.sheet__col {
        border-right: 1px solid rgba(0, 0, 0, 0);
        // padding: 0;
        // margin: 8px;
        &.center.middle {
          border-right: none;
        }
      }

      // & > .sheet__cols > .sheet__row > .sheet__col {
      //   margin: 8px;
      //   padding: 0;
      // }
    }

    .h3-size-slider {
      border-right: 1px solid rgba(0, 0, 0, 0);
    }

    .sheet__head {
      font-weight: 600;
      background-color: #f8fbff;
      border-radius: 4px;
    }

    .sheet__body {
      overflow: auto;
    }

    .h3-size-slider__sider {
      top: 8px;
      background: rgba(0, 0, 0, 0.15);
      width: 1px;
      height: 21px;
      right: 3px;
      &:hover {
        width: 3px;
        background: #2970ff
      }
    }
  }
}

.pdf-frame-div {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  z-index: 233;
  background-color: #000;
  opacity: 0.6;
}

.pdf-frame {
  position: fixed;
  left: 250%;
  top: 0;
  z-index: 2333;
  width: 50%;
  height: 100%;
}

.close-previewPdf {
  width: 32px;
  position: fixed;
  z-index: 23330;
  left: 75.5%;
  bottom: 96%;
  color: #606165;
  font-size: 32px;
  background: #ccc;
  border-radius: 50%;

  &:hover {
    color: #000;
    cursor: pointer;
    transition: all 0.3s
  }
}
