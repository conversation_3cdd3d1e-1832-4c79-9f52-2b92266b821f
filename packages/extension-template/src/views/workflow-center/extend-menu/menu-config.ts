/**
 * 流程中心左侧菜单配置属性描述说明，二开人员请勿修改
 */
interface MenuConfig {
  path: string; //点击菜单后跳转的路由地址, 也是各个路由的标识，注意：不可重复
  iconClass: string; //使用iconfont图标库中图标，可以浏览器打开cloudpivot-icons依赖包中的demo_html进行查找选择
  /**
   * menu名称中英文配置
   * 可以直接传入对应的中英文名称
   * 或者传入一个函数（主要是产品定义的菜单使用）
   */
  name_i18n:
    | {
        zh: string; //中文menu名称设置
        en: string; //英文menu名称设置
      }
    | Function;

  /**
   * 权限配置
   * 如果数组为空代表所有人都可以看
   * 如果数组中有一个或多个元素，则只有在用户拥有数组中配置的权限时才显示
   * isRootAdmin: 超级管理员，admin账号
   * admin: 系统管理员，在后台系统管理中配置(超级管理员有系统管理员权限，admin中也包含)
   * isAppAdmin: 子管理员(原来的数据管理员)，在后台系统管理中配置
   * isPrivilegedPerson 流程特权处理人，在流程属性中配置
   * isNormal: 普通用户（没有任何管理员及特权人权限的人）
   * 注意：产品菜单权限不允许二开修改，修改了也会不生效
   */
  authority?: (
    | 'isRootAdmin'
    | 'admin'
    | 'isAppAdmin'
    | 'isPrivilegedPerson'
    | 'isNormal'
  )[];

  /**
   * 在菜单名称右侧的计数区域的显示字段
   * 数据源是Vuex中的流程中心store
   * 主要产品使用，目前待办数据条数:unFinishedListCount、待阅数据条数:unReadListCount可用，二次开发可忽略
   */
  countField?: string;

  /**
   * 菜单顶部是否有分隔线，类似将菜单进行分组显示
   * 主要产品使用，二次开发可忽略
   */
  topDivider?: boolean;
}

const changeWorkflowCenterMenu = (menus: MenuConfig[]) => {
  /*
  const extendmenus:any = [{
    path: '/workflow-center/extend-menu',
    iconClass: 'h-icon-all-send',
    name_i18n: {
      zh: '扩展菜单',
      en: 'extend_menu',
    },
    authority: ['isAdmin', 'isAppAdmin', 'isPrivilegedPerson', 'isNormal'],
  }]
  menus.splice(6, 0, ...extendmenus);
  */
  return menus;
};
export default changeWorkflowCenterMenu;
