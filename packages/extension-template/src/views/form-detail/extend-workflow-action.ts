/**
 *
 * @param action
 * @param data
 * @param vm 表单详情界面vue实例，拥有许多现有按钮执行逻辑，可以直接使用
 */
export async function onExtendActionClick(
  action: {
    code: string;
    text: string;
  },
  data: any,
  vm: FormDetailVueInstance,
) {
  console.log('执行成功！！！', action);
}

interface FormDetailVueInstance {
  //扩展操作默认接口
  customOperation: () => Promise<any>;
  //作废当前流程
  cancel: () => Promise<any>;
  //结束流程
  finish: () => Promise<any>;
  //撤回流程
  retrieve: () => Promise<any>;
  //删除流程
  delete: () => Promise<any>;
  //保存表单
  save: () => Promise<any>;
  //编辑表单
  edit: () => Promise<any>;
}

/**
 * 扩展操作接口参数
 */
export interface CustomOperationParam {
  workItemId: string;
  workItemAction: string;
  participantors: Participant[];
  comment: string;
}

/**
 * 流程实例参与者
 */
export interface Participant {
  id?: string; //用户id
  name?: string; //用户名称 "李思维"
  sourceId?: string; //来源id "Activity2"
  sourceName?: string; // 来源名称 "填写申请单"
  workItemType?: Number; // 来源类型 "NORMAL"
  isTrust?: boolean;
  trustorProcessor?: any;
}
