<!--
禁止修改!此文件是产品代码的一部分，后续可能变更或者不再开放。
若有问题，请参考前端相关二开文档。
-->

<template>
  <H3Field
    class="et-rate"
    :showIcon="false"
    :label="label"
    :required="ctrl.required"
    :labelStyle="styleObj"
    :tip="controlOpts.tips"
  >
    <a-rate v-model="ctrl.value" :allowHalf="allowHalf" :count="count" />
  </H3Field>
</template>

<script lang="ts">
import { LogicOptions } from 'cloudpivot-form/form/schema';
import { BaseControl } from 'cloudpivot-form/form/src/common/controls/base-control';
import { H3Field, H3Switch } from 'h3-mobile-vue';
import { Component } from 'vue-property-decorator';
import { Rate } from '@h3/antd-vue';
@Component({
  name: 'et-rate',
  components: {
    H3Switch,
    H3Field,
    ARate: Rate,
  },
})
export default class ETRate extends BaseControl<LogicOptions> {
  get allowHalf() {
    return this.options.allowHalf;
  }

  get count() {
    return this.options.count;
  }

  get options() {
    return this.control.options;
  }
}
</script>

<style lang="less" scoped></style>
