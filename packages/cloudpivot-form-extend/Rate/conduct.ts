import baseAttribute from 'cloudpivot-form/form/src/common/component-base-attribute';
import {
  ControlAttributeType,
  ControllerConduct,
} from 'cloudpivot-form/form/typings';
export default {
  groups: {
    base: {
      label: '基础信息',
      keys: [
        'name',
        'visible',
        'style',
        'tips',
        'dataItemType',
        'dataItemName',
      ],
    },
    controller: {
      label: '控制属性',
      keys: ['count', 'allowHalf', 'span'],
    },
  },
  properties: {
    name: {
      ...baseAttribute.name,
    },
    style: {
      ...baseAttribute.style,
    },
    dataItemType: {
      ...baseAttribute.dataItemType,
      value: '数值',
    },
    dataItemName: {
      ...baseAttribute.dataItemName,
    },
    count: {
      inputMethod: ControlAttributeType.Dropdown,
      options: {
        list: () => {
          const s: any = [];
          let i = 1;
          while (i <= 10) {
            s.push({ label: i, value: i });
            i++;
          }
          return s;
        },
      },
    },
    tips: {
      options: {
        regexps: {
          maxLength: {
            len: 200,
            message: '长度不能超过200字节',
          },
        },
        placeholder: '控件提示',
      },
    },
    span: {
      ...baseAttribute.span,
    },
  },
} as ControllerConduct;
