![](https://pic.downk.cc/item/5ea7a2fdc2a9a83be57dc0a4.png)

云枢表单设计时页面布局.

左侧: 组件面板.

中间: 组件布局.

右侧: 组件属性栏.

## 组件构成

一个表单组件有四个部分组成:

1. 组件信息(Info) - 说明组件的名称,数据类型,所属的组件分组.
2. 组件属性(Schema) - 声明组件所拥有的属性和属性的类型.
3. 组件属性行为(Conduct) - 定义组件属性在组件属性栏的特定动作.
4. Vue 组件 - 组件在 admin 设计时和 PC、Mobile 运行时的组件.

推荐组件目录结构:

--- NewComponent

​ --- index.ts

​ --- schema.ts

​ --- conduct.ts

​ --- components

​ --- design.vue

​ --- pc.vue

​ --- mobile.vue

一个组件应遵循的接口`ComponentAsset`:

```typescript
interface ComponentAsset {
	info: ComponentInfo;

	schema: ObjectPropertyInfo;

	conduct: ControllerConduct;

	/**
	 * 组件 design/设计时 pc/电脑端浏览器 mobile/移动端h5
	 */
	components: {
		design: Component<any, any, any, any> | AsyncComponent<any, any, any, any>; // VueConstructor<Vue> | (() => Promise<typeof import('*.vue')>)
		pc?: Component<any, any, any, any> | AsyncComponent<any, any, any, any>;
		mobile?: Component<any, any, any, any> | AsyncComponent<any, any, any, any>;
	};
}
```

```typescript
import { ComponentAsset } from "cloudpivot-form/form/typings";
export default {
	info,
	schema,
	conduct,
	components: {
		design,
		pc,
		mobile,
	},
} as ComponentAsset;
```

## 组件信息(Info)

```typescript
import { ComponentInfo } from "cloudpivot-form/form/typings";
import { DataItemType, FormControlType } from "cloudpivot-form/form/schema";
export const info: ComponentInfo = {
	title: "单行文本", // 组件名称,在组件面板展示的名称
	type: "single-line", // 组件的type, 组件的唯一标识之一. 在HTML自定模式下 标签名
	icon: "h-icon-all-single-line-text", // 组件icon,在组件面板和组件名称一起展示
	dataItemType: DataItemType.ShortText, // 组件数据项类型, 组件在运行时的数据类型.可以重复.
	formControllerType: FormControlType.Text, // 组件控制编码类型, 组件唯一标识之一.应该是number类型.
	group: "基础控件", // 组件分组类型, 在组件面板展示的分类.
};
```

⚠️ 组件的信息应该遵循接口`ComponentInfo`

```typescript
interface ComponentInfo {
	/**
	 * 控件名称
	 */
	title: string;

	/**
	 * 控件类型
	 */
	type: string;
	/**
	 * 数据项类型
	 */
	dataItemType?: DataItemType;
	/**
	 * 控制器类型
	 */
	formControllerType: FormControlType;
	/**
	 * 强制替换原生组件. ps.布局控件无法被替换.
	 */
	force?: boolean;
	/**
	 * 控件图标
	 */
	icon?: string;

	/**
	 * 控件所属分组
	 */
	group?: string;
}
```

### title

**title** 必填. 类型: String

### type

**type** 必填,唯一. 类型: String

⚠️ 如果要替换云枢原生组件,需要结合`force`使用. 云枢`布局控件`不能替换.

### icon

**icon** 非必填. 类型: String

⚠️icon 已经引入 admin 设计时,可使用的 icon 请查看`entries\admin\src\assets\icons\demon_index.html`

### dataItemType

**dataItemType** 非必填. 类型: Number

`import { DataItemType } from "cloudpivot-form/form/schema";`

```typescript
/**
 * 数据项类型
 */
export enum DataItemType {
	/**
	 * 短文本
	 */
	ShortText = 0,

	/**
	 * 长文本
	 */
	LongText = 1,

	/**
	 * 数值
	 */
	Number = 2,

	/**
	 * 日期
	 */
	Date = 3,

	/**
	 * 逻辑
	 */
	Logic = 4,

	/**
	 * 选人控件
	 */
	StaffSelector = 5,

	/**
	 * 附件
	 */
	Attachment = 6,

	/**
	 * 审批意见
	 */
	ApprovalOpinion = 7,

	/**
	 * 子表
	 */
	Sheet = 8,

	/**
	 * 关联单选
	 */
	RelevanceForm = 9,

	/**
	 * 地理位置
	 */
	Address = 10,

	/**
	 * 关联多选
	 */
	RelevanceFormEx = 11,
}
```

⚠️ 数据项类型是和后台同步的.不要修改或新增.

### formControllerType

**formControllerType** 必填,唯一.类型: Number.

⚠️ 控制器类型编码应该小于 90.并且不应该和现有的组件控制编码重复.已有的组件控制器编码,请看*云枢已有组件*一节.

### group

**group** 非必填.类型:String.

声明组件所属的分类, 目前已有组件分类有: **基础控件**、**布局控件**、**系统控件**、**高级控件**

## 组件属性(Schema)

Schema 应该遵循接口`ObjectPropertyInfo` .

```typescript
export interface PropertyInfo {
	title?: string;

	type?: string;

	default?: any;
}
export interface ObjectPropertyInfo extends PropertyInfo {
	$id: string;
	$ref?: string;
	properties: {
		[key: string]: PropertyInfo;
	};
}
```

Schema 描述组件所拥有属性和它的数据类型和默认值.

⚠️Schema 是参考 JSON Schema Specification.但它并没有实现 JSON Schema Specification 所有的能力.

```typescript
import { ObjectPropertyInfo, DataType } from "cloudpivot-form/form/typings";
export default {
	$id: "shortText", // schema id. 具有唯一
	type: DataType.Object,
	properties: {
		name: {
			type: DataType.String,
			title: "控件名称",
			default: "",
		},
		name_i18n: {
			type: DataType.Object,
			title: "多语言",
		},
		visible: {
			type: DataType.Boolean,
			title: "是否可见",
			default: true,
		},
		style: {
			type: DataType.String,
			title: "控件样式",
		},
		tips: {
			type: DataType.String,
			title: "控件Tips",
		},
		dataItemName: {
			type: DataType.String,
			title: "绑定数据项编码",
		},
	},
} as ObjectPropertyInfo;
```

⚠️ 定制开发组件需要在 Schema 定义 `name`,`name_i18n` ,`visible`,`style`,`tips`,`dataItemName`

### type

**type** 必填.类型:String

属性类型:`"cloudpivot-form/form/typings"`

```typescript
export enum DataType {
	Object = "object",
	String = "string",
	Boolean = "boolean",
	Integer = "integer",
	Array = "array",
	Number = "number",
}
```

### title

**title** 必填.类型:String

title 是属性在组件属性栏展示的名称.

### default

**default** 非必填.类型: DataType

属性默认值.

## 组件属性行为(Conduct)

`Conduct`应该遵循接口`ControllerConduct`.

`import {ControllerConduct,ControlAttributeType,} from "cloudpivot-form/form/typings";`

```typescript
enum ControlAttributeType {
	Boolean,
	String,
	Dropdown,
	Modal,
	Date,
	Array,
}
interface ControllerConduct {
	groups: {
		[key: "base" | "controller"]: {
			label: string;
			keys: string[];
		};
	};
	properties?: {
		[key: string]: {
			inputMethod?: ControlAttributeType;
			inputComponent?:
				| Component<any, any, any, any>
				| AsyncComponent<any, any, any, any>; // 引入vue标准的组件
			options?: ControllerOptions;
			[key: string]: any;
		};
	};
}
```

### ControllerConduct

#### groups

**groups** 组件属性分组,在组件属性栏展示的数据.

必填. 类型:Object.

```typescript
  groups: {
    base: {
      label: "基础信息",
      keys: ["name", "visible", "style", "tips"],
    },
    controller: {
      label: "控制属性",
      keys: [
        "defaultValue",
        "displayFormula",
        "requiredFormula",
        "options",
        "direction",
      ],
    },
  }
```

**groups** 类型是 Object.key 是分组的 code,目前 key 只支持 `case`和`controller`两个.

如上面例子: keys 是在 Schema 中定义的属性. 它在 keys 中的顺序就是在组件属性栏中的顺序.

#### properties

**properties** 定义属性在的输入行为.

非必填. 类型: Object.

```typescript
properties: {
  displayFormula: {
    inputMethod: ControlAttributeType.Modal,
    inputComponent: () =>
        import("cloudpivot-form/form/src/common/components/required-condition.vue"),
    options: {
      formatter: (val) {
          if (val) {
    				return '已设置';
  				}
  				return '未设置';
      },
    },
  }
}
```

#### inputMethod

属性输入值的方式,它的类型有:

```typescript
enum ControlAttributeType {
	Boolean,
	String,
	Dropdown,
	Modal,
	Date,
	Array,
}
```

**ControlAttributeType.Dropdown**

表示通过下拉框选值. 可以在`options.list`或`options.dataList`定义下拉框的值.

`options.list`应该是个 Array 类型的值, `options.dataList`是一个 Function,应该返回一个数组.

**ControlAttributeType.Modal**

表示通过弹框输入值. 弹框 Vue 组件赋值给**inputComponent**

#### inputComponent

**inputMethod** 值类型是 ControlAttributeType.Modal 时,**inputComponent** 是必填.

**inputComponent**要是标准的 Vue 组件

弹框组件约定:

```typescript
@Prop({type: Object})
 	modalData!: any; // modalData.data.value
@Prop({type:Object})
	modalOptions // 可以获取到属性配置中其他内容,除了 inputMethod, inputComponent.

this.$emit("backData", {value: modalValue}); // 弹框出确定, modalValue会被写给 属性栏
this.$emit("closeModal") // 关闭弹出框
```

⚠️ 自定义的 Vue 组件中可以获取到这些信息.

#### options

```typescript
interface ControllerOptions {
	dateFormat?: string; // 时间格式
	formatter?: Function; //格式化 （value, attr: ControlAttributeOptions, attrs: ControlAttributeOptions[]）
	regexps?: any; // 文本正则匹配 regexp message
	maxLength?: number; // 最长文本数量
	pickerOptions?: any; // 用户控件配置项
	disabled?: Boolean; // 当前控件是否可以交互
	dataList?: Function; // 通过函数获取下拉控件值
	list?: any; // 下拉控件值
	fieldDisplay?: Array<Array<string>>; // 针对于Bool类型 需要显示或者隐藏字段 根据当前value [ [],[] ] 0 是true是需要隐藏的字段，1 是false 需要隐藏的字段
	hideField?: Function | string[]; // 所有控件需要隐藏字段的方法 fun返回一个字符串数组 或者 字段数字
	transaction?: Function; //事务处理 { attr，attrs, displayFields, allControls}
	callback?: Function; //事件回调
	importModal?: Function; //弹窗输入重载  return {value:value, default:default};
	exportModal?: Function; ///弹窗输出重载 return data: any;
	allowClear?: boolean; // 下拉框鼠标移上去可删除
}
```

**options** 提供属性栏输入值钩子方法.最常用的有:

##### options.formatter

格式话显示值.

类型: `(value,attr,attrs):any`

在属性栏展示的值,不会修改实际的值.

##### options.hideField

根据当前数据值隐藏其他属性.

类型: `hideField : string[]` or `hideField:(value):string[]`

##### options.list

下拉控件值.

类型: `any[]`

##### options.dataList

下拉控件值

类型: `():any[]`

##### options.disabled

属性栏是否可以操作

类型: `Boolean`

##### options.callback

属性值变化后回调方法

类型:`callback:(key, attr,attrs,vm):vold`

**attr** 当前属性信息

**attrs** 所有属性信息

**vm** vue 实例

## Vue 组件

组件的 view 要求是标准的 Vue 组件.

组件分为

设计时:design

运行时: PC、Mobile

### design

设计时的组件可以获取到组件控制器.

```typescript
@Prop()
control!:any // control.options可以获取到组件的属性
```

### PC、Mobile

运行时的组件做了约定.需要继承`BaseControl`控制器.

```typescript
<template>
  <div>
    <a-rate v-model="ctrl.value" :allowHalf="allowHalf" :count="count" />
  </div>
</template>
<script lang="ts">
import { Component, Vue, Prop, Watch, Inject } from "vue-property-decorator";
import {BaseControl} from "cloudpivot-form/form/src/renderer/controls"
import * as typings from "cloudpivot-form/form/schema";
import { Rate } from "@h3/antd-vue";
@Component({
  name: "Rate",
  components: {
    ARate: Rate,
  }
})
export default class ETRate extends BaseControl<typings.FormControlOptions> {
  // this.ctrl 组件的控制器. 组件的value 通过 this.ctrl获取
  get allowHalf() {
    return this.options.allowHalf
  }
  get count() {
    return this.options.count
  }
  // 那组件配置的属性
  get options() {
    return this.control.options // 组件的所有属性
  }
  created() {
    this.ctrl.value // 组件的值
  }
}
```

## 将组件注入云枢表单

定制开发组件需要引入到`cloudpivot-form/form/registerComponent.ts`

```typescript
import component from "cloudpivot-form/form/src/components"; // 云枢原生组件
import { register } from "cloudpivot-form/form/utils/register";
import { ComponentInfo, ComponentAsset } from "cloudpivot-form/form/typings";
import extendComponent from "cloudpivot-form/form/components-extend"; // 扩展组件

const components = [...component, ...extendComponent];
export default function () {
	for (let cmp of components) {
		register.append(cmp);
	}
}
```

## 云枢已有组件

### 基础控件

组件名称,组件 type,控制器编码

| 组件名称 | 组件 Type               | 控制器编码 |
| -------- | ----------------------- | ---------- |
| 单行文本 | single-line             | 1          |
| 长文本   | text-multiline          | 2          |
| 日期     | calendar                | 3          |
| 数值     | number                  | 4          |
| 单选框   | single-selection        | 5          |
| 复选框   | check-square            | 6          |
| 下拉框   | drop-down               | 7          |
| 逻辑     | logic                   | 8          |
| 附件     | attachment              | 9          |
| 图片     | picture-card            | 10         |
| 手写签名 | signature               | 15         |
| 地址     | inputAddress            | 14         |
| 人员单选 | staffSelector           | 50         |
| 人员多选 | staffMultiSelector      | 51         |
| 部门单选 | departmentSelector      | 60         |
| 部门多选 | departmentMultiSelector | 61         |

### 布局控件

| 组件名称 | 组件 Type   | 控制器编码 |
| -------- | ----------- | ---------- |
| 分组标题 | group       | 200        |
| 描述说明 | description | 202        |
| 子表     | sheet       | 201        |
| 标签页   | tabs        | 500        |

### 系统控件

| 组件名称   | 组件 Type       | 控制器编码 |
| ---------- | --------------- | ---------- |
| 单据号     | sequence-number | 100        |
| 创建人     | create          | 101        |
| 创建人部门 | createDept      | 102        |
| 拥有者     | ownerId         | 106        |
| 创建时间   | createdTime     | 104        |
| 修改时间   | modifiedTime    | 105        |
| 修改人     | modifier        | 103        |

### 高级控件

| 组件名称 | 组件 Type         | 控制器编码 |
| -------- | ----------------- | ---------- |
| 关联表单 | relevance-form    | 80         |
| 关联查询 | reverse-relevance | 90         |

### other

| 组件名称   | 组件 Type       | 控制器编码 |
| ---------- | --------------- | ---------- |
| 位置       | inputLocation   | 11         |
| 日期范围   | dateRange       | 12         |
| 数值范围   | numberRange     | 13         |
| Tips       | tips            | 16         |
| 审批意见   | approvalOpinion | 70         |
| 系统-其他  | systemOther     | 107        |
| 单据状态   | sequenceStatus  | 108        |
| 子表统计   | sheetStatistic  | 300        |
| Html       | html            | 400        |
| 标签页面板 | tabsPanel       | 501        |

⚠️**other**是系统存在但不在组件面板上的组件. 这些组件也不要复写.

## 云枢组件之间的联动

每一个运行时的云枢组件应该继承`base-control.ts`,在`base-control.ts`已经注入了`valChange`方法用来获取其它组件的值变化的订阅.

![](https://pic.downk.cc/item/5eb37ba0c2a9a83be5e47d06.png)

### valChange(key:string, fun: Function) => void

#### key

**key** 组件的绑定数据项编码(`dataItemName`)

#### fun

**fun** 值变化回掉方法.**fun** 函数的参数是一个对象:`{value:新值, oldValue:旧值}`

更多的组件能力请查看云枢帮助中心: (https://www.yuque.com/skwme4)

## 云枢自定义组件开发目录

![](https://pic.downk.cc/item/5eb4b9d8c2a9a83be51a51c5.png)

自定组件建议放置:`modules/@cloudpovot/form/components-extend`目录中.

然后需要在`registerComponent.ts`中引入自定义组件

![](https://pic.downk.cc/item/5eb4bac6c2a9a83be51b0d09.png)

启动云枢项目即可看到

## F&Q

#### 是否可替换或扩展云枢原生控件?

**基础控件**可以替换. 在组件 info 中将 type 设置成和基础组件一样,并设置`force:true`.

#### 为什么只能替换基础组件

**布局控件**是和运行时的平台有关联.

**系统控件**是默认和后端有关联.

**高级控件**和其他模型有关联.

#### 为啥是 conduct 而不是 setting

表单组件开发规范和页面设计器组件开发规范是不一样. 避免一致的映像.

#### 为什么运行时的组件需要继承 `base-controls.ts`

在运行时,云枢每个组件会被构建成单独的`controller`,这些`controller`会有相同的能力,而这些能力是通过`base-controls`提供.

#### 为什么组件需要有 type 和 formControllerType 两个唯一的标识

云枢组件可以被视为一个 DOM 标签和被视为具有特殊能力的 controller. **type**是在组件挂载 DOM 的时候做为唯一的表示.而 **formControllerType** 是作为组件库中唯一的 controller 标识使用.

组件库中如果替换云枢基础控件**type**是可以重复,但**formControllerType**不能重复.
