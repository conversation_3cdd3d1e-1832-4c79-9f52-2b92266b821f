{"name": "cloudpivot-admin", "version": "6.15.15", "private": true, "description": "云枢admin端项目", "scripts": {"serve": "npm run install-fix && vue-cli-service serve", "build": "npm run install-fix && npm run build-prod", "test:unit": "vue-cli-service test:unit", "lint": "vue-cli-service lint", "analyze": "vue-cli-service build --report", "build-dev": "vue-cli-service build --mode development", "build-prod": "rimraf node_modules/.cache && cross-env node --max_old_space_size=8192 ../../node_modules/@vue/cli-service/bin/vue-cli-service.js build", "dll": "webpack -p --progress --config ./webpack.dll.conf.js", "local": "rimraf node_modules/.cache && cross-env node --max_old_space_size=8192 node_modules/@vue/cli-service/bin/vue-cli-service.js serve --mode local", "test": "vue-cli-service test:unit", "install-fix": "cd ../../bin && node ./install-fix.js", "increase-memory-limit": "increase-memory-limit"}, "dependencies": {"canvg": "1.5.3", "cloudpivot": "6.15.11", "cloudpivot-admin-core": "6.15.13", "cloudpivot-designer": "6.15.1", "cloudpivot-flow": "6.15.8", "cloudpivot-form": "6.15.15", "cloudpivot-forms": "6.15.7", "cloudpivot-icons": "6.15.1", "cloudpivot-kindeditor": "6.15.0", "cloudpivot-list": "6.15.13", "cloudpivot-platform": "6.15.0", "cloudpivot-portal-designer": "6.15.9", "cloudpivot-web-ide": "6.15.4", "html2canvas": "1.0.0-rc.3", "monaco-editor": "0.44.0"}, "devDependencies": {"@babel/core": "^7.9.0", "@babel/preset-env": "^7.8.4", "@vue/cli-plugin-babel": "~5.0.0-alpha.7", "@vue/cli-plugin-eslint": "~5.0.0-alpha.7", "@vue/cli-plugin-router": "~5.0.0-alpha.7", "@vue/cli-plugin-typescript": "~5.0.0-alpha.7", "@vue/cli-plugin-vuex": "~5.0.0-alpha.7", "@vue/cli-service": "~5.0.0-alpha.7", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^7.0.0", "babel-core": "7.0.0-bridge.0", "babel-eslint": "^10.0.3", "babel-plugin-import": "^1.13.1", "babel-plugin-syntax-dynamic-import": "^6.18.0", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-vue-jsx": "^3.7.0", "eslint": "^6.7.2", "eslint-plugin-vue": "^6.2.2", "increase-memory-limit": "^1.0.7", "less": "^3.0.4", "less-loader": "^5.0.0", "monaco-editor-webpack-plugin": "7.1.0", "typescript": "~4.1.5", "uuidjs": "^4.2.8"}}