let host = `${window.location.protocol}//${window.location.host}`;
let oauthHost = `${window.location.protocol}//${window.location.host}`;
const marketPortalHost = 'https://market.internal.cloudpivot.cn';

window.config = {
  oauthHost: `${oauthHost}/api`,
  redirectHost: `${host}/admin`,
  client_id: 'api',
  scope: 'read',
  secret: '',
  apiHost: `${host}/api`,
  portalHost: host,
  marketPortalHost: marketPortalHost,
  marketPortalUrl: `${marketPortalHost}/#/market/dashboard`,
  marketPortalSwitch: true,
  changeControlType: true, // 控件发布前，在表单设计中允许修改控件类型
  //表单设计或视图设计中，html展示模式【'edit' | 'readonly' | 'hidden'】
  // edit：可编辑， readonly: 只读， hidden：不可见
  htmlEdit: {
    listDesign: 'edit',
    formDesign: 'edit',
  },
  pageMourningColor: false, // 页面开启哀悼色
};
