{"name": "fronted", "version": "6.15.15", "private": true, "description": "云枢6.15多包项目工程", "scripts": {"cz": "yarn run addNotes && npx git-cz", "dev:mobile": "cd packages/mobile && yarn run serve", "dev:portal": "cd packages/portal && yarn run serve", "dev:admin": "cd packages/admin && yarn run serve", "build:admin": "cd packages/admin && yarn run build", "build:portal": "cd packages/portal && yarn run build", "build:mobile": "cd packages/mobile && yarn run build", "build:mobile-analyze": "cd packages/mobile && yarn run analyze", "build:portal-analyze": "cd packages/portal && yarn run analyze", "build:admin-analyze": "cd packages/admin && yarn run analyze", "build:forms": "cd packages/cloudpivot-forms && yarn run build", "clean": "cd bin && node clean.js", "addNotes": "cd bin && node addNotes.js", "build:all": "./bin/build.sh", "changePath": "cd bin && node changePath.js", "install-fix": "cd bin && node ./install-fix.js"}, "dependencies": {"@h3/antd-vue": "1.6.3", "@h3/awesome-ui": "3.1.34", "@h3/report": "5.3.12-alpha.7", "@h3/report-mobile": "5.3.12-alpha.7", "@h3/theme-pc": "3.1.22", "@h3/thinking-ui": "^1.2.3", "@h3print/designer": "2.7.21", "@h3print/excellit": "2.7.21", "@h3print/runtime": "2.7.21", "animate.css": "3.7.2", "ansi-html": "0.0.9", "babel-polyfill": "6.26.0", "codemirror": "5.49.2", "compressorjs": "1.0.7", "core-js": "^3.20.2", "decimal.js-light": "2.5.1", "dingtalk-jsapi": "^2.7.6", "fastclick": "1.0.6", "gdt-jsapi": "1.9.41", "h3-antd-vue": "1.1.50", "h3-flow-drawer": "^0.1.17", "h3-mobile-vue": "0.2.156", "js-base64": "2.5.1", "jsencrypt": "3.0.0-rc.1", "lodash": "^4.17.20", "mescroll.js": "1.3.2", "moment": "^2.26.0", "number-precision": "^1.5.1", "qs": "6.7.0", "recast": "^0.20.4", "tinymce": "5.1.2", "vue": "2.7.14", "vue-amap": "0.5.9", "vue-class-component": "^7.2.3", "vue-i18n": "8.11.2", "vue-property-decorator": "^9.1.2", "vue-router": "3.2.0", "vuedraggable": "^2.23.2", "vuex": "^3.4.0", "vuex-class": "0.3.2", "vuex-persist": "2.2.0", "webpack-theme-color-replacer": "^1.3.26", "wx-jsapi": "0.0.1"}, "devDependencies": {"@babel/core": "^7.9.0", "@commitlint/cli": "^8.3.5", "@commitlint/config-conventional": "^11.0.0", "@types/html2canvas": "0.0.35", "@types/js-base64": "2.3.1", "@types/lodash": "^4.14.168", "@types/resize-observer-browser": "0.1.7", "@types/tinymce": "4.5.23", "@types/vue": "2.0.0", "@typescript-eslint/eslint-plugin": "^4.18.0", "@typescript-eslint/parser": "^4.18.0", "@vue/cli-plugin-babel": "~5.0.0-alpha.7", "@vue/cli-plugin-eslint": "~5.0.0-alpha.7", "@vue/cli-plugin-router": "~5.0.0-alpha.7", "@vue/cli-plugin-typescript": "~5.0.0-alpha.7", "@vue/cli-plugin-vuex": "~5.0.0-alpha.7", "@vue/cli-service": "~5.0.0-alpha.7", "@vue/eslint-config-prettier": "^6.0.0", "@vue/eslint-config-typescript": "^7.0.0", "axios": "^0.21.1", "babel-eslint": "^10.0.3", "babel-loader": "^8.1.0", "babel-plugin-import": "^1.13.1", "commitizen": "^4.0.4", "cross-env": "^7.0.3", "crypto-js": "3.2.1", "cz-customizable": "^6.3.0", "eslint": "^6.7.2", "eslint-config-standard": "^14.1.0", "eslint-plugin-import": "^2.19.1", "eslint-plugin-node": "^10.0.0", "eslint-plugin-prettier": "^3.1.3", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-react": "^7.22.0", "eslint-plugin-standard": "^4.0.1", "eslint-plugin-vue": "^6.2.2", "gulp": "^4.0.2", "gulp-babel": "^8.0.0", "gulp-clean": "^0.4.0", "husky": "^4.3.8", "lerna": "^3.22.1", "less": "^3.0.4", "less-loader": "^5.0.0", "lint-staged": "^10.5.4", "prettier": "^2.2.1", "rimraf": "^3.0.2", "speed-measure-webpack-plugin": "^1.5.0", "stylelint": "^13.3.3", "stylelint-config-prettier": "^8.0.1", "stylelint-config-standard": "^20.0.0", "stylelint-prettier": "^1.1.2", "ts-node": "^10.0.0", "typescript": "~4.1.5", "vue-eslint-parser": "^7.0.0", "webpack": "^5.84.1", "webpack-cli": "5.1.1", "@types/copy-webpack-plugin": "10.1.0", "copy-webpack-plugin": "10.1.0", "vue-template-compiler": "2.7.14"}, "lint-staged": {"packages/**/*.{js,vue,ts,tsx,jsx}": ["prettier --write", "eslint --fix"]}, "husky": {"hooks": {"pre-commit": ["lint-staged"], "commit-msg": "commitlint -E HUSKY_GIT_PARAMS"}}, "config": {"commitizen": {"path": "cz-customizable"}, "cz-customizable": {"config": "./config/cz-customizable-config-standard.js"}}, "workspaces": ["packages/*", "packages/builtin/*", "packages/plugin/*"]}