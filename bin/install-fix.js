/**
 * 升级webpack5后，部分插件未升级支持webpack5，需要hack
 */
const fs = require('fs');

// 替换recast - request('os').EOL
function fixRecast () {
  try {
    const file = './../node_modules/recast/lib/options.js';
    let fileContent = fs.readFileSync(file);
    fileContent = fileContent.toString();
    fileContent = fileContent.replace(/require\("os"\)\.EOL \|\|/g, '');
    fs.writeFileSync(file, fileContent);
  } catch (e) {}
}

function fixDist () {
  try {
    const file = './../node_modules/@h3/thinking-ui/dist/index.css';
    let fileContent = fs.readFileSync(file);
    fileContent = fileContent.toString();
    fileContent = fileContent.replace(/\/dist\/iconfont\//g, './iconfont/');
    fs.writeFileSync(file, fileContent);
  } catch (e) {}
}
fixRecast();

fixDist();
