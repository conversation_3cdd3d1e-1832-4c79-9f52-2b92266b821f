// 用于修改包自己引用自己本身文件，不使用相对路径，使用node_modules包的问题
var fs = require("fs");

const path = require('path');

// 不需要检查的文件夹
const unChangeFolder = [
  'node_modules'
];

// 需要检查修改引入文件路径的文件类型
const wantedChangeFile = ['.vue', '.ts', '.js'];

const npms = {
  'cloudpivot-form': {
    path: './../packages/cloudpivot-form',
    regex: /from \'cloudpivot-form\/.*?\'/s
  },
  'cloudpivot': {
    path: './../packages/cloudpivot',
    regex: /from \'cloudpivot\/.*?\'/s
  },
  'cloudpivot-admin-core': {
    path: './../packages/cloudpivot-admin-core',
    regex: /from \'cloudpivot-admin-core\/.*?\'/s
  },
  'cloudpivot-designer': {
    path: './../packages/cloudpivot-designer',
    regex: /from \'cloudpivot-designer\/.*?\'/s
  },
  'cloudpivot-flow': {
    path: './../packages/cloudpivot-flow',
    regex: /from \'cloudpivot-flow\/.*?\'/s
  },
  'cloudpivot-form-extend': {
    path: './../packages/cloudpivot-form-extend',
    regex: /from \'cloudpivot-form-extend\/.*?\'/s
  },
  'cloudpivot-forms': {
    path: './../packages/cloudpivot-forms',
    regex: /from \'cloudpivot-forms\/.*?\'/s
  },
  'cloudpivot-list': {
    path: './../packages/cloudpivot-list',
    regex: /from \'cloudpivot-list\/.*?\'/s
  },
  'cloudpivot-portal-designer': {
    path: './../packages/cloudpivot-portal-designer',
    regex: /from \'cloudpivot-portal-designer\/.*?\'/s
  },
}

Object.keys(npms).forEach((key) => {
  const options = npms[key];
  changePath(options.path, options.regex)
})


function changePath(filePath, regex) {
  //遍历test文件夹
  fs.readdir(filePath + "", function (err, files) {
    if (err) {
      return err;
    }
    if (files.length !== 0) {
      files.forEach((item) => {
        const path = filePath + "/" + item;
        //判断文件的状态，用于区分文件名/文件夹
        fs.stat(path, function (err, status) {
          if (err) {
            return err;
          }
          const isFile = status.isFile(); //是文件
          const isDir = status.isDirectory(); //是文件夹
          
          // 文件类型 && 需要添加注释
          if (isFile && wantedChangeFile.some(el => new RegExp(`\.${el}$`).test(item))) {
            replaceFile(path, regex);
          }

          // 文件夹类型 && 需要递归
          if (isDir && !unChangeFolder.includes(item)) {
            changePath(path, regex)
          }
        });
      });
    }
  });
}

// 添加注释
function replaceFile(filePath, regex) {
  fs.readFile(filePath, function (err, data) {
    if (err) {
      return err;
    }
    let str = data.toString();
    let isChange = false;
    const file1 = filePath.replace('./../packages/','');
    str = str.replace(regex, (p0) => {
      const file2 = p0.slice(6, -1);
      let relativePath = path.relative(file1, file2).slice(3).replace(/\\/g, '/');
      isChange = true;
      return `from './${relativePath}'`
    })
    if(isChange){
      fs.writeFileSync(filePath, str, function (err) {
        if (err) {
          return err;
        }
      });
      console.log("修改文件路径成功==>>:" + filePath);
    }
  });
};